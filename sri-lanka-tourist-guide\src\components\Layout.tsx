import React from 'react'
import Header from './Header'
import Footer from './Footer'
import ScrollProgress from './ScrollProgress'
import FloatingActionButton from './FloatingActionButton'
import SkipToContent from './SkipToContent'

interface LayoutProps {
  children: React.ReactNode
}

const Layout: React.FC<LayoutProps> = ({ children }) => {
  return (
    <div className="min-h-screen flex flex-col">
      <SkipToContent />
      <ScrollProgress />
      <Header />
      <main id="main" className="flex-grow" tabIndex={-1}>
        {children}
      </main>
      <Footer />
      <FloatingActionButton />
    </div>
  )
}

export default Layout
