import { chromium, FullConfig } from '@playwright/test'

async function globalSetup(config: FullConfig) {
  console.log('🚀 Starting global setup...')
  
  // Launch browser for setup
  const browser = await chromium.launch()
  const page = await browser.newPage()
  
  try {
    // Wait for the dev server to be ready
    const baseURL = config.projects[0].use.baseURL || 'http://localhost:5173'
    console.log(`📡 Checking if dev server is ready at ${baseURL}`)
    
    // Try to access the homepage
    await page.goto(baseURL, { waitUntil: 'networkidle' })
    
    // Check if the page loaded successfully
    const title = await page.title()
    console.log(`✅ Dev server is ready. Page title: ${title}`)
    
    // Perform any global setup tasks here
    // For example, you could:
    // - Seed test data
    // - Set up authentication
    // - Clear caches
    // - Initialize test environment
    
    console.log('🔧 Performing global setup tasks...')
    
    // Clear localStorage and sessionStorage
    await page.evaluate(() => {
      localStorage.clear()
      sessionStorage.clear()
    })
    
    // Check if critical resources are loading
    const criticalResources = [
      '/src/main.tsx',
      '/src/App.tsx'
    ]
    
    for (const resource of criticalResources) {
      try {
        const response = await page.goto(`${baseURL}${resource}`)
        if (response && response.status() >= 400) {
          console.warn(`⚠️ Warning: Resource ${resource} returned status ${response.status()}`)
        }
      } catch (error) {
        console.warn(`⚠️ Warning: Could not check resource ${resource}:`, error)
      }
    }
    
    console.log('✅ Global setup completed successfully')
    
  } catch (error) {
    console.error('❌ Global setup failed:', error)
    throw error
  } finally {
    await browser.close()
  }
}

export default globalSetup
