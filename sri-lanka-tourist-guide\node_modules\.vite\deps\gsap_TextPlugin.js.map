{"version": 3, "sources": ["../../gsap/utils/strings.js", "../../gsap/TextPlugin.js"], "sourcesContent": ["/*!\n * strings: 3.13.0\n * https://gsap.com\n *\n * Copyright 2008-2025, GreenSock. All rights reserved.\n * Subject to the terms at https://gsap.com/standard-license\n * @author: <PERSON>, <EMAIL>\n*/\n\n/* eslint-disable */\nvar _trimExp = /(?:^\\s+|\\s+$)/g;\nexport var emojiExp = /([\\uD800-\\uDBFF][\\uDC00-\\uDFFF](?:[\\u200D\\uFE0F][\\uD800-\\uDBFF][\\uDC00-\\uDFFF]){2,}|\\uD83D\\uDC69(?:\\u200D(?:(?:\\uD83D\\uDC69\\u200D)?\\uD83D\\uDC67|(?:\\uD83D\\uDC69\\u200D)?\\uD83D\\uDC66)|\\uD83C[\\uDFFB-\\uDFFF])|\\uD83D\\uDC69\\u200D(?:\\uD83D\\uDC69\\u200D)?\\uD83D\\uDC66\\u200D\\uD83D\\uDC66|\\uD83D\\uDC69\\u200D(?:\\uD83D\\uDC69\\u200D)?\\uD83D\\uDC67\\u200D(?:\\uD83D[\\uDC66\\uDC67])|\\uD83C\\uDFF3\\uFE0F\\u200D\\uD83C\\uDF08|(?:\\uD83C[\\uDFC3\\uDFC4\\uDFCA]|\\uD83D[\\uDC6E\\uDC71\\uDC73\\uDC77\\uDC81\\uDC82\\uDC86\\uDC87\\uDE45-\\uDE47\\uDE4B\\uDE4D\\uDE4E\\uDEA3\\uDEB4-\\uDEB6]|\\uD83E[\\uDD26\\uDD37-\\uDD39\\uDD3D\\uDD3E\\uDDD6-\\uDDDD])(?:\\uD83C[\\uDFFB-\\uDFFF])\\u200D[\\u2642\\u2640]\\uFE0F|\\uD83D\\uDC69(?:\\uD83C[\\uDFFB-\\uDFFF])\\u200D(?:\\uD83C[\\uDF3E\\uDF73\\uDF93\\uDFA4\\uDFA8\\uDFEB\\uDFED]|\\uD83D[\\uDCBB\\uDD27\\uDCBC\\uDD2C\\uDE80\\uDE92])|(?:\\uD83C[\\uDFC3\\uDFC4\\uDFCA]|\\uD83D[\\uDC6E\\uDC6F\\uDC71\\uDC73\\uDC77\\uDC81\\uDC82\\uDC86\\uDC87\\uDE45-\\uDE47\\uDE4B\\uDE4D\\uDE4E\\uDEA3\\uDEB4-\\uDEB6]|\\uD83E[\\uDD26\\uDD37-\\uDD39\\uDD3C-\\uDD3E\\uDDD6-\\uDDDF])\\u200D[\\u2640\\u2642]\\uFE0F|\\uD83C\\uDDFD\\uD83C\\uDDF0|\\uD83C\\uDDF6\\uD83C\\uDDE6|\\uD83C\\uDDF4\\uD83C\\uDDF2|\\uD83C\\uDDE9(?:\\uD83C[\\uDDEA\\uDDEC\\uDDEF\\uDDF0\\uDDF2\\uDDF4\\uDDFF])|\\uD83C\\uDDF7(?:\\uD83C[\\uDDEA\\uDDF4\\uDDF8\\uDDFA\\uDDFC])|\\uD83C\\uDDE8(?:\\uD83C[\\uDDE6\\uDDE8\\uDDE9\\uDDEB-\\uDDEE\\uDDF0-\\uDDF5\\uDDF7\\uDDFA-\\uDDFF])|(?:\\u26F9|\\uD83C[\\uDFCC\\uDFCB]|\\uD83D\\uDD75)(?:\\uFE0F\\u200D[\\u2640\\u2642]|(?:\\uD83C[\\uDFFB-\\uDFFF])\\u200D[\\u2640\\u2642])\\uFE0F|(?:\\uD83D\\uDC41\\uFE0F\\u200D\\uD83D\\uDDE8|\\uD83D\\uDC69(?:\\uD83C[\\uDFFB-\\uDFFF])\\u200D[\\u2695\\u2696\\u2708]|\\uD83D\\uDC69\\u200D[\\u2695\\u2696\\u2708]|\\uD83D\\uDC68(?:(?:\\uD83C[\\uDFFB-\\uDFFF])\\u200D[\\u2695\\u2696\\u2708]|\\u200D[\\u2695\\u2696\\u2708]))\\uFE0F|\\uD83C\\uDDF2(?:\\uD83C[\\uDDE6\\uDDE8-\\uDDED\\uDDF0-\\uDDFF])|\\uD83D\\uDC69\\u200D(?:\\uD83C[\\uDF3E\\uDF73\\uDF93\\uDFA4\\uDFA8\\uDFEB\\uDFED]|\\uD83D[\\uDCBB\\uDCBC\\uDD27\\uDD2C\\uDE80\\uDE92]|\\u2764\\uFE0F\\u200D(?:\\uD83D\\uDC8B\\u200D(?:\\uD83D[\\uDC68\\uDC69])|\\uD83D[\\uDC68\\uDC69]))|\\uD83C\\uDDF1(?:\\uD83C[\\uDDE6-\\uDDE8\\uDDEE\\uDDF0\\uDDF7-\\uDDFB\\uDDFE])|\\uD83C\\uDDEF(?:\\uD83C[\\uDDEA\\uDDF2\\uDDF4\\uDDF5])|\\uD83C\\uDDED(?:\\uD83C[\\uDDF0\\uDDF2\\uDDF3\\uDDF7\\uDDF9\\uDDFA])|\\uD83C\\uDDEB(?:\\uD83C[\\uDDEE-\\uDDF0\\uDDF2\\uDDF4\\uDDF7])|[#\\*0-9]\\uFE0F\\u20E3|\\uD83C\\uDDE7(?:\\uD83C[\\uDDE6\\uDDE7\\uDDE9-\\uDDEF\\uDDF1-\\uDDF4\\uDDF6-\\uDDF9\\uDDFB\\uDDFC\\uDDFE\\uDDFF])|\\uD83C\\uDDE6(?:\\uD83C[\\uDDE8-\\uDDEC\\uDDEE\\uDDF1\\uDDF2\\uDDF4\\uDDF6-\\uDDFA\\uDDFC\\uDDFD\\uDDFF])|\\uD83C\\uDDFF(?:\\uD83C[\\uDDE6\\uDDF2\\uDDFC])|\\uD83C\\uDDF5(?:\\uD83C[\\uDDE6\\uDDEA-\\uDDED\\uDDF0-\\uDDF3\\uDDF7-\\uDDF9\\uDDFC\\uDDFE])|\\uD83C\\uDDFB(?:\\uD83C[\\uDDE6\\uDDE8\\uDDEA\\uDDEC\\uDDEE\\uDDF3\\uDDFA])|\\uD83C\\uDDF3(?:\\uD83C[\\uDDE6\\uDDE8\\uDDEA-\\uDDEC\\uDDEE\\uDDF1\\uDDF4\\uDDF5\\uDDF7\\uDDFA\\uDDFF])|\\uD83C\\uDFF4\\uDB40\\uDC67\\uDB40\\uDC62(?:\\uDB40\\uDC77\\uDB40\\uDC6C\\uDB40\\uDC73|\\uDB40\\uDC73\\uDB40\\uDC63\\uDB40\\uDC74|\\uDB40\\uDC65\\uDB40\\uDC6E\\uDB40\\uDC67)\\uDB40\\uDC7F|\\uD83D\\uDC68(?:\\u200D(?:\\u2764\\uFE0F\\u200D(?:\\uD83D\\uDC8B\\u200D)?\\uD83D\\uDC68|(?:(?:\\uD83D[\\uDC68\\uDC69])\\u200D)?\\uD83D\\uDC66\\u200D\\uD83D\\uDC66|(?:(?:\\uD83D[\\uDC68\\uDC69])\\u200D)?\\uD83D\\uDC67\\u200D(?:\\uD83D[\\uDC66\\uDC67])|\\uD83C[\\uDF3E\\uDF73\\uDF93\\uDFA4\\uDFA8\\uDFEB\\uDFED]|\\uD83D[\\uDCBB\\uDCBC\\uDD27\\uDD2C\\uDE80\\uDE92])|(?:\\uD83C[\\uDFFB-\\uDFFF])\\u200D(?:\\uD83C[\\uDF3E\\uDF73\\uDF93\\uDFA4\\uDFA8\\uDFEB\\uDFED]|\\uD83D[\\uDCBB\\uDCBC\\uDD27\\uDD2C\\uDE80\\uDE92]))|\\uD83C\\uDDF8(?:\\uD83C[\\uDDE6-\\uDDEA\\uDDEC-\\uDDF4\\uDDF7-\\uDDF9\\uDDFB\\uDDFD-\\uDDFF])|\\uD83C\\uDDF0(?:\\uD83C[\\uDDEA\\uDDEC-\\uDDEE\\uDDF2\\uDDF3\\uDDF5\\uDDF7\\uDDFC\\uDDFE\\uDDFF])|\\uD83C\\uDDFE(?:\\uD83C[\\uDDEA\\uDDF9])|\\uD83C\\uDDEE(?:\\uD83C[\\uDDE8-\\uDDEA\\uDDF1-\\uDDF4\\uDDF6-\\uDDF9])|\\uD83C\\uDDF9(?:\\uD83C[\\uDDE6\\uDDE8\\uDDE9\\uDDEB-\\uDDED\\uDDEF-\\uDDF4\\uDDF7\\uDDF9\\uDDFB\\uDDFC\\uDDFF])|\\uD83C\\uDDEC(?:\\uD83C[\\uDDE6\\uDDE7\\uDDE9-\\uDDEE\\uDDF1-\\uDDF3\\uDDF5-\\uDDFA\\uDDFC\\uDDFE])|\\uD83C\\uDDFA(?:\\uD83C[\\uDDE6\\uDDEC\\uDDF2\\uDDF3\\uDDF8\\uDDFE\\uDDFF])|\\uD83C\\uDDEA(?:\\uD83C[\\uDDE6\\uDDE8\\uDDEA\\uDDEC\\uDDED\\uDDF7-\\uDDFA])|\\uD83C\\uDDFC(?:\\uD83C[\\uDDEB\\uDDF8])|(?:\\u26F9|\\uD83C[\\uDFCB\\uDFCC]|\\uD83D\\uDD75)(?:\\uD83C[\\uDFFB-\\uDFFF])|(?:\\uD83C[\\uDFC3\\uDFC4\\uDFCA]|\\uD83D[\\uDC6E\\uDC71\\uDC73\\uDC77\\uDC81\\uDC82\\uDC86\\uDC87\\uDE45-\\uDE47\\uDE4B\\uDE4D\\uDE4E\\uDEA3\\uDEB4-\\uDEB6]|\\uD83E[\\uDD26\\uDD37-\\uDD39\\uDD3D\\uDD3E\\uDDD6-\\uDDDD])(?:\\uD83C[\\uDFFB-\\uDFFF])|(?:[\\u261D\\u270A-\\u270D]|\\uD83C[\\uDF85\\uDFC2\\uDFC7]|\\uD83D[\\uDC42\\uDC43\\uDC46-\\uDC50\\uDC66\\uDC67\\uDC70\\uDC72\\uDC74-\\uDC76\\uDC78\\uDC7C\\uDC83\\uDC85\\uDCAA\\uDD74\\uDD7A\\uDD90\\uDD95\\uDD96\\uDE4C\\uDE4F\\uDEC0\\uDECC]|\\uD83E[\\uDD18-\\uDD1C\\uDD1E\\uDD1F\\uDD30-\\uDD36\\uDDD1-\\uDDD5])(?:\\uD83C[\\uDFFB-\\uDFFF])|\\uD83D\\uDC68(?:\\u200D(?:(?:(?:\\uD83D[\\uDC68\\uDC69])\\u200D)?\\uD83D\\uDC67|(?:(?:\\uD83D[\\uDC68\\uDC69])\\u200D)?\\uD83D\\uDC66)|\\uD83C[\\uDFFB-\\uDFFF])|(?:[\\u261D\\u26F9\\u270A-\\u270D]|\\uD83C[\\uDF85\\uDFC2-\\uDFC4\\uDFC7\\uDFCA-\\uDFCC]|\\uD83D[\\uDC42\\uDC43\\uDC46-\\uDC50\\uDC66-\\uDC69\\uDC6E\\uDC70-\\uDC78\\uDC7C\\uDC81-\\uDC83\\uDC85-\\uDC87\\uDCAA\\uDD74\\uDD75\\uDD7A\\uDD90\\uDD95\\uDD96\\uDE45-\\uDE47\\uDE4B-\\uDE4F\\uDEA3\\uDEB4-\\uDEB6\\uDEC0\\uDECC]|\\uD83E[\\uDD18-\\uDD1C\\uDD1E\\uDD1F\\uDD26\\uDD30-\\uDD39\\uDD3D\\uDD3E\\uDDD1-\\uDDDD])(?:\\uD83C[\\uDFFB-\\uDFFF])?|(?:[\\u231A\\u231B\\u23E9-\\u23EC\\u23F0\\u23F3\\u25FD\\u25FE\\u2614\\u2615\\u2648-\\u2653\\u267F\\u2693\\u26A1\\u26AA\\u26AB\\u26BD\\u26BE\\u26C4\\u26C5\\u26CE\\u26D4\\u26EA\\u26F2\\u26F3\\u26F5\\u26FA\\u26FD\\u2705\\u270A\\u270B\\u2728\\u274C\\u274E\\u2753-\\u2755\\u2757\\u2795-\\u2797\\u27B0\\u27BF\\u2B1B\\u2B1C\\u2B50\\u2B55]|\\uD83C[\\uDC04\\uDCCF\\uDD8E\\uDD91-\\uDD9A\\uDDE6-\\uDDFF\\uDE01\\uDE1A\\uDE2F\\uDE32-\\uDE36\\uDE38-\\uDE3A\\uDE50\\uDE51\\uDF00-\\uDF20\\uDF2D-\\uDF35\\uDF37-\\uDF7C\\uDF7E-\\uDF93\\uDFA0-\\uDFCA\\uDFCF-\\uDFD3\\uDFE0-\\uDFF0\\uDFF4\\uDFF8-\\uDFFF]|\\uD83D[\\uDC00-\\uDC3E\\uDC40\\uDC42-\\uDCFC\\uDCFF-\\uDD3D\\uDD4B-\\uDD4E\\uDD50-\\uDD67\\uDD7A\\uDD95\\uDD96\\uDDA4\\uDDFB-\\uDE4F\\uDE80-\\uDEC5\\uDECC\\uDED0-\\uDED2\\uDEEB\\uDEEC\\uDEF4-\\uDEF8]|\\uD83E[\\uDD10-\\uDD3A\\uDD3C-\\uDD3E\\uDD40-\\uDD45\\uDD47-\\uDD4C\\uDD50-\\uDD6B\\uDD80-\\uDD97\\uDDC0\\uDDD0-\\uDDE6])|(?:[#\\*0-9\\xA9\\xAE\\u203C\\u2049\\u2122\\u2139\\u2194-\\u2199\\u21A9\\u21AA\\u231A\\u231B\\u2328\\u23CF\\u23E9-\\u23F3\\u23F8-\\u23FA\\u24C2\\u25AA\\u25AB\\u25B6\\u25C0\\u25FB-\\u25FE\\u2600-\\u2604\\u260E\\u2611\\u2614\\u2615\\u2618\\u261D\\u2620\\u2622\\u2623\\u2626\\u262A\\u262E\\u262F\\u2638-\\u263A\\u2640\\u2642\\u2648-\\u2653\\u2660\\u2663\\u2665\\u2666\\u2668\\u267B\\u267F\\u2692-\\u2697\\u2699\\u269B\\u269C\\u26A0\\u26A1\\u26AA\\u26AB\\u26B0\\u26B1\\u26BD\\u26BE\\u26C4\\u26C5\\u26C8\\u26CE\\u26CF\\u26D1\\u26D3\\u26D4\\u26E9\\u26EA\\u26F0-\\u26F5\\u26F7-\\u26FA\\u26FD\\u2702\\u2705\\u2708-\\u270D\\u270F\\u2712\\u2714\\u2716\\u271D\\u2721\\u2728\\u2733\\u2734\\u2744\\u2747\\u274C\\u274E\\u2753-\\u2755\\u2757\\u2763\\u2764\\u2795-\\u2797\\u27A1\\u27B0\\u27BF\\u2934\\u2935\\u2B05-\\u2B07\\u2B1B\\u2B1C\\u2B50\\u2B55\\u3030\\u303D\\u3297\\u3299]|\\uD83C[\\uDC04\\uDCCF\\uDD70\\uDD71\\uDD7E\\uDD7F\\uDD8E\\uDD91-\\uDD9A\\uDDE6-\\uDDFF\\uDE01\\uDE02\\uDE1A\\uDE2F\\uDE32-\\uDE3A\\uDE50\\uDE51\\uDF00-\\uDF21\\uDF24-\\uDF93\\uDF96\\uDF97\\uDF99-\\uDF9B\\uDF9E-\\uDFF0\\uDFF3-\\uDFF5\\uDFF7-\\uDFFF]|\\uD83D[\\uDC00-\\uDCFD\\uDCFF-\\uDD3D\\uDD49-\\uDD4E\\uDD50-\\uDD67\\uDD6F\\uDD70\\uDD73-\\uDD7A\\uDD87\\uDD8A-\\uDD8D\\uDD90\\uDD95\\uDD96\\uDDA4\\uDDA5\\uDDA8\\uDDB1\\uDDB2\\uDDBC\\uDDC2-\\uDDC4\\uDDD1-\\uDDD3\\uDDDC-\\uDDDE\\uDDE1\\uDDE3\\uDDE8\\uDDEF\\uDDF3\\uDDFA-\\uDE4F\\uDE80-\\uDEC5\\uDECB-\\uDED2\\uDEE0-\\uDEE5\\uDEE9\\uDEEB\\uDEEC\\uDEF0\\uDEF3-\\uDEF8]|\\uD83E[\\uDD10-\\uDD3A\\uDD3C-\\uDD3E\\uDD40-\\uDD45\\uDD47-\\uDD4C\\uDD50-\\uDD6B\\uDD80-\\uDD97\\uDDC0\\uDDD0-\\uDDE6])\\uFE0F)/;\nexport function getText(e) {\n  var type = e.nodeType,\n      result = \"\";\n\n  if (type === 1 || type === 9 || type === 11) {\n    if (typeof e.textContent === \"string\") {\n      return e.textContent;\n    } else {\n      for (e = e.firstChild; e; e = e.nextSibling) {\n        result += getText(e);\n      }\n    }\n  } else if (type === 3 || type === 4) {\n    return e.nodeValue;\n  }\n\n  return result;\n}\nexport function splitInnerHTML(element, delimiter, trim, preserveSpaces, unescapedCharCodes) {\n  var node = element.firstChild,\n      result = [],\n      s;\n\n  while (node) {\n    if (node.nodeType === 3) {\n      s = (node.nodeValue + \"\").replace(/^\\n+/g, \"\");\n\n      if (!preserveSpaces) {\n        s = s.replace(/\\s+/g, \" \");\n      }\n\n      result.push.apply(result, emojiSafeSplit(s, delimiter, trim, preserveSpaces, unescapedCharCodes));\n    } else if ((node.nodeName + \"\").toLowerCase() === \"br\") {\n      result[result.length - 1] += \"<br>\";\n    } else {\n      result.push(node.outerHTML);\n    }\n\n    node = node.nextSibling;\n  }\n\n  if (!unescapedCharCodes) {\n    s = result.length;\n\n    while (s--) {\n      result[s] === \"&\" && result.splice(s, 1, \"&amp;\");\n    }\n  }\n\n  return result;\n}\n/*\n//smaller kb version that only handles the simpler emoji's, which is often perfectly adequate.\n\nlet _emoji = \"[\\uE000-\\uF8FF]|\\uD83C[\\uDC00-\\uDFFF]|\\uD83D[\\uDC00-\\uDFFF]|[\\u2694-\\u2697]|\\uD83E[\\uDD10-\\uDD5D]|[\\uD800-\\uDBFF][\\uDC00-\\uDFFF]\",\n\t_emojiExp = new RegExp(_emoji),\n\t_emojiAndCharsExp = new RegExp(_emoji + \"|.\", \"g\"),\n\t_emojiSafeSplit = (text, delimiter, trim) => {\n\t\tif (trim) {\n\t\t\ttext = text.replace(_trimExp, \"\");\n\t\t}\n\t\treturn ((delimiter === \"\" || !delimiter) && _emojiExp.test(text)) ? text.match(_emojiAndCharsExp) : text.split(delimiter || \"\");\n\t};\n */\n\nexport function emojiSafeSplit(text, delimiter, trim, preserveSpaces, unescapedCharCodes) {\n  text += \"\"; // make sure it's cast as a string. Someone may pass in a number.\n\n  trim && (text = text.trim ? text.trim() : text.replace(_trimExp, \"\")); // IE9 and earlier compatibility\n\n  if (delimiter && delimiter !== \"\") {\n    return text.replace(/>/g, \"&gt;\").replace(/</g, \"&lt;\").split(delimiter);\n  }\n\n  var result = [],\n      l = text.length,\n      i = 0,\n      j,\n      character;\n\n  for (; i < l; i++) {\n    character = text.charAt(i);\n\n    if (character.charCodeAt(0) >= 0xD800 && character.charCodeAt(0) <= 0xDBFF || text.charCodeAt(i + 1) >= 0xFE00 && text.charCodeAt(i + 1) <= 0xFE0F) {\n      //special emoji characters use 2 or 4 unicode characters that we must keep together.\n      j = ((text.substr(i, 12).split(emojiExp) || [])[1] || \"\").length || 2;\n      character = text.substr(i, j);\n      result.emoji = 1;\n      i += j - 1;\n    }\n\n    result.push(unescapedCharCodes ? character : character === \">\" ? \"&gt;\" : character === \"<\" ? \"&lt;\" : preserveSpaces && character === \" \" && (text.charAt(i - 1) === \" \" || text.charAt(i + 1) === \" \") ? \"&nbsp;\" : character);\n  }\n\n  return result;\n}", "/*!\n * TextPlugin 3.13.0\n * https://gsap.com\n *\n * @license Copyright 2008-2025, GreenSock. All rights reserved.\n * Subject to the terms at https://gsap.com/standard-license\n * @author: <PERSON>, <EMAIL>\n*/\n\n/* eslint-disable */\nimport { emojiSafeSplit, getText, splitInnerHTML } from \"./utils/strings.js\";\n\nvar gsap,\n    _tempDiv,\n    _getGSAP = function _getGSAP() {\n  return gsap || typeof window !== \"undefined\" && (gsap = window.gsap) && gsap.registerPlugin && gsap;\n};\n\nexport var TextPlugin = {\n  version: \"3.13.0\",\n  name: \"text\",\n  init: function init(target, value, tween) {\n    typeof value !== \"object\" && (value = {\n      value: value\n    });\n\n    var i = target.nodeName.toUpperCase(),\n        data = this,\n        _value = value,\n        newClass = _value.newClass,\n        oldClass = _value.oldClass,\n        preserveSpaces = _value.preserveSpaces,\n        rtl = _value.rtl,\n        delimiter = data.delimiter = value.delimiter || \"\",\n        fillChar = data.fillChar = value.fillChar || (value.padSpace ? \"&nbsp;\" : \"\"),\n        _short,\n        text,\n        original,\n        j,\n        condensedText,\n        condensedOriginal,\n        aggregate,\n        s;\n\n    data.svg = target.getBBox && (i === \"TEXT\" || i === \"TSPAN\");\n\n    if (!(\"innerHTML\" in target) && !data.svg) {\n      return false;\n    }\n\n    data.target = target;\n\n    if (!(\"value\" in value)) {\n      data.text = data.original = [\"\"];\n      return;\n    }\n\n    original = splitInnerHTML(target, delimiter, false, preserveSpaces, data.svg);\n    _tempDiv || (_tempDiv = document.createElement(\"div\"));\n    _tempDiv.innerHTML = value.value;\n    text = splitInnerHTML(_tempDiv, delimiter, false, preserveSpaces, data.svg);\n    data.from = tween._from;\n\n    if ((data.from || rtl) && !(rtl && data.from)) {\n      // right-to-left or \"from()\" tweens should invert things (but if it's BOTH .from() and rtl, inverting twice equals not inverting at all :)\n      i = original;\n      original = text;\n      text = i;\n    }\n\n    data.hasClass = !!(newClass || oldClass);\n    data.newClass = rtl ? oldClass : newClass;\n    data.oldClass = rtl ? newClass : oldClass;\n    i = original.length - text.length;\n    _short = i < 0 ? original : text;\n\n    if (i < 0) {\n      i = -i;\n    }\n\n    while (--i > -1) {\n      _short.push(fillChar);\n    }\n\n    if (value.type === \"diff\") {\n      j = 0;\n      condensedText = [];\n      condensedOriginal = [];\n      aggregate = \"\";\n\n      for (i = 0; i < text.length; i++) {\n        s = text[i];\n\n        if (s === original[i]) {\n          aggregate += s;\n        } else {\n          condensedText[j] = aggregate + s;\n          condensedOriginal[j++] = aggregate + original[i];\n          aggregate = \"\";\n        }\n      }\n\n      text = condensedText;\n      original = condensedOriginal;\n\n      if (aggregate) {\n        text.push(aggregate);\n        original.push(aggregate);\n      }\n    }\n\n    value.speed && tween.duration(Math.min(0.05 / value.speed * _short.length, value.maxDuration || 9999));\n    data.rtl = rtl;\n    data.original = original;\n    data.text = text;\n\n    data._props.push(\"text\");\n  },\n  render: function render(ratio, data) {\n    if (ratio > 1) {\n      ratio = 1;\n    } else if (ratio < 0) {\n      ratio = 0;\n    }\n\n    if (data.from) {\n      ratio = 1 - ratio;\n    }\n\n    var text = data.text,\n        hasClass = data.hasClass,\n        newClass = data.newClass,\n        oldClass = data.oldClass,\n        delimiter = data.delimiter,\n        target = data.target,\n        fillChar = data.fillChar,\n        original = data.original,\n        rtl = data.rtl,\n        l = text.length,\n        i = (rtl ? 1 - ratio : ratio) * l + 0.5 | 0,\n        applyNew,\n        applyOld,\n        str;\n\n    if (hasClass && ratio) {\n      applyNew = newClass && i;\n      applyOld = oldClass && i !== l;\n      str = (applyNew ? \"<span class='\" + newClass + \"'>\" : \"\") + text.slice(0, i).join(delimiter) + (applyNew ? \"</span>\" : \"\") + (applyOld ? \"<span class='\" + oldClass + \"'>\" : \"\") + delimiter + original.slice(i).join(delimiter) + (applyOld ? \"</span>\" : \"\");\n    } else {\n      str = text.slice(0, i).join(delimiter) + delimiter + original.slice(i).join(delimiter);\n    }\n\n    if (data.svg) {\n      //SVG text elements don't have an \"innerHTML\" in Microsoft browsers.\n      target.textContent = str;\n    } else {\n      target.innerHTML = fillChar === \"&nbsp;\" && ~str.indexOf(\"  \") ? str.split(\"  \").join(\"&nbsp;&nbsp;\") : str;\n    }\n  }\n};\nTextPlugin.splitInnerHTML = splitInnerHTML;\nTextPlugin.emojiSafeSplit = emojiSafeSplit;\nTextPlugin.getText = getText;\n_getGSAP() && gsap.registerPlugin(TextPlugin);\nexport { TextPlugin as default };"], "mappings": ";;;AAUA,IAAI,WAAW;AACR,IAAI,WAAW;AACf,SAAS,QAAQ,GAAG;AACzB,MAAI,OAAO,EAAE,UACT,SAAS;AAEb,MAAI,SAAS,KAAK,SAAS,KAAK,SAAS,IAAI;AAC3C,QAAI,OAAO,EAAE,gBAAgB,UAAU;AACrC,aAAO,EAAE;AAAA,IACX,OAAO;AACL,WAAK,IAAI,EAAE,YAAY,GAAG,IAAI,EAAE,aAAa;AAC3C,kBAAU,QAAQ,CAAC;AAAA,MACrB;AAAA,IACF;AAAA,EACF,WAAW,SAAS,KAAK,SAAS,GAAG;AACnC,WAAO,EAAE;AAAA,EACX;AAEA,SAAO;AACT;AACO,SAAS,eAAe,SAAS,WAAW,MAAM,gBAAgB,oBAAoB;AAC3F,MAAI,OAAO,QAAQ,YACf,SAAS,CAAC,GACV;AAEJ,SAAO,MAAM;AACX,QAAI,KAAK,aAAa,GAAG;AACvB,WAAK,KAAK,YAAY,IAAI,QAAQ,SAAS,EAAE;AAE7C,UAAI,CAAC,gBAAgB;AACnB,YAAI,EAAE,QAAQ,QAAQ,GAAG;AAAA,MAC3B;AAEA,aAAO,KAAK,MAAM,QAAQ,eAAe,GAAG,WAAW,MAAM,gBAAgB,kBAAkB,CAAC;AAAA,IAClG,YAAY,KAAK,WAAW,IAAI,YAAY,MAAM,MAAM;AACtD,aAAO,OAAO,SAAS,CAAC,KAAK;AAAA,IAC/B,OAAO;AACL,aAAO,KAAK,KAAK,SAAS;AAAA,IAC5B;AAEA,WAAO,KAAK;AAAA,EACd;AAEA,MAAI,CAAC,oBAAoB;AACvB,QAAI,OAAO;AAEX,WAAO,KAAK;AACV,aAAO,CAAC,MAAM,OAAO,OAAO,OAAO,GAAG,GAAG,OAAO;AAAA,IAClD;AAAA,EACF;AAEA,SAAO;AACT;AAeO,SAAS,eAAe,MAAM,WAAW,MAAM,gBAAgB,oBAAoB;AACxF,UAAQ;AAER,WAAS,OAAO,KAAK,OAAO,KAAK,KAAK,IAAI,KAAK,QAAQ,UAAU,EAAE;AAEnE,MAAI,aAAa,cAAc,IAAI;AACjC,WAAO,KAAK,QAAQ,MAAM,MAAM,EAAE,QAAQ,MAAM,MAAM,EAAE,MAAM,SAAS;AAAA,EACzE;AAEA,MAAI,SAAS,CAAC,GACV,IAAI,KAAK,QACT,IAAI,GACJ,GACA;AAEJ,SAAO,IAAI,GAAG,KAAK;AACjB,gBAAY,KAAK,OAAO,CAAC;AAEzB,QAAI,UAAU,WAAW,CAAC,KAAK,SAAU,UAAU,WAAW,CAAC,KAAK,SAAU,KAAK,WAAW,IAAI,CAAC,KAAK,SAAU,KAAK,WAAW,IAAI,CAAC,KAAK,OAAQ;AAElJ,YAAM,KAAK,OAAO,GAAG,EAAE,EAAE,MAAM,QAAQ,KAAK,CAAC,GAAG,CAAC,KAAK,IAAI,UAAU;AACpE,kBAAY,KAAK,OAAO,GAAG,CAAC;AAC5B,aAAO,QAAQ;AACf,WAAK,IAAI;AAAA,IACX;AAEA,WAAO,KAAK,qBAAqB,YAAY,cAAc,MAAM,SAAS,cAAc,MAAM,SAAS,kBAAkB,cAAc,QAAQ,KAAK,OAAO,IAAI,CAAC,MAAM,OAAO,KAAK,OAAO,IAAI,CAAC,MAAM,OAAO,WAAW,SAAS;AAAA,EACjO;AAEA,SAAO;AACT;;;AC/FA,IAAI;AAAJ,IACI;AADJ,IAEI,WAAW,SAASA,YAAW;AACjC,SAAO,QAAQ,OAAO,WAAW,gBAAgB,OAAO,OAAO,SAAS,KAAK,kBAAkB;AACjG;AAEO,IAAI,aAAa;AAAA,EACtB,SAAS;AAAA,EACT,MAAM;AAAA,EACN,MAAM,SAAS,KAAK,QAAQ,OAAO,OAAO;AACxC,WAAO,UAAU,aAAa,QAAQ;AAAA,MACpC;AAAA,IACF;AAEA,QAAI,IAAI,OAAO,SAAS,YAAY,GAChC,OAAO,MACP,SAAS,OACT,WAAW,OAAO,UAClB,WAAW,OAAO,UAClB,iBAAiB,OAAO,gBACxB,MAAM,OAAO,KACb,YAAY,KAAK,YAAY,MAAM,aAAa,IAChD,WAAW,KAAK,WAAW,MAAM,aAAa,MAAM,WAAW,WAAW,KAC1E,QACA,MACA,UACA,GACA,eACA,mBACA,WACA;AAEJ,SAAK,MAAM,OAAO,YAAY,MAAM,UAAU,MAAM;AAEpD,QAAI,EAAE,eAAe,WAAW,CAAC,KAAK,KAAK;AACzC,aAAO;AAAA,IACT;AAEA,SAAK,SAAS;AAEd,QAAI,EAAE,WAAW,QAAQ;AACvB,WAAK,OAAO,KAAK,WAAW,CAAC,EAAE;AAC/B;AAAA,IACF;AAEA,eAAW,eAAe,QAAQ,WAAW,OAAO,gBAAgB,KAAK,GAAG;AAC5E,iBAAa,WAAW,SAAS,cAAc,KAAK;AACpD,aAAS,YAAY,MAAM;AAC3B,WAAO,eAAe,UAAU,WAAW,OAAO,gBAAgB,KAAK,GAAG;AAC1E,SAAK,OAAO,MAAM;AAElB,SAAK,KAAK,QAAQ,QAAQ,EAAE,OAAO,KAAK,OAAO;AAE7C,UAAI;AACJ,iBAAW;AACX,aAAO;AAAA,IACT;AAEA,SAAK,WAAW,CAAC,EAAE,YAAY;AAC/B,SAAK,WAAW,MAAM,WAAW;AACjC,SAAK,WAAW,MAAM,WAAW;AACjC,QAAI,SAAS,SAAS,KAAK;AAC3B,aAAS,IAAI,IAAI,WAAW;AAE5B,QAAI,IAAI,GAAG;AACT,UAAI,CAAC;AAAA,IACP;AAEA,WAAO,EAAE,IAAI,IAAI;AACf,aAAO,KAAK,QAAQ;AAAA,IACtB;AAEA,QAAI,MAAM,SAAS,QAAQ;AACzB,UAAI;AACJ,sBAAgB,CAAC;AACjB,0BAAoB,CAAC;AACrB,kBAAY;AAEZ,WAAK,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK;AAChC,YAAI,KAAK,CAAC;AAEV,YAAI,MAAM,SAAS,CAAC,GAAG;AACrB,uBAAa;AAAA,QACf,OAAO;AACL,wBAAc,CAAC,IAAI,YAAY;AAC/B,4BAAkB,GAAG,IAAI,YAAY,SAAS,CAAC;AAC/C,sBAAY;AAAA,QACd;AAAA,MACF;AAEA,aAAO;AACP,iBAAW;AAEX,UAAI,WAAW;AACb,aAAK,KAAK,SAAS;AACnB,iBAAS,KAAK,SAAS;AAAA,MACzB;AAAA,IACF;AAEA,UAAM,SAAS,MAAM,SAAS,KAAK,IAAI,OAAO,MAAM,QAAQ,OAAO,QAAQ,MAAM,eAAe,IAAI,CAAC;AACrG,SAAK,MAAM;AACX,SAAK,WAAW;AAChB,SAAK,OAAO;AAEZ,SAAK,OAAO,KAAK,MAAM;AAAA,EACzB;AAAA,EACA,QAAQ,SAAS,OAAO,OAAO,MAAM;AACnC,QAAI,QAAQ,GAAG;AACb,cAAQ;AAAA,IACV,WAAW,QAAQ,GAAG;AACpB,cAAQ;AAAA,IACV;AAEA,QAAI,KAAK,MAAM;AACb,cAAQ,IAAI;AAAA,IACd;AAEA,QAAI,OAAO,KAAK,MACZ,WAAW,KAAK,UAChB,WAAW,KAAK,UAChB,WAAW,KAAK,UAChB,YAAY,KAAK,WACjB,SAAS,KAAK,QACd,WAAW,KAAK,UAChB,WAAW,KAAK,UAChB,MAAM,KAAK,KACX,IAAI,KAAK,QACT,KAAK,MAAM,IAAI,QAAQ,SAAS,IAAI,MAAM,GAC1C,UACA,UACA;AAEJ,QAAI,YAAY,OAAO;AACrB,iBAAW,YAAY;AACvB,iBAAW,YAAY,MAAM;AAC7B,aAAO,WAAW,kBAAkB,WAAW,OAAO,MAAM,KAAK,MAAM,GAAG,CAAC,EAAE,KAAK,SAAS,KAAK,WAAW,YAAY,OAAO,WAAW,kBAAkB,WAAW,OAAO,MAAM,YAAY,SAAS,MAAM,CAAC,EAAE,KAAK,SAAS,KAAK,WAAW,YAAY;AAAA,IAC7P,OAAO;AACL,YAAM,KAAK,MAAM,GAAG,CAAC,EAAE,KAAK,SAAS,IAAI,YAAY,SAAS,MAAM,CAAC,EAAE,KAAK,SAAS;AAAA,IACvF;AAEA,QAAI,KAAK,KAAK;AAEZ,aAAO,cAAc;AAAA,IACvB,OAAO;AACL,aAAO,YAAY,aAAa,YAAY,CAAC,IAAI,QAAQ,IAAI,IAAI,IAAI,MAAM,IAAI,EAAE,KAAK,cAAc,IAAI;AAAA,IAC1G;AAAA,EACF;AACF;AACA,WAAW,iBAAiB;AAC5B,WAAW,iBAAiB;AAC5B,WAAW,UAAU;AACrB,SAAS,KAAK,KAAK,eAAe,UAAU;", "names": ["_getGSAP"]}