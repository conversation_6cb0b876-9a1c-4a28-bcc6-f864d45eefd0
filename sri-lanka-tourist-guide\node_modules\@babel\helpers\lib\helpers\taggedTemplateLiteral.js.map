{"version": 3, "names": ["_taggedTemplateLiteral", "strings", "raw", "slice", "Object", "freeze", "defineProperties", "value"], "sources": ["../../src/helpers/taggedTemplateLiteral.ts"], "sourcesContent": ["/* @minVersion 7.0.0-beta.0 */\n\nexport default function _taggedTemplateLiteral(\n  strings: readonly string[],\n  raw?: readonly string[],\n): TemplateStringsArray {\n  if (!raw) {\n    raw = strings.slice(0);\n  }\n  return Object.freeze(\n    Object.defineProperties<TemplateStringsArray>(strings as any, {\n      raw: { value: Object.freeze(raw) },\n    }),\n  );\n}\n"], "mappings": ";;;;;;AAEe,SAASA,sBAAsBA,CAC5CC,OAA0B,EAC1BC,GAAuB,EACD;EACtB,IAAI,CAACA,GAAG,EAAE;IACRA,GAAG,GAAGD,OAAO,CAACE,KAAK,CAAC,CAAC,CAAC;EACxB;EACA,OAAOC,MAAM,CAACC,MAAM,CAClBD,MAAM,CAACE,gBAAgB,CAAuBL,OAAO,EAAS;IAC5DC,GAAG,EAAE;MAAEK,KAAK,EAAEH,MAAM,CAACC,MAAM,CAACH,GAAG;IAAE;EACnC,CAAC,CACH,CAAC;AACH", "ignoreList": []}