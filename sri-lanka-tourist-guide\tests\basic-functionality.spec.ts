import { test, expect } from '@playwright/test'

test.describe('Basic Functionality Tests', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/')
  })

  test('homepage loads successfully', async ({ page }) => {
    // Check page title
    await expect(page).toHaveTitle(/Sri Lanka Tourist Guide/)
    
    // Check main heading
    await expect(page.locator('h1')).toContainText('Sri Lanka')
    
    // Check navigation is present
    await expect(page.locator('nav')).toBeVisible()
    
    // Check footer is present
    await expect(page.locator('footer')).toBeVisible()
  })

  test('navigation menu works correctly', async ({ page }) => {
    // Test main navigation links
    const navLinks = [
      { text: 'Destinations', url: '/destinations' },
      { text: 'Activities', url: '/activities' },
      { text: 'Culture', url: '/culture' },
      { text: 'Travel Tips', url: '/travel-tips' },
      { text: 'Articles', url: '/articles' },
      { text: 'Contact', url: '/contact' }
    ]

    for (const link of navLinks) {
      await page.click(`nav a:has-text("${link.text}")`)
      await expect(page).toHaveURL(new RegExp(link.url))
      await page.goBack()
    }
  })

  test('mobile navigation works correctly', async ({ page }) => {
    // Set mobile viewport
    await page.setViewportSize({ width: 375, height: 667 })
    
    // Check if mobile menu button is visible
    const mobileMenuButton = page.locator('[aria-label="Toggle menu"], .mobile-menu-button, button:has-text("Menu")')
    await expect(mobileMenuButton).toBeVisible()
    
    // Click mobile menu button
    await mobileMenuButton.click()
    
    // Check if mobile menu is open
    const mobileMenu = page.locator('.mobile-menu, [role="dialog"]')
    await expect(mobileMenu).toBeVisible()
    
    // Test a navigation link in mobile menu
    await page.click('text=Articles')
    await expect(page).toHaveURL(/\/articles/)
  })

  test('search functionality works', async ({ page }) => {
    // Look for search input
    const searchInput = page.locator('input[type="search"], input[placeholder*="search" i], input[placeholder*="Search" i]')
    
    if (await searchInput.count() > 0) {
      await searchInput.fill('Sigiriya')
      await searchInput.press('Enter')
      
      // Check if search results are displayed
      await expect(page.locator('text=Sigiriya')).toBeVisible()
    }
  })

  test('articles page loads and displays articles', async ({ page }) => {
    await page.goto('/articles')
    
    // Check page title
    await expect(page).toHaveTitle(/Articles/)
    
    // Check if articles are displayed
    const articles = page.locator('[data-testid="article-card"], .article-card, article')
    await expect(articles.first()).toBeVisible()
    
    // Check if article has required elements
    await expect(articles.first().locator('h2, h3, .title')).toBeVisible()
    await expect(articles.first().locator('p, .excerpt, .description')).toBeVisible()
  })

  test('article detail page loads correctly', async ({ page }) => {
    // Go to articles page first
    await page.goto('/articles')
    
    // Click on first article
    const firstArticle = page.locator('[data-testid="article-card"], .article-card, article').first()
    await firstArticle.click()
    
    // Check if we're on an article detail page
    await expect(page).toHaveURL(/\/articles\//)
    
    // Check if article content is displayed
    await expect(page.locator('h1')).toBeVisible()
    await expect(page.locator('main, .content, .article-content')).toBeVisible()
    
    // Check if back button works
    const backButton = page.locator('text=Back, [aria-label="Back"], .back-button')
    if (await backButton.count() > 0) {
      await backButton.click()
      await expect(page).toHaveURL(/\/articles$/)
    }
  })

  test('contact page loads and form is present', async ({ page }) => {
    await page.goto('/contact')
    
    // Check page title
    await expect(page).toHaveTitle(/Contact/)
    
    // Check if contact form is present
    const form = page.locator('form')
    if (await form.count() > 0) {
      await expect(form).toBeVisible()
      
      // Check for common form fields
      const nameField = page.locator('input[name="name"], input[placeholder*="name" i]')
      const emailField = page.locator('input[name="email"], input[type="email"]')
      const messageField = page.locator('textarea[name="message"], textarea[placeholder*="message" i]')
      
      if (await nameField.count() > 0) await expect(nameField).toBeVisible()
      if (await emailField.count() > 0) await expect(emailField).toBeVisible()
      if (await messageField.count() > 0) await expect(messageField).toBeVisible()
    }
  })

  test('404 page works correctly', async ({ page }) => {
    await page.goto('/non-existent-page')
    
    // Check if 404 page is displayed
    await expect(page.locator('text=404, text=Not Found, text=Page not found')).toBeVisible()
    
    // Check if there's a link back to home
    const homeLink = page.locator('a[href="/"], text=Home, text=Go back')
    if (await homeLink.count() > 0) {
      await expect(homeLink).toBeVisible()
    }
  })

  test('external links open in new tab', async ({ page }) => {
    // Look for external links (social media, etc.)
    const externalLinks = page.locator('a[href^="http"]:not([href*="localhost"]):not([href*="127.0.0.1"])')
    
    if (await externalLinks.count() > 0) {
      const firstExternalLink = externalLinks.first()
      await expect(firstExternalLink).toHaveAttribute('target', '_blank')
      await expect(firstExternalLink).toHaveAttribute('rel', /noopener/)
    }
  })

  test('images have proper alt attributes', async ({ page }) => {
    const images = page.locator('img')
    const imageCount = await images.count()
    
    if (imageCount > 0) {
      for (let i = 0; i < Math.min(imageCount, 10); i++) {
        const img = images.nth(i)
        const alt = await img.getAttribute('alt')
        expect(alt).toBeTruthy()
        expect(alt?.length).toBeGreaterThan(0)
      }
    }
  })

  test('page has proper meta tags', async ({ page }) => {
    // Check for essential meta tags
    const metaDescription = page.locator('meta[name="description"]')
    await expect(metaDescription).toHaveCount(1)
    
    const metaViewport = page.locator('meta[name="viewport"]')
    await expect(metaViewport).toHaveCount(1)
    
    // Check Open Graph tags
    const ogTitle = page.locator('meta[property="og:title"]')
    const ogDescription = page.locator('meta[property="og:description"]')
    
    if (await ogTitle.count() > 0) {
      await expect(ogTitle).toHaveAttribute('content')
    }
    if (await ogDescription.count() > 0) {
      await expect(ogDescription).toHaveAttribute('content')
    }
  })

  test('page loads within acceptable time', async ({ page }) => {
    const startTime = Date.now()
    await page.goto('/')
    await page.waitForLoadState('networkidle')
    const loadTime = Date.now() - startTime
    
    // Page should load within 5 seconds
    expect(loadTime).toBeLessThan(5000)
  })

  test('no console errors on page load', async ({ page }) => {
    const consoleErrors: string[] = []
    
    page.on('console', msg => {
      if (msg.type() === 'error') {
        consoleErrors.push(msg.text())
      }
    })
    
    await page.goto('/')
    await page.waitForLoadState('networkidle')
    
    // Filter out known acceptable errors
    const filteredErrors = consoleErrors.filter(error => 
      !error.includes('favicon.ico') &&
      !error.includes('404') &&
      !error.includes('net::ERR_')
    )
    
    expect(filteredErrors).toHaveLength(0)
  })
})
