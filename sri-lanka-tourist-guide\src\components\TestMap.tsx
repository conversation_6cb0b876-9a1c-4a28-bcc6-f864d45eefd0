import React, { useEffect, useState } from 'react'
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON> } from 'react-leaflet'
import { Icon } from 'leaflet'

// Fix for default markers in React Leaflet
delete (Icon.Default.prototype as any)._getIconUrl
Icon.Default.mergeOptions({
  iconRetinaUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon-2x.png',
  iconUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon.png',
  shadowUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-shadow.png',
})

const TestMap: React.FC = () => {
  const [mapReady, setMapReady] = useState(false)

  // Simple test destinations
  const testDestinations = [
    { id: 1, name: '<PERSON><PERSON><PERSON>', lat: 7.9568, lng: 80.7592 },
    { id: 2, name: '<PERSON><PERSON>', lat: 7.2906, lng: 80.6337 },
    { id: 3, name: '<PERSON><PERSON>', lat: 6.0329, lng: 80.217 }
  ]

  return (
    <div style={{ padding: '20px' }}>
      <h1>Test Map Component</h1>
      <p>Map Ready: {mapReady ? 'Yes' : 'No'}</p>
      
      <div 
        style={{ 
          height: '500px', 
          width: '100%', 
          border: '2px solid #ccc',
          borderRadius: '8px',
          overflow: 'hidden'
        }}
      >
        <MapContainer
          center={[7.8731, 80.7718]}
          zoom={8}
          style={{ height: '100%', width: '100%' }}
          whenReady={() => {
            console.log('Map is ready!')
            setMapReady(true)
          }}
        >
          <TileLayer
            attribution='&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
            url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"
          />
          
          {testDestinations.map((dest) => (
            <Marker
              key={dest.id}
              position={[dest.lat, dest.lng]}
            >
              <Popup>
                <div>
                  <h3>{dest.name}</h3>
                  <p>Coordinates: {dest.lat}, {dest.lng}</p>
                </div>
              </Popup>
            </Marker>
          ))}
        </MapContainer>
      </div>
      
      <div style={{ marginTop: '20px' }}>
        <h3>Debug Info:</h3>
        <ul>
          <li>Map container height: 500px</li>
          <li>Map container width: 100%</li>
          <li>Number of markers: {testDestinations.length}</li>
          <li>Center coordinates: [7.8731, 80.7718]</li>
          <li>Zoom level: 8</li>
        </ul>
      </div>
    </div>
  )
}

export default TestMap
