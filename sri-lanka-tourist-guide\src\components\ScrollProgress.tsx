import React, { useEffect, useRef } from 'react'
import { gsap } from 'gsap'
import { ScrollTrigger } from 'gsap/ScrollTrigger'

gsap.registerPlugin(ScrollTrigger)

const ScrollProgress: React.FC = () => {
  const progressRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    if (!progressRef.current) return

    const progressBar = progressRef.current

    // Create scroll progress animation
    gsap.to(progressBar, {
      scaleX: 1,
      transformOrigin: 'left center',
      ease: 'none',
      scrollTrigger: {
        trigger: document.body,
        start: 'top top',
        end: 'bottom bottom',
        scrub: true
      }
    })

    return () => {
      ScrollTrigger.getAll().forEach(trigger => {
        if (trigger.trigger === document.body) {
          trigger.kill()
        }
      })
    }
  }, [])

  return (
    <div className="fixed top-0 left-0 w-full h-1 bg-gray-200 z-50 overflow-hidden">
      <div
        ref={progressRef}
        className="h-full bg-gradient-to-r from-primary-500 to-purple-500 transform scale-x-0 origin-left"
      />
    </div>
  )
}

export default ScrollProgress
