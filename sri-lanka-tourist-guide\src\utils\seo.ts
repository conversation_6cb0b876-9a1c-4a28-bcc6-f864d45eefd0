import { Article } from '../data/articles'

// SEO Configuration
export const seoConfig = {
  siteName: 'Sri Lanka Tourist Guide',
  siteUrl: 'https://srilankatouristguide.com',
  defaultTitle: 'Sri Lanka Tourist Guide - Complete Travel Guide to Paradise Island',
  defaultDescription: 'Comprehensive travel guide to Sri Lanka featuring destinations, cultural sites, wildlife, practical tips, and authentic experiences. Plan your perfect Sri Lankan adventure.',
  defaultImage: '/images/sri-lanka-hero.jpg',
  twitterHandle: '@SriLankaTourism',
  facebookAppId: '*********',
  language: 'en',
  locale: 'en_US',
  type: 'website'
}

// Generate SEO metadata for articles
export const generateArticleSEO = (article: Article) => {
  const title = `${article.title} | ${seoConfig.siteName}`
  const description = article.excerpt.length > 160 
    ? `${article.excerpt.substring(0, 157)}...`
    : article.excerpt
  
  const url = `${seoConfig.siteUrl}/articles/${article.id}`
  const image = article.image.startsWith('http') 
    ? article.image 
    : `${seoConfig.siteUrl}${article.image}`

  return {
    title,
    description,
    canonical: url,
    openGraph: {
      title,
      description,
      url,
      type: 'article',
      images: [
        {
          url: image,
          width: 1200,
          height: 630,
          alt: article.title
        }
      ],
      article: {
        publishedTime: article.publishDate,
        modifiedTime: article.publishDate,
        authors: [article.author || 'Sri Lanka Travel Expert'],
        tags: article.tags,
        section: article.category
      }
    },
    twitter: {
      card: 'summary_large_image',
      site: seoConfig.twitterHandle,
      title,
      description,
      image
    },
    additionalMetaTags: [
      {
        name: 'keywords',
        content: article.seoKeywords?.join(', ') || article.tags.join(', ')
      },
      {
        name: 'author',
        content: article.author || 'Sri Lanka Travel Expert'
      },
      {
        name: 'article:published_time',
        content: article.publishDate
      },
      {
        name: 'article:modified_time',
        content: article.publishDate
      },
      {
        name: 'article:section',
        content: article.category
      },
      {
        name: 'article:tag',
        content: article.tags.join(', ')
      }
    ]
  }
}

// Generate structured data for articles
export const generateArticleStructuredData = (article: Article) => {
  const baseUrl = seoConfig.siteUrl
  const articleUrl = `${baseUrl}/articles/${article.id}`
  const imageUrl = article.image.startsWith('http') 
    ? article.image 
    : `${baseUrl}${article.image}`

  return {
    '@context': 'https://schema.org',
    '@type': 'Article',
    headline: article.title,
    description: article.excerpt,
    image: {
      '@type': 'ImageObject',
      url: imageUrl,
      width: 1200,
      height: 630
    },
    author: {
      '@type': 'Person',
      name: article.author || 'Sri Lanka Travel Expert',
      url: `${baseUrl}/authors/${(article.author || 'sri-lanka-travel-expert').toLowerCase().replace(/\s+/g, '-')}`
    },
    publisher: {
      '@type': 'Organization',
      name: seoConfig.siteName,
      url: baseUrl,
      logo: {
        '@type': 'ImageObject',
        url: `${baseUrl}/images/logo.png`,
        width: 200,
        height: 60
      }
    },
    datePublished: article.publishDate,
    dateModified: article.publishDate,
    mainEntityOfPage: {
      '@type': 'WebPage',
      '@id': articleUrl
    },
    articleSection: article.category,
    keywords: article.seoKeywords?.join(', ') || article.tags.join(', '),
    wordCount: article.content.split(' ').length,
    url: articleUrl,
    isPartOf: {
      '@type': 'Blog',
      name: `${seoConfig.siteName} Blog`,
      url: `${baseUrl}/articles`
    },
    about: {
      '@type': 'Place',
      name: 'Sri Lanka',
      description: 'Beautiful island nation in South Asia known for its diverse landscapes, rich culture, and warm hospitality'
    }
  }
}

// Generate breadcrumb structured data
export const generateBreadcrumbStructuredData = (breadcrumbs: Array<{name: string, url: string}>) => {
  return {
    '@context': 'https://schema.org',
    '@type': 'BreadcrumbList',
    itemListElement: breadcrumbs.map((crumb, index) => ({
      '@type': 'ListItem',
      position: index + 1,
      name: crumb.name,
      item: crumb.url.startsWith('http') ? crumb.url : `${seoConfig.siteUrl}${crumb.url}`
    }))
  }
}

// Generate FAQ structured data
export const generateFAQStructuredData = (faqs: Array<{question: string, answer: string}>) => {
  return {
    '@context': 'https://schema.org',
    '@type': 'FAQPage',
    mainEntity: faqs.map(faq => ({
      '@type': 'Question',
      name: faq.question,
      acceptedAnswer: {
        '@type': 'Answer',
        text: faq.answer
      }
    }))
  }
}

// Generate travel guide structured data
export const generateTravelGuideStructuredData = (article: Article) => {
  if (!article.category.includes('Destinations') && !article.category.includes('Travel')) {
    return null
  }

  return {
    '@context': 'https://schema.org',
    '@type': 'TravelGuide',
    name: article.title,
    description: article.excerpt,
    url: `${seoConfig.siteUrl}/articles/${article.id}`,
    image: article.image.startsWith('http') ? article.image : `${seoConfig.siteUrl}${article.image}`,
    author: {
      '@type': 'Person',
      name: article.author || 'Sri Lanka Travel Expert'
    },
    publisher: {
      '@type': 'Organization',
      name: seoConfig.siteName,
      url: seoConfig.siteUrl
    },
    datePublished: article.publishDate,
    dateModified: article.publishDate,
    about: {
      '@type': 'Place',
      name: 'Sri Lanka',
      description: 'Beautiful island nation in South Asia'
    },
    touristType: ['Cultural Tourist', 'Adventure Tourist', 'Nature Tourist', 'Beach Tourist'],
    estimatedCost: {
      '@type': 'MonetaryAmount',
      currency: 'USD',
      value: '50-200'
    }
  }
}

// Generate website structured data
export const generateWebsiteStructuredData = () => {
  return {
    '@context': 'https://schema.org',
    '@type': 'WebSite',
    name: seoConfig.siteName,
    url: seoConfig.siteUrl,
    description: seoConfig.defaultDescription,
    publisher: {
      '@type': 'Organization',
      name: seoConfig.siteName,
      url: seoConfig.siteUrl,
      logo: {
        '@type': 'ImageObject',
        url: `${seoConfig.siteUrl}/images/logo.png`
      }
    },
    potentialAction: {
      '@type': 'SearchAction',
      target: {
        '@type': 'EntryPoint',
        urlTemplate: `${seoConfig.siteUrl}/search?q={search_term_string}`
      },
      'query-input': 'required name=search_term_string'
    },
    sameAs: [
      'https://www.facebook.com/srilankatourism',
      'https://www.twitter.com/srilankatourism',
      'https://www.instagram.com/srilankatourism'
    ]
  }
}

// Generate organization structured data
export const generateOrganizationStructuredData = () => {
  return {
    '@context': 'https://schema.org',
    '@type': 'Organization',
    name: seoConfig.siteName,
    url: seoConfig.siteUrl,
    logo: {
      '@type': 'ImageObject',
      url: `${seoConfig.siteUrl}/images/logo.png`,
      width: 200,
      height: 60
    },
    description: seoConfig.defaultDescription,
    foundingDate: '2024',
    contactPoint: {
      '@type': 'ContactPoint',
      contactType: 'Customer Service',
      email: '<EMAIL>'
    },
    sameAs: [
      'https://www.facebook.com/srilankatourism',
      'https://www.twitter.com/srilankatourism',
      'https://www.instagram.com/srilankatourism'
    ],
    areaServed: {
      '@type': 'Country',
      name: 'Sri Lanka'
    },
    knowsAbout: [
      'Sri Lanka Travel',
      'Tourism',
      'Cultural Heritage',
      'Wildlife',
      'Adventure Travel',
      'Beach Tourism'
    ]
  }
}

// SEO utility functions
export const seoUtils = {
  // Generate meta title with proper length
  generateTitle: (title: string, suffix = seoConfig.siteName) => {
    const fullTitle = `${title} | ${suffix}`
    return fullTitle.length > 60 ? title : fullTitle
  },

  // Generate meta description with proper length
  generateDescription: (description: string) => {
    return description.length > 160 ? `${description.substring(0, 157)}...` : description
  },

  // Generate canonical URL
  generateCanonical: (path: string) => {
    return `${seoConfig.siteUrl}${path.startsWith('/') ? path : `/${path}`}`
  },

  // Generate image URL
  generateImageUrl: (imagePath: string) => {
    return imagePath.startsWith('http') ? imagePath : `${seoConfig.siteUrl}${imagePath}`
  },

  // Extract keywords from content
  extractKeywords: (content: string, existingKeywords: string[] = []) => {
    const commonWords = ['the', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by', 'is', 'are', 'was', 'were', 'be', 'been', 'have', 'has', 'had', 'do', 'does', 'did', 'will', 'would', 'could', 'should', 'may', 'might', 'must', 'can', 'shall', 'this', 'that', 'these', 'those', 'a', 'an']
    
    const words = content.toLowerCase()
      .replace(/[^\w\s]/g, ' ')
      .split(/\s+/)
      .filter(word => word.length > 3 && !commonWords.includes(word))
    
    const wordCount = words.reduce((acc, word) => {
      acc[word] = (acc[word] || 0) + 1
      return acc
    }, {} as Record<string, number>)
    
    const topWords = Object.entries(wordCount)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 10)
      .map(([word]) => word)
    
    return [...new Set([...existingKeywords, ...topWords])]
  }
}

export default {
  config: seoConfig,
  generateArticleSEO,
  generateArticleStructuredData,
  generateBreadcrumbStructuredData,
  generateFAQStructuredData,
  generateTravelGuideStructuredData,
  generateWebsiteStructuredData,
  generateOrganizationStructuredData,
  utils: seoUtils
}
