import { gsap } from 'gsap'
import { ScrollTrigger } from 'gsap/ScrollTrigger'
import { TextPlugin } from 'gsap/TextPlugin'

// Register GSAP plugins
gsap.registerPlugin(ScrollTrigger, TextPlugin)

// Animation configurations
export const animationConfig = {
  duration: {
    fast: 0.3,
    normal: 0.6,
    slow: 1.2,
  },
  ease: {
    smooth: 'power2.out',
    bounce: 'back.out(1.7)',
    elastic: 'elastic.out(1, 0.3)',
  },
  stagger: {
    fast: 0.1,
    normal: 0.2,
    slow: 0.3,
  }
}

// Fade in animation
export const fadeIn = (element: string | Element, options?: gsap.TweenVars) => {
  return gsap.fromTo(
    element,
    { opacity: 0, y: 30 },
    {
      opacity: 1,
      y: 0,
      duration: animationConfig.duration.normal,
      ease: animationConfig.ease.smooth,
      ...options,
    }
  )
}

// Slide in from left
export const slideInLeft = (element: string | Element, options?: gsap.TweenVars) => {
  return gsap.fromTo(
    element,
    { opacity: 0, x: -50 },
    {
      opacity: 1,
      x: 0,
      duration: animationConfig.duration.normal,
      ease: animationConfig.ease.smooth,
      ...options,
    }
  )
}

// Slide in from right
export const slideInRight = (element: string | Element, options?: gsap.TweenVars) => {
  return gsap.fromTo(
    element,
    { opacity: 0, x: 50 },
    {
      opacity: 1,
      x: 0,
      duration: animationConfig.duration.normal,
      ease: animationConfig.ease.smooth,
      ...options,
    }
  )
}

// Scale in animation
export const scaleIn = (element: string | Element, options?: gsap.TweenVars) => {
  return gsap.fromTo(
    element,
    { opacity: 0, scale: 0.8 },
    {
      opacity: 1,
      scale: 1,
      duration: animationConfig.duration.normal,
      ease: animationConfig.ease.bounce,
      ...options,
    }
  )
}

// Stagger animation for multiple elements
export const staggerAnimation = (
  elements: string | Element[],
  animation: 'fadeIn' | 'slideInLeft' | 'slideInRight' | 'scaleIn',
  staggerDelay: number = animationConfig.stagger.normal
) => {
  const animationMap = {
    fadeIn: { opacity: 0, y: 30 },
    slideInLeft: { opacity: 0, x: -50 },
    slideInRight: { opacity: 0, x: 50 },
    scaleIn: { opacity: 0, scale: 0.8 },
  }

  const toMap = {
    fadeIn: { opacity: 1, y: 0 },
    slideInLeft: { opacity: 1, x: 0 },
    slideInRight: { opacity: 1, x: 0 },
    scaleIn: { opacity: 1, scale: 1 },
  }

  return gsap.fromTo(
    elements,
    animationMap[animation],
    {
      ...toMap[animation],
      duration: animationConfig.duration.normal,
      ease: animationConfig.ease.smooth,
      stagger: staggerDelay,
    }
  )
}

// Scroll-triggered animations
export const createScrollTrigger = (
  element: string | Element,
  animation: () => gsap.core.Timeline | gsap.core.Tween,
  options?: ScrollTrigger.Vars
) => {
  return ScrollTrigger.create({
    trigger: element,
    start: 'top 80%',
    end: 'bottom 20%',
    toggleActions: 'play none none reverse',
    ...options,
    animation: animation(),
  })
}

// Parallax effect
export const createParallax = (element: string | Element, speed: number = 0.5) => {
  return gsap.to(element, {
    yPercent: -50 * speed,
    ease: 'none',
    scrollTrigger: {
      trigger: element,
      start: 'top bottom',
      end: 'bottom top',
      scrub: true,
    },
  })
}

// Text reveal animation
export const textReveal = (element: string | Element, options?: gsap.TweenVars) => {
  return gsap.fromTo(
    element,
    { 
      opacity: 0,
      y: 100,
      skewY: 7,
    },
    {
      opacity: 1,
      y: 0,
      skewY: 0,
      duration: animationConfig.duration.slow,
      ease: animationConfig.ease.smooth,
      ...options,
    }
  )
}

// Hover animations
export const createHoverAnimation = (element: string | Element) => {
  const el = typeof element === 'string' ? document.querySelector(element) : element
  if (!el) return

  el.addEventListener('mouseenter', () => {
    gsap.to(el, {
      scale: 1.05,
      duration: animationConfig.duration.fast,
      ease: animationConfig.ease.smooth,
    })
  })

  el.addEventListener('mouseleave', () => {
    gsap.to(el, {
      scale: 1,
      duration: animationConfig.duration.fast,
      ease: animationConfig.ease.smooth,
    })
  })
}

// Loading animation
export const createLoadingAnimation = (element: string | Element) => {
  return gsap.to(element, {
    rotation: 360,
    duration: 1,
    ease: 'none',
    repeat: -1,
  })
}

// Page transition
export const pageTransition = {
  initial: { opacity: 0, y: 20 },
  animate: { opacity: 1, y: 0 },
  exit: { opacity: 0, y: -20 },
  transition: { duration: 0.5, ease: 'easeInOut' }
}

// Enhanced hover animations
export const hoverAnimations = {
  lift: (element: string | Element) => {
    const el = typeof element === 'string' ? document.querySelector(element) : element
    if (!el) return

    const handleMouseEnter = () => {
      gsap.to(el, {
        y: -8,
        scale: 1.02,
        duration: animationConfig.duration.fast,
        ease: animationConfig.ease.smooth
      })
    }

    const handleMouseLeave = () => {
      gsap.to(el, {
        y: 0,
        scale: 1,
        duration: animationConfig.duration.fast,
        ease: animationConfig.ease.smooth
      })
    }

    el.addEventListener('mouseenter', handleMouseEnter)
    el.addEventListener('mouseleave', handleMouseLeave)

    return () => {
      el.removeEventListener('mouseenter', handleMouseEnter)
      el.removeEventListener('mouseleave', handleMouseLeave)
    }
  },

  imageZoom: (element: string | Element) => {
    const el = typeof element === 'string' ? document.querySelector(element) : element
    if (!el) return

    const image = el.querySelector('img')
    if (!image) return

    const handleMouseEnter = () => {
      gsap.to(image, {
        scale: 1.1,
        duration: animationConfig.duration.normal,
        ease: animationConfig.ease.smooth
      })
    }

    const handleMouseLeave = () => {
      gsap.to(image, {
        scale: 1,
        duration: animationConfig.duration.normal,
        ease: animationConfig.ease.smooth
      })
    }

    el.addEventListener('mouseenter', handleMouseEnter)
    el.addEventListener('mouseleave', handleMouseLeave)

    return () => {
      el.removeEventListener('mouseenter', handleMouseEnter)
      el.removeEventListener('mouseleave', handleMouseLeave)
    }
  },

  glow: (element: string | Element) => {
    const el = typeof element === 'string' ? document.querySelector(element) : element
    if (!el) return

    const handleMouseEnter = () => {
      gsap.to(el, {
        boxShadow: "0 20px 40px rgba(0, 0, 0, 0.15)",
        duration: animationConfig.duration.fast,
        ease: animationConfig.ease.smooth
      })
    }

    const handleMouseLeave = () => {
      gsap.to(el, {
        boxShadow: "0 4px 6px rgba(0, 0, 0, 0.1)",
        duration: animationConfig.duration.fast,
        ease: animationConfig.ease.smooth
      })
    }

    el.addEventListener('mouseenter', handleMouseEnter)
    el.addEventListener('mouseleave', handleMouseLeave)

    return () => {
      el.removeEventListener('mouseenter', handleMouseEnter)
      el.removeEventListener('mouseleave', handleMouseLeave)
    }
  }
}

// Text animations
export const textAnimations = {
  typewriter: (element: string | Element, text: string, speed = 0.05) => {
    const el = typeof element === 'string' ? document.querySelector(element) : element
    if (!el) return

    return gsap.to(el, {
      duration: text.length * speed,
      text: text,
      ease: "none"
    })
  },

  fadeInWords: (element: string | Element) => {
    const el = typeof element === 'string' ? document.querySelector(element) : element
    if (!el) return

    const words = el.textContent?.split(' ') || []
    el.innerHTML = words.map(word => `<span class="word">${word}</span>`).join(' ')

    const wordElements = el.querySelectorAll('.word')
    return gsap.fromTo(wordElements,
      { opacity: 0, y: 20 },
      {
        opacity: 1,
        y: 0,
        duration: 0.6,
        stagger: 0.1,
        ease: animationConfig.ease.smooth
      }
    )
  },

  slideInChars: (element: string | Element) => {
    const el = typeof element === 'string' ? document.querySelector(element) : element
    if (!el) return

    const text = el.textContent || ''
    el.innerHTML = text.split('').map(char =>
      char === ' ' ? ' ' : `<span class="char">${char}</span>`
    ).join('')

    const charElements = el.querySelectorAll('.char')
    return gsap.fromTo(charElements,
      { opacity: 0, x: -20 },
      {
        opacity: 1,
        x: 0,
        duration: 0.05,
        stagger: 0.02,
        ease: animationConfig.ease.smooth
      }
    )
  }
}

// Navigation animations
export const navigationAnimations = {
  slideDown: (element: string | Element) => {
    return gsap.fromTo(element,
      { opacity: 0, y: -20 },
      {
        opacity: 1,
        y: 0,
        duration: animationConfig.duration.fast,
        ease: animationConfig.ease.smooth
      }
    )
  },

  slideUp: (element: string | Element) => {
    return gsap.to(element, {
      opacity: 0,
      y: -20,
      duration: animationConfig.duration.fast,
      ease: animationConfig.ease.smooth
    })
  },

  mobileMenuSlide: (element: string | Element, isOpen: boolean) => {
    if (isOpen) {
      return gsap.fromTo(element,
        { x: '100%' },
        {
          x: '0%',
          duration: animationConfig.duration.normal,
          ease: animationConfig.ease.smooth
        }
      )
    } else {
      return gsap.to(element, {
        x: '100%',
        duration: animationConfig.duration.normal,
        ease: animationConfig.ease.smooth
      })
    }
  }
}

// Cleanup function
export const cleanupAnimations = () => {
  ScrollTrigger.getAll().forEach(trigger => trigger.kill())
  gsap.killTweensOf('*')
}

// Utility functions
export const animationUtils = {
  killAll: cleanupAnimations,
  refresh: () => ScrollTrigger.refresh(),
  createTimeline: (options?: gsap.TimelineVars) => gsap.timeline(options),
  batch: (elements: (string | Element)[], animation: (element: string | Element) => void) => {
    elements.forEach(animation)
  }
}
