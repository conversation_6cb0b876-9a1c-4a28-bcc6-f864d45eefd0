import { test, expect } from '@playwright/test'

test.describe('SEO Tests', () => {
  test('homepage has proper SEO meta tags', async ({ page }) => {
    await page.goto('/')
    
    // Check title
    const title = await page.title()
    expect(title).toBeTruthy()
    expect(title.length).toBeGreaterThan(10)
    expect(title.length).toBeLessThan(60)
    expect(title.toLowerCase()).toContain('sri lanka')
    
    // Check meta description
    const metaDescription = await page.getAttribute('meta[name="description"]', 'content')
    expect(metaDescription).toBeTruthy()
    expect(metaDescription!.length).toBeGreaterThan(120)
    expect(metaDescription!.length).toBeLessThan(160)
    
    // Check canonical URL
    const canonical = await page.getAttribute('link[rel="canonical"]', 'href')
    expect(canonical).toBeTruthy()
    expect(canonical).toMatch(/^https?:\/\//)
    
    // Check viewport meta tag
    const viewport = await page.getAttribute('meta[name="viewport"]', 'content')
    expect(viewport).toBeTruthy()
    expect(viewport).toContain('width=device-width')
    
    // Check language attribute
    const lang = await page.getAttribute('html', 'lang')
    expect(lang).toBeTruthy()
    expect(lang).toMatch(/^[a-z]{2}(-[A-Z]{2})?$/)
  })

  test('Open Graph tags are present and valid', async ({ page }) => {
    await page.goto('/')
    
    // Check Open Graph title
    const ogTitle = await page.getAttribute('meta[property="og:title"]', 'content')
    expect(ogTitle).toBeTruthy()
    expect(ogTitle!.length).toBeGreaterThan(10)
    
    // Check Open Graph description
    const ogDescription = await page.getAttribute('meta[property="og:description"]', 'content')
    expect(ogDescription).toBeTruthy()
    expect(ogDescription!.length).toBeGreaterThan(50)
    
    // Check Open Graph image
    const ogImage = await page.getAttribute('meta[property="og:image"]', 'content')
    expect(ogImage).toBeTruthy()
    expect(ogImage).toMatch(/\.(jpg|jpeg|png|webp)$/i)
    
    // Check Open Graph URL
    const ogUrl = await page.getAttribute('meta[property="og:url"]', 'content')
    expect(ogUrl).toBeTruthy()
    expect(ogUrl).toMatch(/^https?:\/\//)
    
    // Check Open Graph type
    const ogType = await page.getAttribute('meta[property="og:type"]', 'content')
    expect(ogType).toBeTruthy()
    expect(['website', 'article']).toContain(ogType)
    
    // Check Open Graph site name
    const ogSiteName = await page.getAttribute('meta[property="og:site_name"]', 'content')
    expect(ogSiteName).toBeTruthy()
  })

  test('Twitter Card tags are present and valid', async ({ page }) => {
    await page.goto('/')
    
    // Check Twitter card type
    const twitterCard = await page.getAttribute('meta[name="twitter:card"]', 'content')
    expect(twitterCard).toBeTruthy()
    expect(['summary', 'summary_large_image']).toContain(twitterCard)
    
    // Check Twitter title
    const twitterTitle = await page.getAttribute('meta[name="twitter:title"]', 'content')
    expect(twitterTitle).toBeTruthy()
    
    // Check Twitter description
    const twitterDescription = await page.getAttribute('meta[name="twitter:description"]', 'content')
    expect(twitterDescription).toBeTruthy()
    
    // Check Twitter image
    const twitterImage = await page.getAttribute('meta[name="twitter:image"]', 'content')
    expect(twitterImage).toBeTruthy()
    expect(twitterImage).toMatch(/\.(jpg|jpeg|png|webp)$/i)
  })

  test('structured data is present and valid', async ({ page }) => {
    await page.goto('/')
    
    // Check for JSON-LD structured data
    const structuredData = await page.evaluate(() => {
      const scripts = Array.from(document.querySelectorAll('script[type="application/ld+json"]'))
      return scripts.map(script => {
        try {
          return JSON.parse(script.textContent || '')
        } catch (e) {
          return null
        }
      }).filter(Boolean)
    })
    
    expect(structuredData.length).toBeGreaterThan(0)
    
    // Check for Organization schema
    const organizationSchema = structuredData.find(data => 
      data['@type'] === 'Organization' || 
      (Array.isArray(data['@graph']) && data['@graph'].some((item: any) => item['@type'] === 'Organization'))
    )
    expect(organizationSchema).toBeTruthy()
    
    // Check for WebSite schema
    const websiteSchema = structuredData.find(data => 
      data['@type'] === 'WebSite' ||
      (Array.isArray(data['@graph']) && data['@graph'].some((item: any) => item['@type'] === 'WebSite'))
    )
    expect(websiteSchema).toBeTruthy()
  })

  test('article pages have proper SEO', async ({ page }) => {
    await page.goto('/articles')
    
    // Click on first article
    const firstArticle = page.locator('[data-testid="article-card"], .article-card, article').first()
    await firstArticle.click()
    
    // Check article title
    const title = await page.title()
    expect(title).toBeTruthy()
    expect(title.length).toBeGreaterThan(10)
    expect(title.length).toBeLessThan(60)
    
    // Check article meta description
    const metaDescription = await page.getAttribute('meta[name="description"]', 'content')
    expect(metaDescription).toBeTruthy()
    expect(metaDescription!.length).toBeGreaterThan(120)
    expect(metaDescription!.length).toBeLessThan(160)
    
    // Check article structured data
    const articleStructuredData = await page.evaluate(() => {
      const scripts = Array.from(document.querySelectorAll('script[type="application/ld+json"]'))
      return scripts.map(script => {
        try {
          return JSON.parse(script.textContent || '')
        } catch (e) {
          return null
        }
      }).filter(Boolean)
    })
    
    const articleSchema = articleStructuredData.find(data => 
      data['@type'] === 'Article' ||
      data['@type'] === 'BlogPosting' ||
      data['@type'] === 'TravelGuide'
    )
    expect(articleSchema).toBeTruthy()
    
    if (articleSchema) {
      expect(articleSchema.headline).toBeTruthy()
      expect(articleSchema.author).toBeTruthy()
      expect(articleSchema.datePublished).toBeTruthy()
      expect(articleSchema.publisher).toBeTruthy()
    }
  })

  test('heading structure is SEO-friendly', async ({ page }) => {
    const pages = ['/', '/articles', '/destinations', '/contact']
    
    for (const url of pages) {
      await page.goto(url)
      
      const headings = await page.evaluate(() => {
        const headingElements = Array.from(document.querySelectorAll('h1, h2, h3, h4, h5, h6'))
        return headingElements.map(el => ({
          level: parseInt(el.tagName.charAt(1)),
          text: el.textContent?.trim(),
          isEmpty: !el.textContent?.trim()
        }))
      })
      
      // Should have exactly one H1
      const h1Count = headings.filter(h => h.level === 1).length
      expect(h1Count).toBe(1)
      
      // H1 should not be empty
      const h1 = headings.find(h => h.level === 1)
      expect(h1?.isEmpty).toBe(false)
      expect(h1?.text?.length).toBeGreaterThan(10)
      
      // Check heading hierarchy (no skipping levels)
      for (let i = 1; i < headings.length; i++) {
        const currentLevel = headings[i].level
        const previousLevel = headings[i - 1].level
        
        if (currentLevel > previousLevel) {
          expect(currentLevel - previousLevel).toBeLessThanOrEqual(1)
        }
      }
      
      // Headings should not be empty
      headings.forEach(heading => {
        expect(heading.isEmpty).toBe(false)
      })
    }
  })

  test('images have SEO-friendly attributes', async ({ page }) => {
    await page.goto('/')
    
    const images = await page.evaluate(() => {
      const imgElements = Array.from(document.querySelectorAll('img'))
      return imgElements.map(img => ({
        src: img.src,
        alt: img.alt,
        title: img.title,
        loading: img.loading,
        hasAlt: !!img.alt,
        altLength: img.alt?.length || 0,
        isDecorative: img.getAttribute('role') === 'presentation' || img.getAttribute('role') === 'none'
      }))
    })
    
    images.forEach(img => {
      // All images should have alt text unless they're decorative
      if (!img.isDecorative) {
        expect(img.hasAlt).toBe(true)
        expect(img.altLength).toBeGreaterThan(0)
        expect(img.altLength).toBeLessThan(125) // Keep alt text concise
      }
      
      // Images should have proper src
      expect(img.src).toBeTruthy()
      expect(img.src).toMatch(/\.(jpg|jpeg|png|webp|svg)$/i)
    })
  })

  test('internal links are SEO-friendly', async ({ page }) => {
    await page.goto('/')
    
    const links = await page.evaluate(() => {
      const linkElements = Array.from(document.querySelectorAll('a[href]'))
      return linkElements.map(link => ({
        href: link.getAttribute('href'),
        text: link.textContent?.trim(),
        title: link.title,
        isInternal: link.getAttribute('href')?.startsWith('/') || link.getAttribute('href')?.includes(window.location.hostname),
        isExternal: link.getAttribute('href')?.startsWith('http') && !link.getAttribute('href')?.includes(window.location.hostname),
        hasText: !!link.textContent?.trim(),
        target: link.target,
        rel: link.rel
      }))
    })
    
    // Internal links should have descriptive text
    const internalLinks = links.filter(link => link.isInternal)
    internalLinks.forEach(link => {
      expect(link.hasText).toBe(true)
      expect(link.text?.length).toBeGreaterThan(2)
      expect(link.text?.toLowerCase()).not.toBe('click here')
      expect(link.text?.toLowerCase()).not.toBe('read more')
      expect(link.text?.toLowerCase()).not.toBe('here')
    })
    
    // External links should open in new tab and have proper rel attributes
    const externalLinks = links.filter(link => link.isExternal)
    externalLinks.forEach(link => {
      expect(link.target).toBe('_blank')
      expect(link.rel).toContain('noopener')
    })
  })

  test('page URLs are SEO-friendly', async ({ page }) => {
    const pages = [
      '/',
      '/destinations',
      '/activities', 
      '/culture',
      '/travel-tips',
      '/articles',
      '/contact'
    ]
    
    for (const url of pages) {
      await page.goto(url)
      
      const currentUrl = page.url()
      
      // URLs should be lowercase
      expect(currentUrl.toLowerCase()).toBe(currentUrl)
      
      // URLs should not have trailing slashes (except root)
      if (url !== '/') {
        expect(currentUrl.endsWith('/')).toBe(false)
      }
      
      // URLs should use hyphens, not underscores
      expect(currentUrl).not.toMatch(/_/)
      
      // URLs should be descriptive
      if (url !== '/') {
        const pathParts = url.split('/').filter(Boolean)
        pathParts.forEach(part => {
          expect(part.length).toBeGreaterThan(2)
          expect(part).toMatch(/^[a-z0-9-]+$/)
        })
      }
    }
  })

  test('robots.txt and sitemap.xml are accessible', async ({ page }) => {
    // Check robots.txt
    const robotsResponse = await page.goto('/robots.txt')
    expect(robotsResponse?.status()).toBe(200)
    
    const robotsContent = await page.textContent('body')
    expect(robotsContent).toContain('User-agent')
    expect(robotsContent).toContain('Sitemap')
    
    // Check sitemap.xml
    const sitemapResponse = await page.goto('/sitemap.xml')
    expect(sitemapResponse?.status()).toBe(200)
    
    const sitemapContent = await page.textContent('body')
    expect(sitemapContent).toContain('<urlset')
    expect(sitemapContent).toContain('<url>')
    expect(sitemapContent).toContain('<loc>')
  })

  test('page loading speed affects SEO', async ({ page }) => {
    const startTime = Date.now()
    await page.goto('/')
    await page.waitForLoadState('domcontentloaded')
    const loadTime = Date.now() - startTime
    
    // Page should load quickly for better SEO
    expect(loadTime).toBeLessThan(3000) // 3 seconds
    
    // Check for render-blocking resources
    const renderBlockingResources = await page.evaluate(() => {
      const stylesheets = Array.from(document.querySelectorAll('link[rel="stylesheet"]:not([media="print"])'))
      const syncScripts = Array.from(document.querySelectorAll('script:not([async]):not([defer])'))
      
      return {
        stylesheets: stylesheets.length,
        syncScripts: syncScripts.length
      }
    })
    
    // Should minimize render-blocking resources
    expect(renderBlockingResources.stylesheets).toBeLessThan(5)
    expect(renderBlockingResources.syncScripts).toBeLessThan(3)
  })

  test('mobile-friendliness for SEO', async ({ page }) => {
    // Set mobile viewport
    await page.setViewportSize({ width: 375, height: 667 })
    await page.goto('/')
    
    // Check viewport meta tag
    const viewport = await page.getAttribute('meta[name="viewport"]', 'content')
    expect(viewport).toContain('width=device-width')
    expect(viewport).toContain('initial-scale=1')
    
    // Check if content fits in viewport
    const contentOverflow = await page.evaluate(() => {
      return document.body.scrollWidth > window.innerWidth
    })
    expect(contentOverflow).toBe(false)
    
    // Check if text is readable without zooming
    const textElements = await page.evaluate(() => {
      const elements = Array.from(document.querySelectorAll('p, h1, h2, h3, h4, h5, h6, span, a'))
      return elements.slice(0, 10).map(el => {
        const styles = window.getComputedStyle(el)
        return {
          fontSize: parseFloat(styles.fontSize),
          lineHeight: parseFloat(styles.lineHeight)
        }
      })
    })
    
    // Text should be at least 16px for mobile readability
    textElements.forEach(text => {
      expect(text.fontSize).toBeGreaterThanOrEqual(14)
    })
  })
})
