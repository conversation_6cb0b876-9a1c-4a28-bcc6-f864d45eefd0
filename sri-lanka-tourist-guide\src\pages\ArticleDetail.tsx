import React, { useState, useEffect } from 'react'
import { use<PERSON>arams, Link, Navigate } from 'react-router-dom'
import { motion } from 'framer-motion'
import {
  ArrowLeftIcon,
  ClockIcon,
  CalendarIcon,
  TagIcon,
  ShareIcon,
  BookmarkIcon
} from '@heroicons/react/24/outline'
import AnimatedSection from '@/components/AnimatedSection'
import { ArticleSEO } from '@/components/SEOHead'
import { getArticleById, type Article } from '@/data/articles'
import { renderMarkdown, generateTableOfContents, type TOCItem } from '@/utils/markdownRenderer'


const ArticleDetail: React.FC = () => {
  const { id } = useParams<{ id: string }>()
  const [article, setArticle] = useState<Article | null>(null)
  const [loading, setLoading] = useState(true)
  const [renderedContent, setRenderedContent] = useState<string>('')
  const [tableOfContents, setTableOfContents] = useState<TOCItem[]>([])

  useEffect(() => {
    if (id) {
      const foundArticle = getArticleById(id)
      setArticle(foundArticle || null)

      if (foundArticle) {
        // Render markdown content
        const rendered = renderMarkdown(foundArticle.content)
        setRenderedContent(rendered)

        // Generate table of contents
        const toc = generateTableOfContents(foundArticle.content)
        setTableOfContents(toc)
      }
    }
    setLoading(false)
  }, [id])

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary-600"></div>
      </div>
    )
  }

  if (!article) {
    return <Navigate to="/articles" replace />
  }

  // Generate breadcrumbs for SEO
  const breadcrumbs = [
    { name: 'Home', url: '/' },
    { name: 'Articles', url: '/articles' },
    { name: article.title, url: `/articles/${article.id}` }
  ]

  return (
    <>
      <ArticleSEO article={article} breadcrumbs={breadcrumbs} />

      <div className="min-h-screen bg-white">
        {/* Back Navigation */}
        <div className="bg-gray-50 border-b">
          <div className="container-max py-4">
            <Link
              to="/articles"
              className="inline-flex items-center space-x-2 text-gray-600 hover:text-primary-600 transition-colors"
            >
              <ArrowLeftIcon className="h-5 w-5" />
              <span>Back to Articles</span>
            </Link>
          </div>
        </div>

        {/* Article Header */}
        <section className="section-padding bg-gradient-to-br from-primary-50 to-purple-50">
          <div className="container-max">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              className="max-w-4xl mx-auto text-center"
            >
              <div className="flex items-center justify-center space-x-4 mb-6">
                <span className="px-3 py-1 bg-primary-100 text-primary-700 rounded-full text-sm font-medium">
                  {article.category}
                </span>
                <div className="flex items-center space-x-2 text-gray-600">
                  <ClockIcon className="h-4 w-4" />
                  <span className="text-sm">{article.readTime}</span>
                </div>
                <div className="flex items-center space-x-2 text-gray-600">
                  <CalendarIcon className="h-4 w-4" />
                  <span className="text-sm">{article.publishDate}</span>
                </div>
              </div>
              
              <h1 className="text-3xl md:text-5xl font-bold text-gray-900 mb-6">
                {article.title}
              </h1>
              
              <p className="text-xl text-gray-600 mb-8">
                {article.excerpt}
              </p>

              <div className="flex flex-wrap justify-center gap-2 mb-8">
                {article.tags.map((tag) => (
                  <span
                    key={tag}
                    className="inline-flex items-center space-x-1 px-3 py-1 bg-white text-gray-600 rounded-full text-sm"
                  >
                    <TagIcon className="h-3 w-3" />
                    <span>{tag}</span>
                  </span>
                ))}
              </div>

              <div className="flex justify-center space-x-4">
                <button
                  type="button"
                  className="flex items-center space-x-2 px-4 py-2 bg-white text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
                >
                  <ShareIcon className="h-5 w-5" />
                  <span>Share</span>
                </button>
                <button
                  type="button"
                  className="flex items-center space-x-2 px-4 py-2 bg-white text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
                >
                  <BookmarkIcon className="h-5 w-5" />
                  <span>Save</span>
                </button>
              </div>
            </motion.div>
          </div>
        </section>

        {/* Article Content */}
        <section className="section-padding">
          <div className="container-max">
            <div className="max-w-6xl mx-auto">
              <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
                {/* Table of Contents - Desktop */}
                {tableOfContents.length > 0 && (
                  <div className="hidden lg:block lg:col-span-1">
                    <div className="sticky top-24">
                      <div className="bg-gray-50 rounded-lg p-6">
                        <h3 className="text-lg font-semibold text-gray-900 mb-4">Table of Contents</h3>
                        <nav className="space-y-2">
                          {tableOfContents.map((item) => (
                            <a
                              key={item.id}
                              href={`#${item.id}`}
                              className={`block text-sm hover:text-primary-600 transition-colors ${
                                item.level === 1 ? 'font-semibold text-gray-900' :
                                item.level === 2 ? 'font-medium text-gray-700 ml-2' :
                                'text-gray-600 ml-4'
                              }`}
                            >
                              {item.text}
                            </a>
                          ))}
                        </nav>
                      </div>
                    </div>
                  </div>
                )}

                {/* Main Content */}
                <div className={`${tableOfContents.length > 0 ? 'lg:col-span-3' : 'lg:col-span-4'}`}>
                  <AnimatedSection>
                    <div className="prose prose-lg max-w-none">
                      <div
                        dangerouslySetInnerHTML={{ __html: renderedContent }}
                        className="article-content"
                      />
                    </div>
                  </AnimatedSection>

                  {/* Related Articles */}
                  {article.relatedArticles && article.relatedArticles.length > 0 && (
                    <div className="mt-12 pt-8 border-t border-gray-200">
                      <h3 className="text-2xl font-bold text-gray-900 mb-6">Related Articles</h3>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        {article.relatedArticles.slice(0, 4).map((relatedId) => {
                          const relatedArticle = getArticleById(relatedId)
                          if (!relatedArticle) return null

                          return (
                            <Link
                              key={relatedId}
                              to={`/articles/${relatedId}`}
                              className="block bg-white border border-gray-200 rounded-lg p-6 hover:shadow-lg transition-shadow"
                            >
                              <h4 className="text-lg font-semibold text-gray-900 mb-2 line-clamp-2">
                                {relatedArticle.title}
                              </h4>
                              <p className="text-gray-600 text-sm mb-3 line-clamp-3">
                                {relatedArticle.excerpt}
                              </p>
                              <div className="flex items-center justify-between text-sm text-gray-500">
                                <span>{relatedArticle.category}</span>
                                <span>{relatedArticle.readTime}</span>
                              </div>
                            </Link>
                          )
                        })}
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        </section>
      </div>
    </>
  )
}

export default ArticleDetail
