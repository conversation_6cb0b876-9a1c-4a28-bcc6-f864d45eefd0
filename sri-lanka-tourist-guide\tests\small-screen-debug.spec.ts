import { test, expect } from '@playwright/test'

test.describe('Small Screen Debug', () => {
  test('Identify specific overflow elements on iPhone SE', async ({ page }) => {
    await page.goto('/')
    await page.waitForLoadState('networkidle')
    
    // Set iPhone SE viewport (320px)
    await page.setViewportSize({ width: 320, height: 568 })
    await page.waitForTimeout(500)
    
    const overflowDetails = await page.evaluate(() => {
      const elements = Array.from(document.querySelectorAll('*'))
      const overflowingElements = elements.filter(el => {
        const rect = el.getBoundingClientRect()
        return rect.right > window.innerWidth || rect.left < 0
      })
      
      return overflowingElements.map(el => ({
        tag: el.tagName,
        className: el.className,
        id: el.id,
        width: el.getBoundingClientRect().width,
        right: el.getBoundingClientRect().right,
        left: el.getBoundingClientRect().left,
        viewportWidth: window.innerWidth,
        overflow: el.getBoundingClientRect().right - window.innerWidth,
        textContent: el.textContent?.slice(0, 50),
        styles: {
          position: window.getComputedStyle(el).position,
          display: window.getComputedStyle(el).display,
          width: window.getComputedStyle(el).width,
          minWidth: window.getComputedStyle(el).minWidth,
          maxWidth: window.getComputedStyle(el).maxWidth,
          overflow: window.getComputedStyle(el).overflow,
          whiteSpace: window.getComputedStyle(el).whiteSpace
        }
      }))
    })
    
    console.log('Overflowing elements on 320px:', overflowDetails)
    
    // Take screenshot for visual inspection
    await page.screenshot({
      path: 'test-results/iphone-se-overflow-debug.png',
      fullPage: true
    })
    
    expect(overflowDetails.length).toBeLessThan(3)
  })

  test('Check specific components at 320px', async ({ page }) => {
    await page.goto('/')
    await page.waitForLoadState('networkidle')
    await page.setViewportSize({ width: 320, height: 568 })
    
    const componentCheck = await page.evaluate(() => {
      const components = {
        header: document.querySelector('header'),
        nav: document.querySelector('nav'),
        logo: document.querySelector('.flex.items-center'),
        heroSection: document.querySelector('.hero-section, .relative.min-h-screen'),
        contentSections: document.querySelectorAll('section'),
        buttons: document.querySelectorAll('button'),
        links: document.querySelectorAll('a')
      }
      
      const results: any = {}
      
      Object.entries(components).forEach(([name, element]) => {
        if (element) {
          if (element instanceof NodeList) {
            results[name] = Array.from(element).map(el => ({
              width: el.getBoundingClientRect().width,
              overflows: el.getBoundingClientRect().right > window.innerWidth,
              className: el.className
            }))
          } else {
            results[name] = {
              width: element.getBoundingClientRect().width,
              overflows: element.getBoundingClientRect().right > window.innerWidth,
              className: element.className
            }
          }
        }
      })
      
      return results
    })
    
    console.log('Component check at 320px:', componentCheck)
  })
})
