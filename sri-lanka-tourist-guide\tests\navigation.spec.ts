import { test, expect } from '@playwright/test';

test.describe('Navigation Tests', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/');
  });

  test('should navigate to all main pages', async ({ page }) => {
    // Test home page
    await expect(page).toHaveTitle(/Sri Lanka/);
    
    // Test destinations page
    await page.click('text=Destinations');
    await expect(page).toHaveURL('/destinations');
    await expect(page.locator('h1')).toContainText('Destinations');
    
    // Test activities page
    await page.click('text=Activities');
    await expect(page).toHaveURL('/activities');
    await expect(page.locator('h1')).toContainText('Activities');
    
    // Test culture page
    await page.click('text=Culture');
    await expect(page).toHaveURL('/culture');
    await expect(page.locator('h1')).toContainText('Culture');
    
    // Test articles page
    await page.click('text=Articles');
    await expect(page).toHaveURL('/articles');
    await expect(page.locator('h1')).toContainText('Articles');
    
    // Test travel tips page
    await page.click('text=Travel Tips');
    await expect(page).toHaveURL('/travel-tips');
    await expect(page.locator('h1')).toContainText('Travel');
    
    // Test contact page
    await page.click('text=Contact');
    await expect(page).toHaveURL('/contact');
    await expect(page.locator('h1')).toContainText('Get in Touch');
  });

  test('should have working logo link', async ({ page }) => {
    await page.goto('/destinations');
    await page.click('text=Sri Lanka Guide');
    await expect(page).toHaveURL('/');
  });

  test('should have responsive mobile navigation', async ({ page }) => {
    await page.setViewportSize({ width: 375, height: 667 });

    // Mobile menu should be hidden initially
    await expect(page.locator('[data-testid="mobile-menu"]')).not.toBeVisible();

    // Click hamburger menu
    await page.click('[aria-label="Open navigation menu"]');

    // Mobile menu should be visible
    await expect(page.locator('[data-testid="mobile-menu"]')).toBeVisible();

    // Test mobile navigation - click within the mobile menu
    await page.click('[data-testid="mobile-menu"] a[href="/destinations"]');
    await expect(page).toHaveURL('/destinations');
  });

  test('should highlight active navigation item', async ({ page }) => {
    await page.goto('/destinations');
    
    // Check if destinations nav item has active styling
    const destinationsLink = page.locator('nav a[href="/destinations"]');
    await expect(destinationsLink).toHaveClass(/text-primary-600/);
  });
});

test.describe('Footer Tests', () => {
  test('should have working footer links', async ({ page }) => {
    await page.goto('/');
    
    // Scroll to footer
    await page.locator('footer').scrollIntoViewIfNeeded();
    
    // Test footer navigation links
    const footerDestinations = page.locator('footer a[href="/destinations"]');
    await expect(footerDestinations).toBeVisible();
    
    const footerActivities = page.locator('footer a[href="/activities"]');
    await expect(footerActivities).toBeVisible();
    
    const footerCulture = page.locator('footer a[href="/culture"]');
    await expect(footerCulture).toBeVisible();
    
    const footerTravelTips = page.locator('footer a[href="/travel-tips"]');
    await expect(footerTravelTips).toBeVisible();
  });

  test('should display contact information', async ({ page }) => {
    await page.goto('/');
    
    await page.locator('footer').scrollIntoViewIfNeeded();
    
    // Check for contact information
    await expect(page.locator('footer')).toContainText('<EMAIL>');
    await expect(page.locator('footer')).toContainText('+94 11 123 4567');
    await expect(page.locator('footer')).toContainText('Colombo, Sri Lanka');
  });

  test('should display copyright information', async ({ page }) => {
    await page.goto('/');
    
    await page.locator('footer').scrollIntoViewIfNeeded();
    
    await expect(page.locator('footer')).toContainText('© 2025 Sri Lanka Tourist Guide');
    await expect(page.locator('footer')).toContainText('Tera Works');
  });
});
