#!/usr/bin/env node

const fs = require('fs')
const path = require('path')

// Sitemap generator for Sri Lanka Tourist Guide
const SITE_URL = 'https://srilankatouristguide.com'
const OUTPUT_DIR = path.join(__dirname, '../public')
const SITEMAP_FILE = path.join(OUTPUT_DIR, 'sitemap.xml')
const ROBOTS_FILE = path.join(OUTPUT_DIR, 'robots.txt')

// Static pages configuration
const STATIC_PAGES = [
  {
    url: '/',
    changefreq: 'weekly',
    priority: '1.0',
    lastmod: new Date().toISOString().split('T')[0]
  },
  {
    url: '/destinations',
    changefreq: 'weekly',
    priority: '0.9',
    lastmod: new Date().toISOString().split('T')[0]
  },
  {
    url: '/activities',
    changefreq: 'weekly',
    priority: '0.9',
    lastmod: new Date().toISOString().split('T')[0]
  },
  {
    url: '/culture',
    changefreq: 'weekly',
    priority: '0.9',
    lastmod: new Date().toISOString().split('T')[0]
  },
  {
    url: '/travel-tips',
    changefreq: 'weekly',
    priority: '0.9',
    lastmod: new Date().toISOString().split('T')[0]
  },
  {
    url: '/articles',
    changefreq: 'daily',
    priority: '0.9',
    lastmod: new Date().toISOString().split('T')[0]
  },
  {
    url: '/contact',
    changefreq: 'monthly',
    priority: '0.7',
    lastmod: new Date().toISOString().split('T')[0]
  },
  {
    url: '/about',
    changefreq: 'monthly',
    priority: '0.6',
    lastmod: new Date().toISOString().split('T')[0]
  },
  {
    url: '/privacy-policy',
    changefreq: 'yearly',
    priority: '0.3',
    lastmod: new Date().toISOString().split('T')[0]
  },
  {
    url: '/terms-of-service',
    changefreq: 'yearly',
    priority: '0.3',
    lastmod: new Date().toISOString().split('T')[0]
  }
]

// Article categories for dynamic pages
const ARTICLE_CATEGORIES = [
  'Cultural Heritage',
  'Adventure & Activities', 
  'Food & Culinary',
  'Practical Travel',
  'Destinations',
  'Wildlife & Nature'
]

// Generate article URLs from content directory
function getArticleUrls() {
  const contentDir = path.join(__dirname, '../content-generation/articles')
  const articles = []
  
  if (fs.existsSync(contentDir)) {
    const files = fs.readdirSync(contentDir).filter(file => file.endsWith('.md'))
    
    files.forEach(file => {
      const articleId = path.basename(file, '.md')
      articles.push({
        url: `/articles/${articleId}`,
        changefreq: 'monthly',
        priority: '0.8',
        lastmod: new Date().toISOString().split('T')[0]
      })
    })
  }
  
  return articles
}

// Generate category URLs
function getCategoryUrls() {
  return ARTICLE_CATEGORIES.map(category => ({
    url: `/articles/category/${encodeURIComponent(category.toLowerCase().replace(/\s+/g, '-'))}`,
    changefreq: 'weekly',
    priority: '0.7',
    lastmod: new Date().toISOString().split('T')[0]
  }))
}

// Generate XML sitemap
function generateSitemap() {
  console.log('🗺️ Generating sitemap.xml...')
  
  const staticPages = STATIC_PAGES
  const articlePages = getArticleUrls()
  const categoryPages = getCategoryUrls()
  
  const allPages = [...staticPages, ...articlePages, ...categoryPages]
  
  let sitemapXml = `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9"
        xmlns:news="http://www.google.com/schemas/sitemap-news/0.9"
        xmlns:xhtml="http://www.w3.org/1999/xhtml"
        xmlns:mobile="http://www.google.com/schemas/sitemap-mobile/1.0"
        xmlns:image="http://www.google.com/schemas/sitemap-image/1.1"
        xmlns:video="http://www.google.com/schemas/sitemap-video/1.1">
`

  allPages.forEach(page => {
    sitemapXml += `  <url>
    <loc>${SITE_URL}${page.url}</loc>
    <lastmod>${page.lastmod}</lastmod>
    <changefreq>${page.changefreq}</changefreq>
    <priority>${page.priority}</priority>
  </url>
`
  })
  
  sitemapXml += '</urlset>'
  
  // Ensure output directory exists
  if (!fs.existsSync(OUTPUT_DIR)) {
    fs.mkdirSync(OUTPUT_DIR, { recursive: true })
  }
  
  fs.writeFileSync(SITEMAP_FILE, sitemapXml)
  
  console.log(`✅ Generated sitemap with ${allPages.length} URLs`)
  console.log(`   - Static pages: ${staticPages.length}`)
  console.log(`   - Article pages: ${articlePages.length}`)
  console.log(`   - Category pages: ${categoryPages.length}`)
  
  return allPages.length
}

// Generate robots.txt
function generateRobotsTxt() {
  console.log('🤖 Generating robots.txt...')
  
  const robotsTxt = `# Sri Lanka Tourist Guide - Robots.txt
# Generated on: ${new Date().toISOString()}

User-agent: *
Allow: /

# Sitemap location
Sitemap: ${SITE_URL}/sitemap.xml

# Crawl delay for respectful crawling
Crawl-delay: 1

# Disallow admin and private areas
Disallow: /admin/
Disallow: /private/
Disallow: /_next/
Disallow: /api/

# Allow important directories
Allow: /images/
Allow: /assets/
Allow: /articles/
Allow: /destinations/
Allow: /activities/
Allow: /culture/
Allow: /travel-tips/

# Disallow query parameters that don't add value
Disallow: /*?utm_*
Disallow: /*?ref=*
Disallow: /*?source=*

# Special instructions for search engines
User-agent: Googlebot
Allow: /

User-agent: Bingbot
Allow: /

User-agent: Slurp
Allow: /

# Block problematic bots
User-agent: AhrefsBot
Disallow: /

User-agent: MJ12bot
Disallow: /

User-agent: DotBot
Disallow: /
`

  fs.writeFileSync(ROBOTS_FILE, robotsTxt)
  console.log('✅ Generated robots.txt')
}

// Generate additional SEO files
function generateAdditionalSEOFiles() {
  console.log('📄 Generating additional SEO files...')
  
  // Generate security.txt
  const securityTxt = `Contact: mailto:<EMAIL>
Expires: ${new Date(Date.now() + 365 * 24 * 60 * 60 * 1000).toISOString()}
Preferred-Languages: en
Canonical: ${SITE_URL}/.well-known/security.txt
`
  
  const wellKnownDir = path.join(OUTPUT_DIR, '.well-known')
  if (!fs.existsSync(wellKnownDir)) {
    fs.mkdirSync(wellKnownDir, { recursive: true })
  }
  
  fs.writeFileSync(path.join(wellKnownDir, 'security.txt'), securityTxt)
  
  // Generate humans.txt
  const humansTxt = `/* TEAM */
Developer: Sri Lanka Tourism Development Team
Contact: <EMAIL>
Location: Sri Lanka

/* SITE */
Last update: ${new Date().toISOString().split('T')[0]}
Language: English
Doctype: HTML5
IDE: Visual Studio Code
Technologies: React, TypeScript, Vite, GSAP
`
  
  fs.writeFileSync(path.join(OUTPUT_DIR, 'humans.txt'), humansTxt)
  
  console.log('✅ Generated security.txt and humans.txt')
}

// Validate sitemap
function validateSitemap() {
  console.log('🔍 Validating sitemap...')
  
  if (!fs.existsSync(SITEMAP_FILE)) {
    console.error('❌ Sitemap file not found')
    return false
  }
  
  const sitemapContent = fs.readFileSync(SITEMAP_FILE, 'utf-8')
  
  // Basic XML validation
  if (!sitemapContent.includes('<?xml') || !sitemapContent.includes('<urlset')) {
    console.error('❌ Invalid XML structure')
    return false
  }
  
  // Check for required elements
  const urlCount = (sitemapContent.match(/<url>/g) || []).length
  const locCount = (sitemapContent.match(/<loc>/g) || []).length
  
  if (urlCount !== locCount) {
    console.error('❌ Mismatch between URL and LOC elements')
    return false
  }
  
  // Check file size (should be under 50MB and 50,000 URLs)
  const fileSizeKB = fs.statSync(SITEMAP_FILE).size / 1024
  
  if (fileSizeKB > 50 * 1024) {
    console.warn('⚠️ Sitemap file is larger than 50MB')
  }
  
  if (urlCount > 50000) {
    console.warn('⚠️ Sitemap contains more than 50,000 URLs')
  }
  
  console.log(`✅ Sitemap validation passed`)
  console.log(`   - URLs: ${urlCount}`)
  console.log(`   - File size: ${fileSizeKB.toFixed(2)} KB`)
  
  return true
}

// Main function
function main() {
  console.log('🚀 Starting SEO files generation...')
  
  try {
    const urlCount = generateSitemap()
    generateRobotsTxt()
    generateAdditionalSEOFiles()
    
    if (validateSitemap()) {
      console.log('🎉 SEO files generation completed successfully!')
      console.log(`📊 Summary:`)
      console.log(`   - Total URLs in sitemap: ${urlCount}`)
      console.log(`   - Files generated: sitemap.xml, robots.txt, security.txt, humans.txt`)
      console.log(`   - Ready for search engine submission`)
    } else {
      console.error('❌ Sitemap validation failed')
      process.exit(1)
    }
    
  } catch (error) {
    console.error('❌ SEO files generation failed:', error)
    process.exit(1)
  }
}

if (require.main === module) {
  main()
}

module.exports = {
  generateSitemap,
  generateRobotsTxt,
  generateAdditionalSEOFiles,
  validateSitemap
}
