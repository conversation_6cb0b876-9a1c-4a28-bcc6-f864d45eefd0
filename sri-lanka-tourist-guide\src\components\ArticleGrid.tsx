import React, { useEffect, useRef } from 'react'
import { Link } from 'react-router-dom'
import { Clock, User, Calendar, MapPin, TrendingUp } from 'lucide-react'
import { Article } from '../data/articles'
import { gsap } from 'gsap'
import { ScrollTrigger } from 'gsap/ScrollTrigger'

gsap.registerPlugin(ScrollTrigger)

interface ArticleGridProps {
  articles: Article[]
  className?: string
  showFeatured?: boolean
}

const ArticleCard: React.FC<{ article: Article; index: number }> = ({ article, index }) => {
  const cardRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    const card = cardRef.current
    if (!card) return

    // Initial state
    gsap.set(card, {
      opacity: 0,
      y: 50,
      scale: 0.95
    })

    // Animate in
    gsap.to(card, {
      opacity: 1,
      y: 0,
      scale: 1,
      duration: 0.6,
      delay: index * 0.1,
      ease: "power2.out",
      scrollTrigger: {
        trigger: card,
        start: "top 85%",
        toggleActions: "play none none reverse"
      }
    })

    // Hover animations
    const handleMouseEnter = () => {
      gsap.to(card, {
        y: -8,
        scale: 1.02,
        duration: 0.3,
        ease: "power2.out"
      })
      
      gsap.to(card.querySelector('.article-image'), {
        scale: 1.1,
        duration: 0.4,
        ease: "power2.out"
      })
    }

    const handleMouseLeave = () => {
      gsap.to(card, {
        y: 0,
        scale: 1,
        duration: 0.3,
        ease: "power2.out"
      })
      
      gsap.to(card.querySelector('.article-image'), {
        scale: 1,
        duration: 0.4,
        ease: "power2.out"
      })
    }

    card.addEventListener('mouseenter', handleMouseEnter)
    card.addEventListener('mouseleave', handleMouseLeave)

    return () => {
      card.removeEventListener('mouseenter', handleMouseEnter)
      card.removeEventListener('mouseleave', handleMouseLeave)
      ScrollTrigger.getAll().forEach(trigger => {
        if (trigger.trigger === card) {
          trigger.kill()
        }
      })
    }
  }, [index])

  const getDifficultyColor = (difficulty?: string) => {
    switch (difficulty) {
      case 'Easy': return '#10b981'
      case 'Easy to Moderate': return '#3b82f6'
      case 'Moderate': return '#f59e0b'
      case 'Moderate to Challenging': return '#ef4444'
      case 'Challenging': return '#dc2626'
      default: return '#6b7280'
    }
  }

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'Destinations': return <MapPin size={16} />
      case 'Practical Travel': return <TrendingUp size={16} />
      default: return <MapPin size={16} />
    }
  }

  return (
    <div ref={cardRef} className="article-card">
      <Link to={`/article/${article.id}`} className="article-link">
        <div className="article-image-container">
          <img
            src={article.image}
            alt={article.title}
            className="article-image"
            loading="lazy"
          />
          {article.featured && (
            <div className="featured-badge">
              <span>Featured</span>
            </div>
          )}
          <div className="category-badge">
            {getCategoryIcon(article.category)}
            <span>{article.category}</span>
          </div>
        </div>
        
        <div className="article-content">
          <div className="article-meta">
            <div className="meta-item">
              <Clock size={14} />
              <span>{article.readTime}</span>
            </div>
            {article.difficulty && (
              <div className="meta-item difficulty" style={{ color: getDifficultyColor(article.difficulty) }}>
                <span>{article.difficulty}</span>
              </div>
            )}
          </div>
          
          <h3 className="article-title">{article.title}</h3>
          <p className="article-excerpt">{article.excerpt}</p>
          
          <div className="article-footer">
            <div className="article-tags">
              {article.tags.slice(0, 3).map((tag, index) => (
                <span key={index} className="tag">
                  {tag}
                </span>
              ))}
            </div>
            
            <div className="article-author">
              <User size={14} />
              <span>{article.author}</span>
            </div>
          </div>
        </div>
      </Link>

      <style jsx>{`
        .article-card {
          background: white;
          border-radius: 1rem;
          overflow: hidden;
          box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
          transition: all 0.3s ease;
          height: 100%;
          display: flex;
          flex-direction: column;
        }

        .article-link {
          text-decoration: none;
          color: inherit;
          height: 100%;
          display: flex;
          flex-direction: column;
        }

        .article-image-container {
          position: relative;
          height: 200px;
          overflow: hidden;
        }

        .article-image {
          width: 100%;
          height: 100%;
          object-fit: cover;
          transition: transform 0.4s ease;
        }

        .featured-badge {
          position: absolute;
          top: 1rem;
          left: 1rem;
          background: linear-gradient(135deg, #f59e0b, #d97706);
          color: white;
          padding: 0.25rem 0.75rem;
          border-radius: 1rem;
          font-size: 0.75rem;
          font-weight: 600;
          text-transform: uppercase;
          letter-spacing: 0.05em;
        }

        .category-badge {
          position: absolute;
          top: 1rem;
          right: 1rem;
          background: rgba(255, 255, 255, 0.95);
          backdrop-filter: blur(10px);
          color: #374151;
          padding: 0.5rem 0.75rem;
          border-radius: 0.75rem;
          font-size: 0.75rem;
          font-weight: 600;
          display: flex;
          align-items: center;
          gap: 0.25rem;
          border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .article-content {
          padding: 1.5rem;
          flex: 1;
          display: flex;
          flex-direction: column;
        }

        .article-meta {
          display: flex;
          align-items: center;
          gap: 1rem;
          margin-bottom: 1rem;
          font-size: 0.875rem;
          color: #6b7280;
        }

        .meta-item {
          display: flex;
          align-items: center;
          gap: 0.25rem;
        }

        .meta-item.difficulty {
          font-weight: 600;
        }

        .article-title {
          font-size: 1.25rem;
          font-weight: 700;
          color: #111827;
          margin-bottom: 0.75rem;
          line-height: 1.4;
          display: -webkit-box;
          -webkit-line-clamp: 2;
          -webkit-box-orient: vertical;
          overflow: hidden;
        }

        .article-excerpt {
          color: #6b7280;
          line-height: 1.6;
          margin-bottom: 1.5rem;
          flex: 1;
          display: -webkit-box;
          -webkit-line-clamp: 3;
          -webkit-box-orient: vertical;
          overflow: hidden;
        }

        .article-footer {
          display: flex;
          justify-content: space-between;
          align-items: end;
          gap: 1rem;
        }

        .article-tags {
          display: flex;
          flex-wrap: wrap;
          gap: 0.5rem;
          flex: 1;
        }

        .tag {
          background: #f3f4f6;
          color: #374151;
          padding: 0.25rem 0.5rem;
          border-radius: 0.375rem;
          font-size: 0.75rem;
          font-weight: 500;
        }

        .article-author {
          display: flex;
          align-items: center;
          gap: 0.25rem;
          font-size: 0.75rem;
          color: #6b7280;
          white-space: nowrap;
        }

        @media (max-width: 768px) {
          .article-image-container {
            height: 160px;
          }

          .article-content {
            padding: 1rem;
          }

          .article-title {
            font-size: 1.125rem;
          }

          .article-footer {
            flex-direction: column;
            align-items: start;
            gap: 0.75rem;
          }
        }
      `}</style>
    </div>
  )
}

export const ArticleGrid: React.FC<ArticleGridProps> = ({
  articles,
  className = '',
  showFeatured = false
}) => {
  const gridRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    const grid = gridRef.current
    if (!grid) return

    // Animate grid container
    gsap.fromTo(grid, 
      { opacity: 0, y: 30 },
      { 
        opacity: 1, 
        y: 0, 
        duration: 0.8, 
        ease: "power2.out",
        scrollTrigger: {
          trigger: grid,
          start: "top 90%",
          toggleActions: "play none none reverse"
        }
      }
    )
  }, [])

  const displayArticles = showFeatured 
    ? articles.filter(article => article.featured)
    : articles

  if (displayArticles.length === 0) {
    return (
      <div className="no-articles">
        <div className="no-articles-content">
          <h3>No articles found</h3>
          <p>Try adjusting your search or filter criteria</p>
        </div>
        
        <style jsx>{`
          .no-articles {
            text-align: center;
            padding: 4rem 2rem;
            color: #6b7280;
          }

          .no-articles-content h3 {
            font-size: 1.5rem;
            font-weight: 600;
            margin-bottom: 0.5rem;
            color: #374151;
          }

          .no-articles-content p {
            font-size: 1rem;
          }
        `}</style>
      </div>
    )
  }

  return (
    <div ref={gridRef} className={`article-grid ${className}`}>
      {displayArticles.map((article, index) => (
        <ArticleCard key={article.id} article={article} index={index} />
      ))}

      <style jsx>{`
        .article-grid {
          display: grid;
          grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
          gap: 2rem;
          padding: 1rem 0;
        }

        @media (max-width: 768px) {
          .article-grid {
            grid-template-columns: 1fr;
            gap: 1.5rem;
          }
        }

        @media (max-width: 480px) {
          .article-grid {
            gap: 1rem;
          }
        }
      `}</style>
    </div>
  )
}

export default ArticleGrid
