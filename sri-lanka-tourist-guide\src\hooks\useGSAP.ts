import { useEffect, useRef, useCallback } from 'react'
import { gsap } from 'gsap'
import { ScrollTrigger } from 'gsap/ScrollTrigger'
import { TextPlugin } from 'gsap/TextPlugin'
import {
  fadeIn,
  slideInLeft,
  slideInRight,
  scaleIn,
  createScrollTrigger,
  createParallax,
  textReveal,
  hoverAnimations,
  textAnimations,
  navigationAnimations
} from '@/utils/animations'

gsap.registerPlugin(ScrollTrigger, TextPlugin)

// Hook for fade in animation on mount
export const useFadeIn = (delay: number = 0) => {
  const ref = useRef<HTMLDivElement>(null)

  useEffect(() => {
    if (ref.current) {
      fadeIn(ref.current, { delay })
    }
  }, [delay])

  return ref
}

// Hook for slide in animations
export const useSlideIn = (direction: 'left' | 'right' = 'left', delay: number = 0) => {
  const ref = useRef<HTMLDivElement>(null)

  useEffect(() => {
    if (ref.current) {
      const animation = direction === 'left' ? slideInLeft : slideInRight
      animation(ref.current, { delay })
    }
  }, [direction, delay])

  return ref
}

// Hook for scale in animation
export const useScaleIn = (delay: number = 0) => {
  const ref = useRef<HTMLElement>(null)

  useEffect(() => {
    if (ref.current) {
      scaleIn(ref.current, { delay })
    }
  }, [delay])

  return ref
}

// Hook for scroll-triggered animations
export const useScrollTrigger = (
  animationType: 'fadeIn' | 'slideInLeft' | 'slideInRight' | 'scaleIn' = 'fadeIn',
  options?: ScrollTrigger.Vars
) => {
  const ref = useRef<HTMLElement>(null)

  useEffect(() => {
    if (ref.current) {
      const animationMap = {
        fadeIn: () => fadeIn(ref.current!),
        slideInLeft: () => slideInLeft(ref.current!),
        slideInRight: () => slideInRight(ref.current!),
        scaleIn: () => scaleIn(ref.current!),
      }

      const trigger = createScrollTrigger(
        ref.current,
        animationMap[animationType],
        options
      )

      return () => {
        trigger.kill()
      }
    }
  }, [animationType, options])

  return ref
}

// Hook for parallax effect
export const useParallax = (speed: number = 0.5) => {
  const ref = useRef<HTMLElement>(null)

  useEffect(() => {
    if (ref.current) {
      const parallax = createParallax(ref.current, speed)

      return () => {
        parallax.kill()
      }
    }
  }, [speed])

  return ref
}

// Hook for text reveal animation
export const useTextReveal = (delay: number = 0) => {
  const ref = useRef<HTMLElement>(null)

  useEffect(() => {
    if (ref.current) {
      textReveal(ref.current, { delay })
    }
  }, [delay])

  return ref
}

// Hook for stagger animations on children
export const useStaggerChildren = (
  animationType: 'fadeIn' | 'slideInLeft' | 'slideInRight' | 'scaleIn' = 'fadeIn',
  staggerDelay: number = 0.1
) => {
  const ref = useRef<HTMLDivElement>(null)

  useEffect(() => {
    if (ref.current) {
      const children = ref.current.children
      if (children.length > 0) {
        const animationMap = {
          fadeIn: { from: { opacity: 0, y: 30 }, to: { opacity: 1, y: 0 } },
          slideInLeft: { from: { opacity: 0, x: -50 }, to: { opacity: 1, x: 0 } },
          slideInRight: { from: { opacity: 0, x: 50 }, to: { opacity: 1, x: 0 } },
          scaleIn: { from: { opacity: 0, scale: 0.8 }, to: { opacity: 1, scale: 1 } },
        }

        const { from, to } = animationMap[animationType]

        gsap.fromTo(
          children,
          from,
          {
            ...to,
            duration: 0.6,
            ease: 'power2.out',
            stagger: staggerDelay,
          }
        )
      }
    }
  }, [animationType, staggerDelay])

  return ref
}

// Hook for hover animations
export const useHoverScale = (scale: number = 1.05) => {
  const ref = useRef<HTMLElement>(null)

  useEffect(() => {
    const element = ref.current
    if (!element) return

    const handleMouseEnter = () => {
      gsap.to(element, {
        scale,
        duration: 0.3,
        ease: 'power2.out',
      })
    }

    const handleMouseLeave = () => {
      gsap.to(element, {
        scale: 1,
        duration: 0.3,
        ease: 'power2.out',
      })
    }

    element.addEventListener('mouseenter', handleMouseEnter)
    element.addEventListener('mouseleave', handleMouseLeave)

    return () => {
      element.removeEventListener('mouseenter', handleMouseEnter)
      element.removeEventListener('mouseleave', handleMouseLeave)
    }
  }, [scale])

  return ref
}

// Hook for timeline animations
export const useTimeline = (animations: (() => void)[], autoPlay: boolean = true) => {
  const ref = useRef<HTMLElement>(null)
  const timelineRef = useRef<gsap.core.Timeline>()

  useEffect(() => {
    if (ref.current && animations.length > 0) {
      timelineRef.current = gsap.timeline({ paused: !autoPlay })
      
      animations.forEach((animation) => {
        animation()
      })

      return () => {
        timelineRef.current?.kill()
      }
    }
  }, [animations, autoPlay])

  const play = () => timelineRef.current?.play()
  const pause = () => timelineRef.current?.pause()
  const reverse = () => timelineRef.current?.reverse()
  const restart = () => timelineRef.current?.restart()

  return { ref, play, pause, reverse, restart, timeline: timelineRef.current }
}

// Hook for enhanced hover animations
export const useEnhancedHover = (
  hoverAnimation: 'lift' | 'imageZoom' | 'glow' = 'lift'
) => {
  const ref = useRef<HTMLElement>(null)

  useEffect(() => {
    if (!ref.current) return

    let cleanup: (() => void) | undefined

    switch (hoverAnimation) {
      case 'lift':
        cleanup = hoverAnimations.lift(ref.current)
        break
      case 'imageZoom':
        cleanup = hoverAnimations.imageZoom(ref.current)
        break
      case 'glow':
        cleanup = hoverAnimations.glow(ref.current)
        break
    }

    return cleanup
  }, [hoverAnimation])

  return ref
}

// Hook for text animations
export const useTextAnimation = (
  text: string,
  animationType: 'typewriter' | 'fadeInWords' | 'slideInChars' = 'fadeInWords',
  speed = 0.05
) => {
  const ref = useRef<HTMLElement>(null)

  useEffect(() => {
    if (!ref.current) return

    switch (animationType) {
      case 'typewriter':
        textAnimations.typewriter(ref.current, text, speed)
        break
      case 'fadeInWords':
        textAnimations.fadeInWords(ref.current)
        break
      case 'slideInChars':
        textAnimations.slideInChars(ref.current)
        break
    }
  }, [text, animationType, speed])

  return ref
}

// Hook for scroll-triggered animations
export const useScrollAnimation = (
  animation: gsap.TweenVars,
  triggerOptions?: Partial<ScrollTrigger.Vars>
) => {
  const ref = useRef<HTMLElement>(null)

  useEffect(() => {
    if (!ref.current) return

    const trigger = createScrollTrigger(ref.current, () => {
      return gsap.fromTo(ref.current!,
        { opacity: 0, y: 50 },
        {
          opacity: 1,
          y: 0,
          duration: 0.8,
          ease: "power2.out",
          ...animation
        }
      )
    }, triggerOptions)

    return () => {
      trigger.kill()
    }
  }, [animation, triggerOptions])

  return ref
}

// Hook for batch animations
export const useBatchAnimation = (
  selector: string,
  animation: gsap.TweenVars,
  stagger = 0.1
) => {
  const ref = useRef<HTMLElement>(null)

  useEffect(() => {
    if (!ref.current) return

    const elements = ref.current.querySelectorAll(selector)
    if (elements.length === 0) return

    gsap.fromTo(elements,
      { opacity: 0, y: 30 },
      {
        opacity: 1,
        y: 0,
        duration: 0.6,
        ease: "power2.out",
        stagger,
        ...animation
      }
    )
  }, [selector, animation, stagger])

  return ref
}

// Hook for navigation animations
export const useNavigationAnimation = (
  animationType: 'slideDown' | 'slideUp' | 'mobileMenuSlide' = 'slideDown',
  isOpen?: boolean
) => {
  const ref = useRef<HTMLElement>(null)

  const trigger = useCallback(() => {
    if (!ref.current) return

    switch (animationType) {
      case 'slideDown':
        navigationAnimations.slideDown(ref.current)
        break
      case 'slideUp':
        navigationAnimations.slideUp(ref.current)
        break
      case 'mobileMenuSlide':
        if (isOpen !== undefined) {
          navigationAnimations.mobileMenuSlide(ref.current, isOpen)
        }
        break
    }
  }, [animationType, isOpen])

  return { ref, trigger }
}

// Hook for performance optimization
export const useGSAPPerformance = () => {
  useEffect(() => {
    // Optimize for mobile devices
    if (window.innerWidth < 768) {
      gsap.globalTimeline.timeScale(1.5) // Speed up animations on mobile
    }

    // Enable GPU acceleration for better performance
    gsap.config({ force3D: true })

    return () => {
      gsap.globalTimeline.timeScale(1)
    }
  }, [])
}
