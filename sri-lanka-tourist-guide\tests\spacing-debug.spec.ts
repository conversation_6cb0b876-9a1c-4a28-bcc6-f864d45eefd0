import { test, expect } from '@playwright/test'

test.describe('Spacing Debug', () => {
  test('Identify specific sections with spacing issues', async ({ page }) => {
    await page.goto('/')
    await page.waitForLoadState('networkidle')
    await page.setViewportSize({ width: 375, height: 667 })
    
    const sectionDetails = await page.evaluate(() => {
      const sections = document.querySelectorAll('section, article, .content-section')
      
      return Array.from(sections).map((section, index) => {
        const rect = section.getBoundingClientRect()
        const styles = window.getComputedStyle(section)
        
        const paddingTop = parseFloat(styles.paddingTop)
        const paddingBottom = parseFloat(styles.paddingBottom)
        const marginTop = parseFloat(styles.marginTop)
        const marginBottom = parseFloat(styles.marginBottom)
        const paddingLeft = parseFloat(styles.paddingLeft)
        const paddingRight = parseFloat(styles.paddingRight)
        
        const totalVerticalSpacing = paddingTop + paddingBottom + marginTop + marginBottom
        
        return {
          index,
          tagName: section.tagName,
          className: section.className,
          id: section.id,
          paddingTop,
          paddingBottom,
          marginTop,
          marginBottom,
          paddingLeft,
          paddingRight,
          totalVerticalSpacing,
          hasInsufficientVerticalSpacing: totalVerticalSpacing < 16,
          hasInsufficientHorizontalPadding: paddingLeft < 16 || paddingRight < 16,
          textContent: section.textContent?.slice(0, 100)
        }
      })
    })
    
    console.log('Section Details:', sectionDetails)
    
    // Take screenshot for visual inspection
    await page.screenshot({
      path: 'test-results/spacing-debug.png',
      fullPage: true
    })
    
    // Identify problematic sections
    const problematicSections = sectionDetails.filter(section => 
      section.hasInsufficientVerticalSpacing || section.hasInsufficientHorizontalPadding
    )
    
    console.log('Problematic Sections:', problematicSections)
    
    expect(problematicSections.length).toBeLessThan(sectionDetails.length * 0.5) // Allow 50% tolerance for debugging
  })
})
