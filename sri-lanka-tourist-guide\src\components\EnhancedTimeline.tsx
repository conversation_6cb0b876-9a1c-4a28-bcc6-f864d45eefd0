import React, { useEffect, useRef } from 'react'
import { motion } from 'framer-motion'
import { gsap } from 'gsap'
import { ScrollTrigger } from 'gsap/ScrollTrigger'
import { HistoricalPeriod } from '@/data/culture'

gsap.registerPlugin(ScrollTrigger)

interface EnhancedTimelineProps {
  periods: HistoricalPeriod[]
  className?: string
}

const EnhancedTimeline: React.FC<EnhancedTimelineProps> = ({ periods, className = '' }) => {
  const timelineRef = useRef<HTMLDivElement>(null)
  const lineRef = useRef<HTMLDivElement>(null)
  const itemRefs = useRef<(HTMLDivElement | null)[]>([])

  useEffect(() => {
    if (!timelineRef.current || !lineRef.current) return

    const timeline = timelineRef.current
    const line = lineRef.current
    const items = itemRefs.current.filter(Boolean)

    // Set initial states
    gsap.set(line, { scaleY: 0, transformOrigin: 'top center' })
    gsap.set(items, { opacity: 0, y: 50, scale: 0.9 })

    // Create main timeline
    gsap.timeline({
      scrollTrigger: {
        trigger: timeline,
        start: 'top 80%',
        end: 'bottom 20%',
        scrub: 1,
        onUpdate: (self) => {
          // Update line progress based on scroll
          gsap.to(line, {
            scaleY: self.progress,
            duration: 0.3,
            ease: 'none'
          })
        }
      }
    })

    // Animate each timeline item
    items.forEach((item, index) => {
      if (!item) return

      // Create individual scroll trigger for each item
      ScrollTrigger.create({
        trigger: item,
        start: 'top 85%',
        end: 'bottom 15%',
        onEnter: () => {
          gsap.to(item, {
            opacity: 1,
            y: 0,
            scale: 1,
            duration: 0.8,
            ease: 'power2.out',
            delay: index * 0.1
          })

          // Add a subtle pulse effect
          gsap.to(item.querySelector('.timeline-dot'), {
            scale: 1.2,
            duration: 0.3,
            ease: 'power2.out',
            yoyo: true,
            repeat: 1
          })
        },
        onLeave: () => {
          gsap.to(item, {
            opacity: 0.7,
            scale: 0.95,
            duration: 0.3,
            ease: 'power2.out'
          })
        },
        onEnterBack: () => {
          gsap.to(item, {
            opacity: 1,
            scale: 1,
            duration: 0.3,
            ease: 'power2.out'
          })
        },
        onLeaveBack: () => {
          gsap.to(item, {
            opacity: 0,
            y: 50,
            scale: 0.9,
            duration: 0.3,
            ease: 'power2.out'
          })
        }
      })

      // Add hover animations
      const handleMouseEnter = () => {
        gsap.to(item, {
          scale: 1.02,
          duration: 0.3,
          ease: 'power2.out'
        })
        gsap.to(item.querySelector('.timeline-content'), {
          y: -5,
          duration: 0.3,
          ease: 'power2.out'
        })
      }

      const handleMouseLeave = () => {
        gsap.to(item, {
          scale: 1,
          duration: 0.3,
          ease: 'power2.out'
        })
        gsap.to(item.querySelector('.timeline-content'), {
          y: 0,
          duration: 0.3,
          ease: 'power2.out'
        })
      }

      item.addEventListener('mouseenter', handleMouseEnter)
      item.addEventListener('mouseleave', handleMouseLeave)

      // Cleanup function for event listeners
      return () => {
        item.removeEventListener('mouseenter', handleMouseEnter)
        item.removeEventListener('mouseleave', handleMouseLeave)
      }
    })

    // Cleanup ScrollTriggers
    return () => {
      ScrollTrigger.getAll().forEach(trigger => {
        if (trigger.trigger === timeline || items.includes(trigger.trigger as HTMLDivElement)) {
          trigger.kill()
        }
      })
    }
  }, [periods])

  const getEraColor = (period: string) => {
    if (period.includes('BCE') || period.includes('Prehistoric')) return 'from-amber-400 to-orange-500'
    if (period.includes('1055') || period.includes('Anuradhapura')) return 'from-emerald-400 to-green-500'
    if (period.includes('1220') || period.includes('Dambadeniya')) return 'from-blue-400 to-indigo-500'
    if (period.includes('1412') || period.includes('Kotte')) return 'from-purple-400 to-violet-500'
    if (period.includes('1469') || period.includes('Kandyan')) return 'from-red-400 to-rose-500'
    if (period.includes('1505') || period.includes('Portuguese')) return 'from-yellow-400 to-amber-500'
    if (period.includes('1658') || period.includes('Dutch')) return 'from-cyan-400 to-blue-500'
    if (period.includes('1796') || period.includes('British')) return 'from-slate-400 to-gray-500'
    if (period.includes('1948') || period.includes('Independence')) return 'from-green-400 to-emerald-500'
    return 'from-gray-400 to-gray-500'
  }

  const getEraIcon = (period: string) => {
    if (period.includes('BCE') || period.includes('Prehistoric')) return '🏺'
    if (period.includes('Buddhism')) return '☸️'
    if (period.includes('Anuradhapura')) return '🏛️'
    if (period.includes('Polonnaruwa')) return '🗿'
    if (period.includes('Dambadeniya')) return '🏰'
    if (period.includes('Kotte')) return '👑'
    if (period.includes('Kandyan')) return '🦁'
    if (period.includes('Portuguese')) return '⛵'
    if (period.includes('Dutch')) return '🌷'
    if (period.includes('British')) return '🇬🇧'
    if (period.includes('Independence')) return '🇱🇰'
    return '📜'
  }

  return (
    <div ref={timelineRef} className={`relative ${className}`} data-testid="enhanced-timeline">
      {/* Animated timeline line - Mobile: left side, Desktop: center */}
      <div className="absolute left-6 md:left-1/2 transform md:-translate-x-1/2 w-1 h-full">
        <div
          ref={lineRef}
          className="w-full h-full bg-gradient-to-b from-orange-400 via-red-400 to-purple-500 rounded-full shadow-lg"
        />
      </div>

      {/* Timeline items */}
      <div className="space-y-8 md:space-y-16">
        {periods.map((period, index) => (
          <div
            key={period.id}
            ref={el => itemRefs.current[index] = el}
            className={`flex items-center ${
              // Mobile: always left-aligned, Desktop: alternating
              'flex-row md:' + (index % 2 === 0 ? 'flex-row' : 'flex-row-reverse')
            }`}
          >
            {/* Content - Mobile: full width with left margin, Desktop: half width */}
            <div className="w-full ml-16 md:ml-0 md:w-1/2 px-4 md:px-8">
              <motion.div
                className="timeline-content bg-white p-4 md:p-8 rounded-xl shadow-xl border border-gray-100 hover:shadow-2xl transition-all duration-300 relative overflow-hidden"
                whileHover={{ y: -5 }}
              >
                {/* Background gradient overlay */}
                <div className={`absolute inset-0 bg-gradient-to-br ${getEraColor(period.period)} opacity-5`} />
                
                {/* Era badge */}
                <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-4 space-y-2 sm:space-y-0">
                  <div className={`inline-flex items-center px-3 md:px-4 py-2 rounded-full bg-gradient-to-r ${getEraColor(period.period)} text-white text-xs md:text-sm font-semibold shadow-lg`}>
                    <span className="mr-2 text-base md:text-lg">{getEraIcon(period.period)}</span>
                    <span className="truncate">{period.period}</span>
                  </div>
                  <div className="text-xl md:text-2xl opacity-70 self-start sm:self-center">
                    {getEraIcon(period.period)}
                  </div>
                </div>

                {/* Title */}
                <h3 className="text-lg md:text-2xl font-bold text-gray-900 mb-3 md:mb-4 leading-tight">
                  {period.name}
                </h3>

                {/* Description */}
                <p className="text-sm md:text-base text-gray-600 mb-4 md:mb-6 leading-relaxed">
                  {period.description}
                </p>

                {/* Key Events */}
                <div className="mb-4 md:mb-6">
                  <h4 className="text-base md:text-lg font-semibold text-gray-900 mb-2 md:mb-3 flex items-center">
                    <span className="w-2 h-2 bg-orange-500 rounded-full mr-2"></span>
                    Key Events
                  </h4>
                  <ul className="space-y-1 md:space-y-2">
                    {period.keyEvents.slice(0, 3).map((event, idx) => (
                      <li key={idx} className="text-gray-600 text-xs md:text-sm flex items-start">
                        <span className="w-1.5 h-1.5 bg-orange-400 rounded-full mt-1.5 md:mt-2 mr-2 md:mr-3 flex-shrink-0"></span>
                        <span className="leading-relaxed">{event}</span>
                      </li>
                    ))}
                    {period.keyEvents.length > 3 && (
                      <li className="text-gray-500 text-xs md:text-sm italic">
                        +{period.keyEvents.length - 3} more events
                      </li>
                    )}
                  </ul>
                </div>

                {/* Cultural Impact */}
                <div className="mb-4 md:mb-6">
                  <h4 className="text-base md:text-lg font-semibold text-gray-900 mb-2 md:mb-3 flex items-center">
                    <span className="w-2 h-2 bg-purple-500 rounded-full mr-2"></span>
                    Cultural Impact
                  </h4>
                  <div className="flex flex-wrap gap-1 md:gap-2">
                    {period.culturalImpact.slice(0, 3).map((impact, idx) => (
                      <span
                        key={idx}
                        className="inline-block px-2 md:px-3 py-1 bg-purple-100 text-purple-700 text-xs rounded-full"
                      >
                        {impact}
                      </span>
                    ))}
                    {period.culturalImpact.length > 3 && (
                      <span className="inline-block px-2 md:px-3 py-1 bg-gray-100 text-gray-600 text-xs rounded-full">
                        +{period.culturalImpact.length - 3} more
                      </span>
                    )}
                  </div>
                </div>

                {/* Monuments */}
                <div>
                  <h4 className="text-base md:text-lg font-semibold text-gray-900 mb-2 md:mb-3 flex items-center">
                    <span className="w-2 h-2 bg-green-500 rounded-full mr-2"></span>
                    Notable Monuments
                  </h4>
                  <div className="flex flex-wrap gap-1 md:gap-2">
                    {period.monuments.slice(0, 3).map((monument, idx) => (
                      <span
                        key={idx}
                        className="inline-block px-2 md:px-3 py-1 bg-green-100 text-green-700 text-xs rounded-full"
                      >
                        {monument}
                      </span>
                    ))}
                    {period.monuments.length > 3 && (
                      <span className="inline-block px-2 md:px-3 py-1 bg-gray-100 text-gray-600 text-xs rounded-full">
                        +{period.monuments.length - 3} more
                      </span>
                    )}
                  </div>
                </div>
              </motion.div>
            </div>

            {/* Timeline dot - Mobile: absolute positioned on left line, Desktop: between content */}
            <div className="absolute left-6 md:relative md:left-auto z-10 transform -translate-x-1/2 md:transform-none">
              <div className={`timeline-dot w-4 h-4 md:w-6 md:h-6 bg-gradient-to-br ${getEraColor(period.period)} rounded-full shadow-lg border-2 md:border-4 border-white flex items-center justify-center`}>
                <div className="w-1 h-1 md:w-2 md:h-2 bg-white rounded-full"></div>
              </div>
            </div>

            {/* Spacer - Only on desktop */}
            <div className="hidden md:block md:w-1/2"></div>
          </div>
        ))}
      </div>
    </div>
  )
}

export default EnhancedTimeline
