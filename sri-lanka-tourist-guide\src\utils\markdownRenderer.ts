import { marked } from 'marked'
import DOMPurify from 'dompurify'

// Configure marked options for better rendering
marked.setOptions({
  breaks: true,
  gfm: true,
  headerIds: true,
  mangle: false,
})

// Custom renderer for better styling
const renderer = new marked.Renderer()

// Custom heading renderer with proper classes
renderer.heading = (text: string, level: number) => {
  // Ensure text is a string and handle edge cases
  const textString = typeof text === 'string' ? text : String(text || '')
  const escapedText = textString.toLowerCase().replace(/[^\w]+/g, '-')
  const classes = {
    1: 'text-4xl md:text-5xl font-bold text-gray-900 mb-8 mt-12',
    2: 'text-3xl md:text-4xl font-bold text-gray-900 mb-6 mt-10',
    3: 'text-2xl md:text-3xl font-bold text-gray-900 mb-4 mt-8',
    4: 'text-xl md:text-2xl font-bold text-gray-900 mb-3 mt-6',
    5: 'text-lg md:text-xl font-bold text-gray-900 mb-2 mt-4',
    6: 'text-base md:text-lg font-bold text-gray-900 mb-2 mt-4'
  }

  return `<h${level} id="${escapedText}" class="${classes[level as keyof typeof classes] || classes[6]}">${textString}</h${level}>`
}

// Custom paragraph renderer
renderer.paragraph = (text: string) => {
  const textString = typeof text === 'string' ? text : String(text || '')
  return `<p class="text-gray-700 leading-relaxed mb-6">${textString}</p>`
}

// Custom list renderer
renderer.list = (body: string, ordered: boolean) => {
  const tag = ordered ? 'ol' : 'ul'
  const classes = ordered 
    ? 'list-decimal list-inside space-y-2 mb-6 ml-4' 
    : 'list-disc list-inside space-y-2 mb-6 ml-4'
  return `<${tag} class="${classes}">${body}</${tag}>`
}

// Custom list item renderer
renderer.listitem = (text: string) => {
  const textString = typeof text === 'string' ? text : String(text || '')
  return `<li class="text-gray-700 leading-relaxed">${textString}</li>`
}

// Custom blockquote renderer
renderer.blockquote = (quote: string) => {
  return `<blockquote class="border-l-4 border-primary-500 pl-6 py-4 my-6 bg-primary-50 rounded-r-lg">
    <div class="text-gray-700 italic">${quote}</div>
  </blockquote>`
}

// Custom code block renderer
renderer.code = (code: string, language?: string) => {
  return `<pre class="bg-gray-100 rounded-lg p-4 mb-6 overflow-x-auto">
    <code class="text-sm text-gray-800">${code}</code>
  </pre>`
}

// Custom inline code renderer
renderer.codespan = (code: string) => {
  return `<code class="bg-gray-100 text-gray-800 px-2 py-1 rounded text-sm">${code}</code>`
}

// Custom link renderer
renderer.link = (href: string, title: string | null, text: string) => {
  const titleAttr = title ? ` title="${title}"` : ''
  // Safe check for window object and hostname
  const currentHostname = typeof window !== 'undefined' ? window.location.hostname : ''
  const isExternal = href.startsWith('http') && currentHostname && !href.includes(currentHostname)
  const target = isExternal ? ' target="_blank" rel="noopener noreferrer"' : ''

  return `<a href="${href}"${titleAttr}${target} class="text-primary-600 hover:text-primary-700 underline transition-colors">${text}</a>`
}

// Custom image renderer
renderer.image = (href: string, title: string | null, text: string) => {
  const titleAttr = title ? ` title="${title}"` : ''
  return `<figure class="my-8">
    <img src="${href}" alt="${text}"${titleAttr} class="w-full rounded-lg shadow-lg" loading="lazy" />
    ${title ? `<figcaption class="text-center text-gray-600 text-sm mt-2">${title}</figcaption>` : ''}
  </figure>`
}

// Custom table renderer
renderer.table = (header: string, body: string) => {
  return `<div class="overflow-x-auto my-6">
    <table class="min-w-full divide-y divide-gray-200 border border-gray-200 rounded-lg">
      <thead class="bg-gray-50">${header}</thead>
      <tbody class="bg-white divide-y divide-gray-200">${body}</tbody>
    </table>
  </div>`
}

// Custom table header renderer
renderer.tablerow = (content: string) => {
  return `<tr>${content}</tr>`
}

// Custom table cell renderer
renderer.tablecell = (content: string, flags: { header: boolean; align: string | null }) => {
  const tag = flags.header ? 'th' : 'td'
  const classes = flags.header 
    ? 'px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider'
    : 'px-6 py-4 whitespace-nowrap text-sm text-gray-900'
  const align = flags.align ? ` style="text-align: ${flags.align}"` : ''
  
  return `<${tag} class="${classes}"${align}>${content}</${tag}>`
}

// Custom horizontal rule renderer
renderer.hr = () => {
  return `<hr class="my-8 border-gray-300" />`
}

// Custom strong/bold renderer
renderer.strong = (text: string) => {
  const textString = typeof text === 'string' ? text : String(text || '')
  return `<strong class="font-bold text-gray-900">${textString}</strong>`
}

// Custom emphasis/italic renderer
renderer.em = (text: string) => {
  const textString = typeof text === 'string' ? text : String(text || '')
  return `<em class="italic text-gray-700">${textString}</em>`
}

// Set the custom renderer
marked.use({ renderer })

/**
 * Renders markdown content to HTML with proper styling
 * @param markdown - The markdown content to render
 * @returns Sanitized HTML string
 */
export const renderMarkdown = (markdown: string): string => {
  try {
    // Convert markdown to HTML
    const html = marked(markdown)
    
    // Sanitize the HTML to prevent XSS attacks
    const sanitizedHtml = DOMPurify.sanitize(html, {
      ALLOWED_TAGS: [
        'h1', 'h2', 'h3', 'h4', 'h5', 'h6',
        'p', 'br', 'strong', 'em', 'u', 's',
        'ul', 'ol', 'li',
        'blockquote',
        'a', 'img', 'figure', 'figcaption',
        'table', 'thead', 'tbody', 'tr', 'th', 'td',
        'pre', 'code',
        'hr', 'div', 'span'
      ],
      ALLOWED_ATTR: [
        'href', 'title', 'alt', 'src', 'class', 'id', 'style',
        'target', 'rel', 'loading'
      ],
      ALLOWED_URI_REGEXP: /^(?:(?:(?:f|ht)tps?|mailto|tel|callto|cid|xmpp|xxx):|[^a-z]|[a-z+.\-]+(?:[^a-z+.\-:]|$))/i
    })
    
    return sanitizedHtml
  } catch (error) {
    console.error('Error rendering markdown:', error)
    return '<p class="text-red-600">Error rendering content</p>'
  }
}

/**
 * Extracts the first paragraph from markdown content for excerpts
 * @param markdown - The markdown content
 * @returns Plain text excerpt
 */
export const extractExcerpt = (markdown: string, maxLength: number = 200): string => {
  try {
    // Remove markdown formatting and get plain text
    const plainText = markdown
      .replace(/#{1,6}\s+/g, '') // Remove headers
      .replace(/\*\*(.*?)\*\*/g, '$1') // Remove bold
      .replace(/\*(.*?)\*/g, '$1') // Remove italic
      .replace(/\[(.*?)\]\(.*?\)/g, '$1') // Remove links
      .replace(/`(.*?)`/g, '$1') // Remove inline code
      .replace(/\n+/g, ' ') // Replace newlines with spaces
      .trim()
    
    // Truncate to max length
    if (plainText.length <= maxLength) {
      return plainText
    }
    
    const truncated = plainText.substring(0, maxLength)
    const lastSpace = truncated.lastIndexOf(' ')
    
    return lastSpace > 0 
      ? truncated.substring(0, lastSpace) + '...'
      : truncated + '...'
  } catch (error) {
    console.error('Error extracting excerpt:', error)
    return 'Content preview unavailable'
  }
}

/**
 * Generates a table of contents from markdown headers
 * @param markdown - The markdown content
 * @returns Array of TOC items
 */
export interface TOCItem {
  id: string
  text: string
  level: number
}

export const generateTableOfContents = (markdown: string): TOCItem[] => {
  try {
    const headerRegex = /^(#{1,6})\s+(.+)$/gm
    const toc: TOCItem[] = []
    let match
    
    while ((match = headerRegex.exec(markdown)) !== null) {
      const level = match[1].length
      const text = match[2].trim()
      const id = text.toLowerCase().replace(/[^\w]+/g, '-')
      
      toc.push({ id, text, level })
    }
    
    return toc
  } catch (error) {
    console.error('Error generating table of contents:', error)
    return []
  }
}
