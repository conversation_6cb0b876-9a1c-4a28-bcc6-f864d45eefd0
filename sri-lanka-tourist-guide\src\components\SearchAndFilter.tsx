import React, { useState, useEffect } from 'react'
import { Search, Filter, X } from 'lucide-react'
import { Article } from '../data/articles'

interface SearchAndFilterProps {
  articles: Article[]
  onFilteredArticles: (articles: Article[]) => void
  className?: string
}

const categories = [
  'All Categories',
  'Destinations',
  'Practical Travel',
  'Cultural Heritage',
  'Wildlife & Nature',
  'Adventure & Activities',
  'Food & Culinary'
]

const difficulties = [
  'All Difficulties',
  'Easy',
  'Easy to Moderate',
  'Moderate',
  'Moderate to Challenging',
  'Challenging'
]

export const SearchAndFilter: React.FC<SearchAndFilterProps> = ({
  articles,
  onFilteredArticles,
  className = ''
}) => {
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedCategory, setSelectedCategory] = useState('All Categories')
  const [selectedDifficulty, setSelectedDifficulty] = useState('All Difficulties')
  const [showFilters, setShowFilters] = useState(false)

  useEffect(() => {
    let filtered = articles

    // Filter by search term
    if (searchTerm) {
      const searchLower = searchTerm.toLowerCase()
      filtered = filtered.filter(article =>
        article.title.toLowerCase().includes(searchLower) ||
        article.excerpt.toLowerCase().includes(searchLower) ||
        article.tags.some(tag => tag.toLowerCase().includes(searchLower)) ||
        (article.seoKeywords && article.seoKeywords.some(keyword => 
          keyword.toLowerCase().includes(searchLower)
        ))
      )
    }

    // Filter by category
    if (selectedCategory !== 'All Categories') {
      filtered = filtered.filter(article => article.category === selectedCategory)
    }

    // Filter by difficulty
    if (selectedDifficulty !== 'All Difficulties') {
      filtered = filtered.filter(article => article.difficulty === selectedDifficulty)
    }

    onFilteredArticles(filtered)
  }, [searchTerm, selectedCategory, selectedDifficulty, articles, onFilteredArticles])

  const clearFilters = () => {
    setSearchTerm('')
    setSelectedCategory('All Categories')
    setSelectedDifficulty('All Difficulties')
  }

  const hasActiveFilters = searchTerm || selectedCategory !== 'All Categories' || selectedDifficulty !== 'All Difficulties'

  return (
    <div className={`search-filter-container ${className}`}>
      {/* Search Bar */}
      <div className="search-bar-wrapper">
        <div className="search-input-container">
          <Search className="search-icon" size={20} />
          <input
            type="text"
            placeholder="Search articles, destinations, activities..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="search-input"
          />
          {searchTerm && (
            <button
              onClick={() => setSearchTerm('')}
              className="clear-search-btn"
              aria-label="Clear search"
            >
              <X size={16} />
            </button>
          )}
        </div>
        
        <button
          onClick={() => setShowFilters(!showFilters)}
          className={`filter-toggle-btn ${showFilters ? 'active' : ''}`}
          aria-label="Toggle filters"
        >
          <Filter size={20} />
          <span>Filters</span>
          {hasActiveFilters && <span className="filter-indicator" />}
        </button>
      </div>

      {/* Filter Panel */}
      <div className={`filter-panel ${showFilters ? 'show' : ''}`}>
        <div className="filter-content">
          <div className="filter-group">
            <label htmlFor="category-select" className="filter-label">
              Category
            </label>
            <select
              id="category-select"
              value={selectedCategory}
              onChange={(e) => setSelectedCategory(e.target.value)}
              className="filter-select"
            >
              {categories.map(category => (
                <option key={category} value={category}>
                  {category}
                </option>
              ))}
            </select>
          </div>

          <div className="filter-group">
            <label htmlFor="difficulty-select" className="filter-label">
              Difficulty
            </label>
            <select
              id="difficulty-select"
              value={selectedDifficulty}
              onChange={(e) => setSelectedDifficulty(e.target.value)}
              className="filter-select"
            >
              {difficulties.map(difficulty => (
                <option key={difficulty} value={difficulty}>
                  {difficulty}
                </option>
              ))}
            </select>
          </div>

          {hasActiveFilters && (
            <button
              onClick={clearFilters}
              className="clear-filters-btn"
            >
              <X size={16} />
              Clear All Filters
            </button>
          )}
        </div>
      </div>

      <style jsx>{`
        .search-filter-container {
          width: 100%;
          margin-bottom: 2rem;
        }

        .search-bar-wrapper {
          display: flex;
          gap: 1rem;
          align-items: center;
          margin-bottom: 1rem;
        }

        .search-input-container {
          flex: 1;
          position: relative;
          display: flex;
          align-items: center;
        }

        .search-icon {
          position: absolute;
          left: 1rem;
          color: #6b7280;
          z-index: 1;
        }

        .search-input {
          width: 100%;
          padding: 0.75rem 1rem 0.75rem 3rem;
          border: 2px solid #e5e7eb;
          border-radius: 0.75rem;
          font-size: 1rem;
          transition: all 0.3s ease;
          background: white;
        }

        .search-input:focus {
          outline: none;
          border-color: #3b82f6;
          box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        .clear-search-btn {
          position: absolute;
          right: 1rem;
          background: none;
          border: none;
          color: #6b7280;
          cursor: pointer;
          padding: 0.25rem;
          border-radius: 0.25rem;
          transition: color 0.2s ease;
        }

        .clear-search-btn:hover {
          color: #374151;
        }

        .filter-toggle-btn {
          display: flex;
          align-items: center;
          gap: 0.5rem;
          padding: 0.75rem 1.5rem;
          background: white;
          border: 2px solid #e5e7eb;
          border-radius: 0.75rem;
          cursor: pointer;
          transition: all 0.3s ease;
          font-weight: 500;
          position: relative;
        }

        .filter-toggle-btn:hover {
          border-color: #3b82f6;
          background: #f8fafc;
        }

        .filter-toggle-btn.active {
          border-color: #3b82f6;
          background: #eff6ff;
          color: #3b82f6;
        }

        .filter-indicator {
          position: absolute;
          top: -0.25rem;
          right: -0.25rem;
          width: 0.75rem;
          height: 0.75rem;
          background: #ef4444;
          border-radius: 50%;
          border: 2px solid white;
        }

        .filter-panel {
          overflow: hidden;
          transition: all 0.3s ease;
          max-height: 0;
          opacity: 0;
        }

        .filter-panel.show {
          max-height: 200px;
          opacity: 1;
        }

        .filter-content {
          padding: 1.5rem;
          background: #f8fafc;
          border-radius: 0.75rem;
          border: 1px solid #e2e8f0;
          display: flex;
          gap: 1.5rem;
          align-items: end;
          flex-wrap: wrap;
        }

        .filter-group {
          display: flex;
          flex-direction: column;
          gap: 0.5rem;
          min-width: 150px;
        }

        .filter-label {
          font-weight: 600;
          color: #374151;
          font-size: 0.875rem;
        }

        .filter-select {
          padding: 0.5rem 0.75rem;
          border: 1px solid #d1d5db;
          border-radius: 0.5rem;
          background: white;
          font-size: 0.875rem;
          cursor: pointer;
          transition: border-color 0.2s ease;
        }

        .filter-select:focus {
          outline: none;
          border-color: #3b82f6;
        }

        .clear-filters-btn {
          display: flex;
          align-items: center;
          gap: 0.5rem;
          padding: 0.5rem 1rem;
          background: #ef4444;
          color: white;
          border: none;
          border-radius: 0.5rem;
          cursor: pointer;
          font-size: 0.875rem;
          font-weight: 500;
          transition: background-color 0.2s ease;
          height: fit-content;
        }

        .clear-filters-btn:hover {
          background: #dc2626;
        }

        @media (max-width: 768px) {
          .search-bar-wrapper {
            flex-direction: column;
            gap: 0.75rem;
          }

          .filter-toggle-btn {
            width: 100%;
            justify-content: center;
          }

          .filter-content {
            flex-direction: column;
            align-items: stretch;
            gap: 1rem;
          }

          .filter-group {
            min-width: auto;
          }

          .clear-filters-btn {
            align-self: center;
          }
        }
      `}</style>
    </div>
  )
}

export default SearchAndFilter
