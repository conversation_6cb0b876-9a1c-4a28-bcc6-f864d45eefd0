import { test, expect } from '@playwright/test'

test.describe('Mobile Timeline Layout', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/culture')
    await page.waitForLoadState('networkidle')
  })

  test('should display timeline correctly on mobile viewport', async ({ page }) => {
    // Set mobile viewport
    await page.setViewportSize({ width: 375, height: 667 })
    
    // Wait for timeline to load
    await page.waitForSelector('[data-testid="enhanced-timeline"]', { timeout: 10000 })
    
    // Check that timeline line is positioned on the left for mobile
    const timelineLine = page.locator('.absolute.left-6')
    await expect(timelineLine).toBeVisible()
    
    // Check that timeline items are in single column layout
    const timelineItems = page.locator('.flex.items-center.flex-row')
    const count = await timelineItems.count()
    expect(count).toBeGreaterThan(0)
    
    // Verify content takes full width with left margin on mobile
    const contentDivs = page.locator('.w-full.ml-16')
    const contentCount = await contentDivs.count()
    expect(contentCount).toBeGreaterThan(0)
    
    // Check that timeline dots are positioned correctly on mobile
    const timelineDots = page.locator('.timeline-dot')
    const firstDot = timelineDots.first()
    await expect(firstDot).toBeVisible()
    
    // Verify mobile-specific styling
    const firstContent = page.locator('.timeline-content').first()
    await expect(firstContent).toHaveClass(/p-4/)
  })

  test('should display timeline correctly on desktop viewport', async ({ page }) => {
    // Set desktop viewport
    await page.setViewportSize({ width: 1280, height: 720 })
    
    // Wait for timeline to load
    await page.waitForSelector('[data-testid="enhanced-timeline"]', { timeout: 10000 })
    
    // Check that timeline line is centered for desktop
    const timelineLine = page.locator('.md\\:left-1\\/2')
    await expect(timelineLine).toBeVisible()
    
    // Check that content uses half width on desktop
    const contentDivs = page.locator('.md\\:w-1\\/2')
    const contentCount = await contentDivs.count()
    expect(contentCount).toBeGreaterThan(0)
    
    // Verify desktop-specific styling
    const firstContent = page.locator('.timeline-content').first()
    await expect(firstContent).toHaveClass(/md:p-8/)
    
    // Check alternating layout on desktop
    const timelineItems = page.locator('.flex.items-center')
    const firstItem = timelineItems.first()
    const secondItem = timelineItems.nth(1)
    
    // First item should be flex-row, second should be flex-row-reverse on desktop
    await expect(firstItem).toHaveClass(/md:flex-row/)
    await expect(secondItem).toHaveClass(/md:flex-row-reverse/)
  })

  test('should have responsive text sizes', async ({ page }) => {
    // Test mobile viewport
    await page.setViewportSize({ width: 375, height: 667 })
    
    const title = page.locator('h3').first()
    await expect(title).toHaveClass(/text-lg/)
    await expect(title).toHaveClass(/md:text-2xl/)
    
    const description = page.locator('.timeline-content p').first()
    await expect(description).toHaveClass(/text-sm/)
    await expect(description).toHaveClass(/md:text-base/)
  })

  test('should handle scroll-triggered animations on mobile', async ({ page }) => {
    await page.setViewportSize({ width: 375, height: 667 })
    
    // Scroll to trigger animations
    await page.evaluate(() => {
      window.scrollTo(0, 500)
    })
    
    await page.waitForTimeout(1000)
    
    // Check that timeline items become visible
    const timelineItems = page.locator('.timeline-content')
    const firstItem = timelineItems.first()
    
    // The item should be visible after scroll trigger
    await expect(firstItem).toBeVisible()
  })

  test('should maintain accessibility on mobile', async ({ page }) => {
    await page.setViewportSize({ width: 375, height: 667 })
    
    // Check that headings maintain proper hierarchy
    const headings = page.locator('h3, h4')
    const headingCount = await headings.count()
    expect(headingCount).toBeGreaterThan(0)
    
    // Verify text contrast and readability
    const textElements = page.locator('.text-gray-600')
    const firstText = textElements.first()
    await expect(firstText).toBeVisible()
    
    // Check that interactive elements are touch-friendly (minimum 44px)
    const timelineDots = page.locator('.timeline-dot')
    const firstDot = timelineDots.first()
    const boundingBox = await firstDot.boundingBox()
    
    if (boundingBox) {
      // On mobile, dots should be at least 16px (w-4 h-4)
      expect(boundingBox.width).toBeGreaterThanOrEqual(16)
      expect(boundingBox.height).toBeGreaterThanOrEqual(16)
    }
  })

  test('should handle content overflow properly on mobile', async ({ page }) => {
    await page.setViewportSize({ width: 320, height: 568 }) // Very small mobile
    
    // Check that content doesn't overflow horizontally
    const timelineContent = page.locator('.timeline-content').first()
    const boundingBox = await timelineContent.boundingBox()
    
    if (boundingBox) {
      // Content should not exceed viewport width minus margins
      expect(boundingBox.width).toBeLessThanOrEqual(320 - 32) // Account for margins
    }
    
    // Check that text wraps properly
    const longTexts = page.locator('.timeline-content p')
    const firstText = longTexts.first()
    await expect(firstText).toHaveCSS('word-wrap', 'break-word')
  })

  test('should display era badges responsively', async ({ page }) => {
    // Test mobile
    await page.setViewportSize({ width: 375, height: 667 })
    
    const eraBadge = page.locator('.rounded-full').first()
    await expect(eraBadge).toBeVisible()
    
    // Badge should have mobile-appropriate text size
    await expect(eraBadge).toHaveClass(/text-xs/)
    await expect(eraBadge).toHaveClass(/md:text-sm/)
    
    // Test desktop
    await page.setViewportSize({ width: 1280, height: 720 })
    
    // Badge should be visible and properly sized on desktop too
    await expect(eraBadge).toBeVisible()
  })

  test('should handle touch interactions on mobile', async ({ page }) => {
    await page.setViewportSize({ width: 375, height: 667 })
    
    // Simulate touch interaction on timeline item
    const firstTimelineItem = page.locator('.timeline-content').first()
    
    // Touch should not cause layout issues
    await firstTimelineItem.tap()
    await page.waitForTimeout(500)
    
    // Item should still be visible and properly positioned
    await expect(firstTimelineItem).toBeVisible()
  })
})
