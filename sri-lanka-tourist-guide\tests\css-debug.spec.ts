import { test, expect } from '@playwright/test'

test.describe('CSS Debug', () => {
  test('Debug mobile menu button visibility at different breakpoints', async ({ page }) => {
    await page.goto('/')
    await page.waitForLoadState('networkidle')
    
    // Test at different viewport sizes
    const viewports = [
      { width: 375, height: 667, name: 'mobile' },
      { width: 640, height: 480, name: 'sm' },
      { width: 768, height: 1024, name: 'md' },
      { width: 1024, height: 768, name: 'lg' },
      { width: 1280, height: 720, name: 'xl' }
    ]
    
    for (const viewport of viewports) {
      await page.setViewportSize(viewport)
      await page.waitForTimeout(300)
      
      const debugInfo = await page.evaluate(() => {
        const mobileButton = document.querySelector('.mobile-menu-button')
        const desktopNav = document.querySelector('nav[aria-label="Main navigation"]')
        
        if (!mobileButton || !desktopNav) {
          return { error: 'Elements not found' }
        }
        
        const mobileStyles = window.getComputedStyle(mobileButton)
        const desktopStyles = window.getComputedStyle(desktopNav)
        
        return {
          viewport: { width: window.innerWidth, height: window.innerHeight },
          mobileButton: {
            display: mobileStyles.display,
            visibility: mobileStyles.visibility,
            opacity: mobileStyles.opacity,
            classes: mobileButton.className,
            isVisible: mobileButton.offsetWidth > 0 && mobileButton.offsetHeight > 0
          },
          desktopNav: {
            display: desktopStyles.display,
            visibility: desktopStyles.visibility,
            opacity: desktopStyles.opacity,
            classes: desktopNav.className,
            isVisible: desktopNav.offsetWidth > 0 && desktopNav.offsetHeight > 0
          }
        }
      })
      
      console.log(`${viewport.name} (${viewport.width}x${viewport.height}):`, debugInfo)
      
      // At md breakpoint (768px) and above, mobile button should be hidden
      if (viewport.width >= 768) {
        expect(debugInfo.mobileButton.isVisible).toBe(false)
        expect(debugInfo.desktopNav.isVisible).toBe(true)
      } else {
        expect(debugInfo.mobileButton.isVisible).toBe(true)
        expect(debugInfo.desktopNav.isVisible).toBe(false)
      }
    }
  })

  test('Check Tailwind CSS classes are being applied correctly', async ({ page }) => {
    await page.goto('/')
    await page.waitForLoadState('networkidle')
    
    // Check if Tailwind CSS is loaded
    const tailwindCheck = await page.evaluate(() => {
      // Check if any Tailwind classes are working
      const testElement = document.createElement('div')
      testElement.className = 'hidden md:block'
      document.body.appendChild(testElement)
      
      const styles = window.getComputedStyle(testElement)
      const isHidden = styles.display === 'none'
      
      document.body.removeChild(testElement)
      
      return {
        isTailwindWorking: isHidden,
        stylesheets: Array.from(document.styleSheets).map(sheet => {
          try {
            return {
              href: sheet.href,
              rules: sheet.cssRules ? sheet.cssRules.length : 'Cannot access'
            }
          } catch (e) {
            return { href: sheet.href, error: e.message }
          }
        })
      }
    })
    
    console.log('Tailwind Check:', tailwindCheck)
    expect(tailwindCheck.isTailwindWorking).toBe(true)
  })

  test('Check specific responsive classes on mobile menu button', async ({ page }) => {
    await page.goto('/')
    await page.waitForLoadState('networkidle')
    
    await page.setViewportSize({ width: 768, height: 1024 })
    await page.waitForTimeout(300)
    
    const classCheck = await page.evaluate(() => {
      const mobileButton = document.querySelector('.mobile-menu-button')
      const parentDiv = mobileButton?.parentElement
      
      if (!mobileButton || !parentDiv) {
        return { error: 'Elements not found' }
      }
      
      const buttonStyles = window.getComputedStyle(mobileButton)
      const parentStyles = window.getComputedStyle(parentDiv)
      
      return {
        button: {
          classes: mobileButton.className,
          display: buttonStyles.display,
          visibility: buttonStyles.visibility,
          position: buttonStyles.position,
          width: buttonStyles.width,
          height: buttonStyles.height
        },
        parent: {
          classes: parentDiv.className,
          display: parentStyles.display,
          visibility: parentStyles.visibility
        },
        computedStyles: {
          buttonRect: mobileButton.getBoundingClientRect(),
          parentRect: parentDiv.getBoundingClientRect()
        }
      }
    })
    
    console.log('Class Check at 768px:', classCheck)
    
    // The parent div should have md:hidden class which should make it display: none at 768px
    expect(classCheck.parent.classes).toContain('md:hidden')
  })
})
