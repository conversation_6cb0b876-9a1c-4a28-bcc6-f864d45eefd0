/* Leaflet CSS */
@import 'leaflet/dist/leaflet.css';

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  html {
    scroll-behavior: smooth;
  }
  
  body {
    font-family: 'Inter', system-ui, sans-serif;
    line-height: 1.6;
    color: #1f2937;
  }
  
  h1, h2, h3, h4, h5, h6 {
    font-family: 'Playfair Display', serif;
    font-weight: 600;
    line-height: 1.2;
  }
}

@layer components {
  .btn-primary {
    @apply bg-primary-600 hover:bg-primary-700 text-white font-medium py-3 px-6 rounded-lg transition-all duration-300 transform hover:scale-105 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2;
  }
  
  .btn-secondary {
    @apply bg-white hover:bg-gray-50 text-primary-600 font-medium py-3 px-6 rounded-lg border-2 border-primary-600 transition-all duration-300 transform hover:scale-105 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2;
  }
  
  .card {
    @apply bg-white rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1;
  }
  
  .section-padding {
    @apply py-16 px-4 sm:px-6 lg:px-8;
  }
  
  .container-max {
    @apply max-w-7xl mx-auto;
  }
  
  .text-gradient {
    @apply bg-gradient-to-r from-primary-600 to-secondary-500 bg-clip-text text-transparent;
  }
  
  .hero-gradient {
    @apply bg-gradient-to-br from-primary-50 via-white to-secondary-50;
  }
  
  .overlay-gradient {
    @apply bg-gradient-to-t from-black/60 via-black/20 to-transparent;
  }
}

@layer utilities {
  .animation-delay-200 {
    animation-delay: 200ms;
  }
  
  .animation-delay-400 {
    animation-delay: 400ms;
  }
  
  .animation-delay-600 {
    animation-delay: 600ms;
  }
  
  .animation-delay-800 {
    animation-delay: 800ms;
  }
  
  .animation-delay-1000 {
    animation-delay: 1000ms;
  }
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f5f9;
}

::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* Loading animation */
.loading-spinner {
  border: 3px solid #f3f4f6;
  border-top: 3px solid #3b82f6;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* GSAP animations will be applied via JavaScript */
.gsap-fade-in {
  opacity: 0;
  transform: translateY(30px);
}

.gsap-slide-left {
  opacity: 0;
  transform: translateX(-50px);
}

.gsap-slide-right {
  opacity: 0;
  transform: translateX(50px);
}

.gsap-scale-in {
  opacity: 0;
  transform: scale(0.8);
}

/* Loading spinner */
.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f4f6;
  border-top: 4px solid #8b5cf6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Leaflet Map Customizations */
.leaflet-map-container {
  height: 100% !important;
  width: 100% !important;
  min-height: 400px !important;
}

.map-container {
  height: 500px;
  min-height: 400px;
  width: 100%;
  position: relative;
}

.map-container-full {
  height: 100%;
  min-height: 400px;
  width: 100%;
  position: relative;
}

.map-container-dynamic {
  min-height: 400px;
  width: 100%;
  position: relative;
  display: block;
  overflow: hidden;
}

/* Specific height classes for different map sizes */
.map-height-500 {
  height: 500px !important;
}

.map-height-400 {
  height: 400px !important;
}

.map-height-600 {
  height: 600px !important;
}

/* Ensure map container has proper dimensions */
.leaflet-container {
  font-family: 'Inter', system-ui, sans-serif;
  height: 100% !important;
  width: 100% !important;
  z-index: 1;
}

/* Fix for map display issues */
.leaflet-map-pane {
  z-index: 2;
}

.leaflet-tile-pane {
  z-index: 1;
}

.leaflet-popup-content-wrapper {
  border-radius: 8px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
}

.leaflet-popup-content {
  margin: 0;
  line-height: 1.5;
}

.custom-popup .leaflet-popup-content-wrapper {
  background: white;
  border-radius: 12px;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

.custom-popup .leaflet-popup-tip {
  background: white;
}

/* Map controls styling */
.leaflet-control-zoom a {
  background-color: white;
  border: 1px solid #e5e7eb;
  color: #374151;
}

.leaflet-control-zoom a:hover {
  background-color: #f9fafb;
  border-color: #d1d5db;
}

/* Fix for map tiles not loading properly */
.leaflet-tile-container {
  pointer-events: auto;
}

.leaflet-tile {
  pointer-events: auto;
}

/* Ensure map container has proper dimensions and display */
.leaflet-container {
  background: #f8f9fa;
  outline: none;
}

.leaflet-map-container .leaflet-container {
  height: 100% !important;
  width: 100% !important;
  position: relative;
  z-index: 1;
}

/* Fix for map not displaying properly */
.map-container-dynamic .leaflet-container {
  height: 100% !important;
  min-height: 400px !important;
}

/* Article Content Styling */
.article-content {
  line-height: 1.8;
}

.article-content h1,
.article-content h2,
.article-content h3,
.article-content h4,
.article-content h5,
.article-content h6 {
  scroll-margin-top: 100px; /* Account for sticky header */
}

.article-content blockquote {
  position: relative;
}

.article-content blockquote::before {
  content: '"';
  position: absolute;
  left: -0.5rem;
  top: -0.5rem;
  font-size: 3rem;
  color: #8b5cf6;
  font-family: serif;
  opacity: 0.3;
}

.article-content pre {
  font-family: 'Fira Code', 'Monaco', 'Consolas', monospace;
}

.article-content table {
  font-size: 0.9rem;
}

.article-content img {
  transition: transform 0.3s ease;
}

.article-content img:hover {
  transform: scale(1.02);
}

/* Smooth scrolling for anchor links */
html {
  scroll-behavior: smooth;
}

/* Line clamp utilities */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
