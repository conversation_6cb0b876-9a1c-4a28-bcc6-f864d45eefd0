import { test, expect } from '@playwright/test'

test.describe('Responsive Layout Enhancement Verification', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/')
    await page.waitForLoadState('networkidle')
  })

  test('Mobile-first responsive design verification', async ({ page }) => {
    const viewports = [
      { width: 320, height: 568, name: 'iPhone SE' },
      { width: 375, height: 667, name: 'iPhone 8' },
      { width: 414, height: 896, name: 'iPhone 11 Pro Max' },
      { width: 768, height: 1024, name: 'iPad' },
      { width: 1024, height: 768, name: 'iPad Landscape' },
      { width: 1280, height: 720, name: 'Desktop' },
      { width: 1920, height: 1080, name: 'Large Desktop' }
    ]

    for (const viewport of viewports) {
      await page.setViewportSize(viewport)
      await page.waitForTimeout(300)

      // Check for horizontal scrollbars (indicates overflow)
      const hasHorizontalScroll = await page.evaluate(() => {
        return document.documentElement.scrollWidth > document.documentElement.clientWidth
      })

      // Check that content fits within viewport
      const contentFitsViewport = await page.evaluate(() => {
        const elements = Array.from(document.querySelectorAll('*'))
        const overflowingElements = elements.filter(el => {
          const rect = el.getBoundingClientRect()
          return rect.right > window.innerWidth || rect.left < 0
        })
        return {
          totalElements: elements.length,
          overflowingCount: overflowingElements.length,
          overflowingElements: overflowingElements.slice(0, 5).map(el => ({
            tag: el.tagName,
            class: el.className,
            width: el.getBoundingClientRect().width,
            right: el.getBoundingClientRect().right
          }))
        }
      })

      console.log(`${viewport.name} (${viewport.width}x${viewport.height}):`, {
        hasHorizontalScroll,
        contentFitsViewport: contentFitsViewport.overflowingCount
      })

      // Assertions
      expect(hasHorizontalScroll).toBe(false)
      expect(contentFitsViewport.overflowingCount).toBeLessThan(3) // Allow minimal tolerance
    }
  })

  test('Touch-friendly interface on mobile devices', async ({ page }) => {
    await page.setViewportSize({ width: 375, height: 667 })
    
    // Check touch target sizes (minimum 44px as per WCAG guidelines)
    const touchTargets = await page.evaluate(() => {
      const interactiveElements = document.querySelectorAll('button, a, input, select, textarea, [role="button"]')
      const smallTargets: any[] = []
      
      interactiveElements.forEach(el => {
        const rect = el.getBoundingClientRect()
        const isVisible = rect.width > 0 && rect.height > 0
        const isTooSmall = (rect.width < 44 || rect.height < 44) && isVisible
        
        if (isTooSmall) {
          smallTargets.push({
            tag: el.tagName,
            class: el.className,
            width: rect.width,
            height: rect.height,
            text: el.textContent?.slice(0, 30)
          })
        }
      })
      
      return {
        totalInteractive: interactiveElements.length,
        smallTargets: smallTargets.slice(0, 10)
      }
    })

    console.log('Touch Target Analysis:', touchTargets)
    
    // Most interactive elements should meet touch target size guidelines
    expect(touchTargets.smallTargets.length).toBeLessThan(5)
  })

  test('Typography and readability on different screen sizes', async ({ page }) => {
    const viewports = [
      { width: 375, height: 667, name: 'mobile' },
      { width: 768, height: 1024, name: 'tablet' },
      { width: 1920, height: 1080, name: 'desktop' }
    ]

    for (const viewport of viewports) {
      await page.setViewportSize(viewport)
      await page.waitForTimeout(300)

      const typographyCheck = await page.evaluate(() => {
        const headings = document.querySelectorAll('h1, h2, h3, h4, h5, h6')
        const paragraphs = document.querySelectorAll('p')
        
        const getReadableSize = (element: Element) => {
          const styles = window.getComputedStyle(element)
          return {
            fontSize: parseFloat(styles.fontSize),
            lineHeight: parseFloat(styles.lineHeight) || parseFloat(styles.fontSize) * 1.2
          }
        }

        const headingSizes = Array.from(headings).map(h => getReadableSize(h))
        const paragraphSizes = Array.from(paragraphs).map(p => getReadableSize(p))

        return {
          headingCount: headings.length,
          paragraphCount: paragraphs.length,
          minHeadingSize: Math.min(...headingSizes.map(h => h.fontSize)),
          minParagraphSize: Math.min(...paragraphSizes.map(p => p.fontSize)),
          avgLineHeight: paragraphSizes.reduce((sum, p) => sum + p.lineHeight, 0) / paragraphSizes.length
        }
      })

      console.log(`Typography ${viewport.name}:`, typographyCheck)

      // Typography should be readable on all devices
      expect(typographyCheck.minHeadingSize).toBeGreaterThan(16) // Minimum 16px for headings
      expect(typographyCheck.minParagraphSize).toBeGreaterThan(14) // Minimum 14px for body text
      expect(typographyCheck.avgLineHeight).toBeGreaterThan(16) // Adequate line height
    }
  })

  test('Image responsiveness and optimization', async ({ page }) => {
    const viewports = [
      { width: 375, height: 667 },
      { width: 768, height: 1024 },
      { width: 1920, height: 1080 }
    ]

    for (const viewport of viewports) {
      await page.setViewportSize(viewport)
      await page.waitForTimeout(300)

      const imageCheck = await page.evaluate(() => {
        const images = document.querySelectorAll('img')
        const issues: any[] = []

        images.forEach((img, index) => {
          const rect = img.getBoundingClientRect()
          const naturalWidth = (img as HTMLImageElement).naturalWidth
          const naturalHeight = (img as HTMLImageElement).naturalHeight

          // Check if image overflows container
          if (rect.width > window.innerWidth) {
            issues.push({
              index,
              issue: 'Image wider than viewport',
              width: rect.width,
              viewportWidth: window.innerWidth
            })
          }

          // Check if image has proper responsive attributes
          const hasResponsiveClass = img.className.includes('w-full') || 
                                   img.className.includes('max-w') ||
                                   img.className.includes('responsive')
          
          if (!hasResponsiveClass && naturalWidth > 800) {
            issues.push({
              index,
              issue: 'Large image without responsive classes',
              naturalWidth,
              className: img.className
            })
          }
        })

        return {
          totalImages: images.length,
          issues: issues.slice(0, 5)
        }
      })

      console.log(`Images ${viewport.width}px:`, imageCheck)
      
      // Images should not cause layout issues
      expect(imageCheck.issues.filter(i => i.issue === 'Image wider than viewport').length).toBe(0)
    }
  })

  test('Form elements responsiveness', async ({ page }) => {
    // Navigate to a page with forms (if any)
    const hasContactForm = await page.locator('form, input, textarea, select').count() > 0
    
    if (hasContactForm) {
      await page.setViewportSize({ width: 375, height: 667 })
      
      const formCheck = await page.evaluate(() => {
        const formElements = document.querySelectorAll('input, textarea, select, button[type="submit"]')
        const issues: any[] = []

        formElements.forEach((element, index) => {
          const rect = element.getBoundingClientRect()
          const styles = window.getComputedStyle(element)

          // Check if form element is too wide for mobile
          if (rect.width > window.innerWidth - 32) { // Allow 16px margin on each side
            issues.push({
              index,
              tag: element.tagName,
              width: rect.width,
              issue: 'Form element too wide for mobile'
            })
          }

          // Check if form element has adequate height for touch
          if (rect.height < 44 && element.tagName !== 'INPUT') {
            issues.push({
              index,
              tag: element.tagName,
              height: rect.height,
              issue: 'Form element too short for touch interaction'
            })
          }
        })

        return {
          totalFormElements: formElements.length,
          issues: issues
        }
      })

      console.log('Form Responsiveness:', formCheck)
      expect(formCheck.issues.length).toBeLessThan(3)
    }
  })

  test('Content hierarchy and spacing on mobile', async ({ page }) => {
    await page.setViewportSize({ width: 375, height: 667 })
    
    const spacingCheck = await page.evaluate(() => {
      const sections = document.querySelectorAll('section, article, .content-section')
      const spacingIssues: any[] = []

      sections.forEach((section, index) => {
        const rect = section.getBoundingClientRect()
        const styles = window.getComputedStyle(section)
        
        const paddingTop = parseFloat(styles.paddingTop)
        const paddingBottom = parseFloat(styles.paddingBottom)
        const marginTop = parseFloat(styles.marginTop)
        const marginBottom = parseFloat(styles.marginBottom)

        // Check for adequate spacing between sections
        const totalVerticalSpacing = paddingTop + paddingBottom + marginTop + marginBottom
        if (totalVerticalSpacing < 16) {
          spacingIssues.push({
            index,
            totalSpacing: totalVerticalSpacing,
            issue: 'Insufficient vertical spacing'
          })
        }

        // Check for adequate horizontal padding on mobile
        const paddingLeft = parseFloat(styles.paddingLeft)
        const paddingRight = parseFloat(styles.paddingRight)
        if (paddingLeft < 16 || paddingRight < 16) {
          spacingIssues.push({
            index,
            paddingLeft,
            paddingRight,
            issue: 'Insufficient horizontal padding for mobile'
          })
        }
      })

      return {
        totalSections: sections.length,
        spacingIssues: spacingIssues.slice(0, 5)
      }
    })

    console.log('Mobile Spacing Check:', spacingCheck)
    expect(spacingCheck.spacingIssues.length).toBeLessThan(spacingCheck.totalSections * 0.3) // Allow 30% tolerance
  })

  test('Performance impact of responsive design', async ({ page }) => {
    // Test different viewport sizes for performance
    const viewports = [
      { width: 375, height: 667, name: 'mobile' },
      { width: 1920, height: 1080, name: 'desktop' }
    ]

    for (const viewport of viewports) {
      await page.setViewportSize(viewport)
      
      const startTime = Date.now()
      await page.reload()
      await page.waitForLoadState('networkidle')
      const loadTime = Date.now() - startTime

      // Check for layout shifts during load
      const layoutShifts = await page.evaluate(() => {
        return new Promise((resolve) => {
          let cumulativeShift = 0
          const observer = new PerformanceObserver((list) => {
            for (const entry of list.getEntries()) {
              if (entry.entryType === 'layout-shift' && !(entry as any).hadRecentInput) {
                cumulativeShift += (entry as any).value
              }
            }
          })
          observer.observe({ entryTypes: ['layout-shift'] })
          
          setTimeout(() => {
            observer.disconnect()
            resolve(cumulativeShift)
          }, 2000)
        })
      })

      console.log(`Performance ${viewport.name}:`, {
        loadTime,
        layoutShifts
      })

      // Performance should be reasonable
      expect(loadTime).toBeLessThan(5000) // 5 second max load time
      expect(layoutShifts).toBeLessThan(0.1) // CLS should be < 0.1
    }
  })

  test('Cross-browser responsive compatibility', async ({ page, browserName }) => {
    await page.setViewportSize({ width: 375, height: 667 })
    
    // Test CSS Grid and Flexbox support
    const cssSupport = await page.evaluate(() => {
      const testElement = document.createElement('div')
      testElement.style.display = 'grid'
      const supportsGrid = testElement.style.display === 'grid'
      
      testElement.style.display = 'flex'
      const supportsFlex = testElement.style.display === 'flex'
      
      return {
        grid: supportsGrid,
        flexbox: supportsFlex,
        userAgent: navigator.userAgent
      }
    })

    console.log(`CSS Support in ${browserName}:`, cssSupport)
    
    // Modern browsers should support these features
    expect(cssSupport.flexbox).toBe(true)
    expect(cssSupport.grid).toBe(true)
  })
})
