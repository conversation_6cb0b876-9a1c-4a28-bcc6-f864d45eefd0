import React, { useState } from 'react'
import { Helmet } from 'react-helmet-async'
import { motion } from 'framer-motion'
import {
  InformationCircleIcon,
  CurrencyDollarIcon,
  ShieldCheckIcon,
  MapPinIcon,
  SunIcon,
  HeartIcon,
  ExclamationTriangleIcon
} from '@heroicons/react/24/outline'
import AnimatedSection from '@/components/AnimatedSection'
import FAQ, { sriLankaTravelFAQs } from '@/components/FAQ'

const TravelTips: React.FC = () => {
  const [activeTab, setActiveTab] = useState('planning')

  const tabs = [
    { id: 'planning', label: 'Planning', icon: MapPinIcon },
    { id: 'budget', label: 'Budget', icon: CurrencyDollarIcon },
    { id: 'safety', label: 'Safety', icon: ShieldCheckIcon },
    { id: 'health', label: 'Health', icon: HeartIcon },
    { id: 'culture', label: 'Culture', icon: InformationCircleIcon },
    { id: 'weather', label: 'Weather', icon: SunIcon }
  ]

  const tipsData = {
    planning: {
      title: 'Planning Your Trip',
      tips: [
        {
          title: 'Best Time to Visit Sri Lanka',
          content: 'December to March: West and south coasts (Colombo, Galle, Mirissa). April to September: East coast (Arugam Bay, Trincomalee). Hill country (Kandy, Ella, Nuwara Eliya) is pleasant year-round but best December-April.',
          icon: '📅'
        },
        {
          title: 'Visa Requirements 2025',
          content: 'Electronic Travel Authorization (ETA) required for most nationalities. Apply online at eta.gov.lk. Cost: USD 50 for 30 days. Processing: 24-72 hours. Passport must be valid for 6 months.',
          icon: '📋'
        },
        {
          title: 'Recommended Duration',
          content: 'Minimum 7-10 days for highlights (Colombo, Kandy, Sigiriya, Galle). 2-3 weeks ideal for comprehensive exploration including hill country, east coast, and cultural triangle.',
          icon: '⏰'
        },
        {
          title: 'Transportation Options',
          content: 'Trains: Scenic routes (Colombo-Kandy-Ella). Buses: Budget-friendly, extensive network. Tuk-tuks: Short distances, negotiate fare. Private drivers: Comfortable, USD 40-60/day. Domestic flights: Colombo-Jaffna.',
          icon: '🚂'
        },
        {
          title: 'Entry Points',
          content: 'Bandaranaike International Airport (CMB) - main gateway. Mattala Rajapaksa International Airport (HRI) - southern region. Colombo Port for cruise ships. Land borders with India (currently restricted).',
          icon: '✈️'
        }
      ]
    },
    budget: {
      title: 'Budget Guidelines 2025',
      tips: [
        {
          title: 'Daily Budget Breakdown',
          content: 'Budget backpacker: $25-40/day (dorms, local food, buses). Mid-range: $60-100/day (private rooms, mix of local/tourist restaurants, some tours). Luxury: $200-500+/day (resorts, fine dining, private transport).',
          icon: '💰'
        },
        {
          title: 'Accommodation Costs',
          content: 'Hostels/guesthouses: $8-25/night. Mid-range hotels: $40-100/night. Luxury resorts: $150-800+/night. Homestays: $15-35/night. Beach resorts peak season: +50-100% premium.',
          icon: '🏨'
        },
        {
          title: 'Food & Dining Costs',
          content: 'Street food: $1-3. Local restaurants: $3-8. Tourist restaurants: $10-20. Hotel dining: $15-40. Rice & curry: $2-5. Fresh seafood: $8-25. Alcohol: Beer $2-4, Wine $15-30.',
          icon: '🍽️'
        },
        {
          title: 'Transportation Costs',
          content: 'Local buses: $0.20-1. Train tickets: $1-8 (2nd/3rd class), $15-25 (1st class). Tuk-tuks: $0.30/km. Private driver: $40-80/day. Domestic flights: $80-150.',
          icon: '🚗'
        },
        {
          title: 'Money & Banking',
          content: 'Currency: Sri Lankan Rupee (LKR). ATMs widely available in cities. Credit cards accepted in hotels, restaurants, shops. Carry cash for small vendors, tuk-tuks, entrance fees. USD/EUR easily exchanged.',
          icon: '💳'
        }
      ]
    },
    safety: {
      title: 'Safety & Security',
      tips: [
        {
          title: 'General Safety',
          content: 'Sri Lanka is generally safe. Use common sense, avoid isolated areas at night, keep valuables secure.',
          icon: '🛡️'
        },
        {
          title: 'Transportation Safety',
          content: 'Choose reputable transport operators, wear seatbelts, be cautious with tuk-tuks in traffic.',
          icon: '🚗'
        },
        {
          title: 'Water Safety',
          content: 'Strong currents on some beaches. Swim at lifeguarded beaches, follow local advice.',
          icon: '🏊‍♂️'
        },
        {
          title: 'Emergency Numbers',
          content: 'Police: 119, Fire/Ambulance: 110, Tourist Police: 1912, Tourist Hotline: 1912.',
          icon: '📞'
        }
      ]
    },
    health: {
      title: 'Health & Medical',
      tips: [
        {
          title: 'Vaccinations',
          content: 'Routine vaccines up to date. Consider Hepatitis A/B, Typhoid, Japanese Encephalitis for some areas.',
          icon: '💉'
        },
        {
          title: 'Malaria Prevention',
          content: 'Low risk in most tourist areas. Use mosquito repellent, especially during dawn/dusk.',
          icon: '🦟'
        },
        {
          title: 'Food & Water',
          content: 'Drink bottled water, eat at busy restaurants, avoid street food initially until acclimatized.',
          icon: '🥤'
        },
        {
          title: 'Medical Care',
          content: 'Good private hospitals in Colombo and major cities. Travel insurance strongly recommended.',
          icon: '🏥'
        }
      ]
    },
    culture: {
      title: 'Cultural Etiquette',
      tips: [
        {
          title: 'Temple Visits',
          content: 'Remove shoes and hats, dress modestly, no photos of Buddha statues, maintain respectful silence.',
          icon: '🏛️'
        },
        {
          title: 'Dress Code',
          content: 'Conservative dress, especially at religious sites. Cover shoulders and knees, avoid tight clothing.',
          icon: '👕'
        },
        {
          title: 'Social Customs',
          content: 'Use right hand for eating/greeting, remove shoes when entering homes, respect elders.',
          icon: '🤝'
        },
        {
          title: 'Photography',
          content: 'Ask permission before photographing people, no photos at military installations or checkpoints.',
          icon: '📸'
        }
      ]
    },
    weather: {
      title: 'Weather & Climate',
      tips: [
        {
          title: 'Monsoon Seasons',
          content: 'Southwest monsoon (May-September), Northeast monsoon (October-January). Plan accordingly.',
          icon: '🌧️'
        },
        {
          title: 'Temperature',
          content: 'Coastal areas: 26-30°C, Hill country: 15-20°C, Hot and humid year-round in lowlands.',
          icon: '🌡️'
        },
        {
          title: 'What to Pack',
          content: 'Light cotton clothes, rain jacket, warm layers for hills, sun protection, comfortable shoes.',
          icon: '🎒'
        },
        {
          title: 'Seasonal Activities',
          content: 'Whale watching (Dec-Apr), Surfing varies by coast, Tea country best in dry season.',
          icon: '🏄‍♂️'
        }
      ]
    }
  }

  const essentialItems = [
    'Passport & visa documents',
    'Travel insurance',
    'Sunscreen (SPF 30+)',
    'Insect repellent',
    'First aid kit',
    'Comfortable walking shoes',
    'Light rain jacket',
    'Power adapter (Type D/G)',
    'Portable charger',
    'Cash in small denominations'
  ]

  return (
    <>
      <Helmet>
        <title>Sri Lanka Travel Tips 2025 - Essential Guide</title>
        <meta name="description" content="Complete Sri Lanka travel tips 2025: visa requirements, budget planning, safety advice, best time to visit, transportation, cultural etiquette, health tips, and insider recommendations for your perfect trip." />
        <meta name="keywords" content="Sri Lanka travel tips 2025, Sri Lanka visa requirements, Sri Lanka budget travel, Sri Lanka safety tips, best time visit Sri Lanka, Sri Lanka transportation, Sri Lanka cultural etiquette" />

        {/* Open Graph */}
        <meta property="og:title" content="Sri Lanka Travel Tips 2025 - Essential Guide for Visitors" />
        <meta property="og:description" content="Complete travel tips for Sri Lanka including visa, budget, safety, transportation, and cultural advice for 2025." />
        <meta property="og:type" content="article" />
        <meta property="og:url" content="https://srilankaguide.com/travel-tips" />
        <meta property="og:image" content="https://srilankaguide.com/images/travel-tips-hero.jpg" />

        {/* Twitter */}
        <meta name="twitter:card" content="summary_large_image" />
        <meta name="twitter:title" content="Sri Lanka Travel Tips 2025 - Essential Guide" />
        <meta name="twitter:description" content="Complete travel tips for Sri Lanka including visa, budget, safety, and cultural advice." />

        {/* Canonical */}
        <link rel="canonical" href="https://srilankaguide.com/travel-tips" />

        {/* Structured Data */}
        <script type="application/ld+json">
          {JSON.stringify({
            "@context": "https://schema.org",
            "@type": "Article",
            "headline": "Sri Lanka Travel Tips 2025 - Essential Guide for Visitors",
            "description": "Complete travel tips for Sri Lanka including visa requirements, budget planning, safety advice, and cultural etiquette.",
            "author": {
              "@type": "Organization",
              "name": "Sri Lanka Tourist Guide"
            },
            "publisher": {
              "@type": "Organization",
              "name": "Sri Lanka Tourist Guide",
              "logo": {
                "@type": "ImageObject",
                "url": "https://srilankaguide.com/logo.png"
              }
            },
            "datePublished": "2025-01-21",
            "dateModified": "2025-01-21"
          })}
        </script>
      </Helmet>

      {/* Hero Section */}
      <section className="bg-gradient-to-br from-green-50 to-blue-50 section-padding">
        <div className="container-max">
          <AnimatedSection animation="fadeIn" className="text-center mb-12">
            <h1 className="text-4xl md:text-6xl font-bold text-gray-900 mb-6">
              Travel <span className="text-gradient">Tips</span>
            </h1>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Everything you need to know for a safe, comfortable, and memorable trip to Sri Lanka.
            </p>
          </AnimatedSection>
        </div>
      </section>

      {/* Tabbed Content */}
      <section className="section-padding bg-white">
        <div className="container-max">
          {/* Tab Navigation */}
          <AnimatedSection animation="slideUp" className="mb-12">
            <div className="flex flex-wrap justify-center gap-2 mb-8">
              {tabs.map((tab) => (
                <button
                  key={tab.id}
                  type="button"
                  onClick={() => setActiveTab(tab.id)}
                  className={`flex items-center px-4 py-2 rounded-lg font-medium transition-all duration-200 ${
                    activeTab === tab.id
                      ? 'bg-primary-600 text-white shadow-lg'
                      : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                  }`}
                >
                  <tab.icon className="h-5 w-5 mr-2" />
                  {tab.label}
                </button>
              ))}
            </div>
          </AnimatedSection>

          {/* Tab Content */}
          <AnimatedSection animation="fadeIn" key={activeTab}>
            <div className="max-w-4xl mx-auto">
              <h2 className="text-3xl font-bold text-gray-900 mb-8 text-center">
                {tipsData[activeTab as keyof typeof tipsData].title}
              </h2>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {tipsData[activeTab as keyof typeof tipsData].tips.map((tip, index) => (
                  <motion.div
                    key={index}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.5, delay: index * 0.1 }}
                    className="card p-6"
                  >
                    <div className="flex items-start">
                      <div className="text-3xl mr-4 flex-shrink-0">{tip.icon}</div>
                      <div>
                        <h3 className="text-lg font-semibold text-gray-900 mb-2">
                          {tip.title}
                        </h3>
                        <p className="text-gray-600 leading-relaxed">
                          {tip.content}
                        </p>
                      </div>
                    </div>
                  </motion.div>
                ))}
              </div>
            </div>
          </AnimatedSection>
        </div>
      </section>

      {/* Essential Packing List */}
      <section className="section-padding bg-gray-50">
        <div className="container-max">
          <AnimatedSection animation="slideUp" className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
              Essential Packing List
            </h2>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              Don't forget these important items for your Sri Lankan adventure.
            </p>
          </AnimatedSection>

          <AnimatedSection animation="stagger" staggerDelay={0.1}>
            <div className="max-w-3xl mx-auto">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {essentialItems.map((item, index) => (
                  <motion.div
                    key={index}
                    whileHover={{ scale: 1.02 }}
                    className="flex items-center p-4 bg-white rounded-lg shadow-sm hover:shadow-md transition-all duration-200"
                  >
                    <div className="w-6 h-6 bg-green-500 rounded-full flex items-center justify-center mr-3 flex-shrink-0">
                      <svg className="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                      </svg>
                    </div>
                    <span className="text-gray-700 font-medium">{item}</span>
                  </motion.div>
                ))}
              </div>
            </div>
          </AnimatedSection>
        </div>
      </section>

      {/* Emergency Information */}
      <section className="section-padding bg-red-50">
        <div className="container-max">
          <AnimatedSection animation="slideUp" className="text-center mb-12">
            <div className="flex items-center justify-center mb-4">
              <ExclamationTriangleIcon className="h-8 w-8 text-red-600 mr-2" />
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900">
                Emergency Information
              </h2>
            </div>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              Important contacts and information for emergencies during your stay.
            </p>
          </AnimatedSection>

          <AnimatedSection animation="stagger" staggerDelay={0.2}>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {[
                { service: 'Police', number: '119', icon: '🚔' },
                { service: 'Fire & Ambulance', number: '110', icon: '🚑' },
                { service: 'Tourist Police', number: '1912', icon: '👮‍♂️' },
                { service: 'Tourist Hotline', number: '1912', icon: '📞' }
              ].map((emergency, index) => (
                <motion.div
                  key={index}
                  whileHover={{ y: -5 }}
                  className="bg-white rounded-lg p-6 text-center shadow-md hover:shadow-lg transition-all duration-200"
                >
                  <div className="text-4xl mb-3">{emergency.icon}</div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">
                    {emergency.service}
                  </h3>
                  <p className="text-2xl font-bold text-red-600">
                    {emergency.number}
                  </p>
                </motion.div>
              ))}
            </div>
          </AnimatedSection>
        </div>
      </section>

      {/* FAQ Section */}
      <FAQ
        title="Sri Lanka Travel FAQ 2025"
        faqs={sriLankaTravelFAQs}
        showStructuredData={true}
      />

      {/* Call to Action */}
      <section className="section-padding bg-gradient-to-r from-green-600 to-blue-600 text-white">
        <div className="container-max text-center">
          <AnimatedSection animation="fadeIn">
            <h2 className="text-3xl md:text-4xl font-bold mb-6">
              Ready for Your Sri Lankan Adventure?
            </h2>
            <p className="text-xl mb-8 opacity-90">
              With these tips in mind, you're all set for an amazing journey through Sri Lanka.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                className="bg-white text-green-600 hover:bg-gray-100 font-medium py-3 px-8 rounded-lg transition-colors"
              >
                Download Travel Guide
              </motion.button>
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                className="border-2 border-white text-white hover:bg-white hover:text-green-600 font-medium py-3 px-8 rounded-lg transition-colors"
              >
                Contact Support
              </motion.button>
            </div>
          </AnimatedSection>
        </div>
      </section>
    </>
  )
}

export default TravelTips
