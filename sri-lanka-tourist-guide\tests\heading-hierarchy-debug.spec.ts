import { test, expect } from '@playwright/test'

test.describe('Heading Hierarchy Debug', () => {
  test('Check heading hierarchy on Contact page', async ({ page }) => {
    await page.goto('/contact')
    await page.waitForLoadState('networkidle')
    
    const headingInfo = await page.evaluate(() => {
      const headings = Array.from(document.querySelectorAll('h1, h2, h3, h4, h5, h6'))
      
      return {
        headings: headings.map(h => ({
          tag: h.tagName,
          text: h.textContent?.trim().slice(0, 50),
          level: parseInt(h.tagName.charAt(1))
        })),
        headingOrder: (() => {
          let previousLevel = 0
          let isValid = true
          
          for (const heading of headings) {
            const currentLevel = parseInt(heading.tagName.charAt(1))
            
            if (previousLevel === 0) {
              // First heading should be H1
              if (currentLevel !== 1) {
                isValid = false
                break
              }
            } else {
              // Subsequent headings should not skip levels
              if (currentLevel > previousLevel + 1) {
                isValid = false
                break
              }
            }
            
            previousLevel = currentLevel
          }
          
          return isValid
        })()
      }
    })
    
    console.log('Contact page heading info:', headingInfo)
    
    // Check if there's exactly one H1
    const h1Count = headingInfo.headings.filter(h => h.level === 1).length
    console.log('H1 count:', h1Count)
    
    // Check heading order
    console.log('Heading order valid:', headingInfo.headingOrder)
    
    expect(h1Count).toBe(1)
    expect(headingInfo.headingOrder).toBe(true)
  })

  test('Check all pages for heading hierarchy', async ({ page }) => {
    const pages = ['/', '/destinations', '/activities', '/culture', '/articles', '/travel-tips', '/contact']
    
    for (const url of pages) {
      await page.goto(url)
      await page.waitForLoadState('networkidle')
      
      const headingInfo = await page.evaluate(() => {
        const headings = Array.from(document.querySelectorAll('h1, h2, h3, h4, h5, h6'))
        
        return {
          h1Count: headings.filter(h => h.tagName === 'H1').length,
          headings: headings.map(h => ({
            tag: h.tagName,
            text: h.textContent?.trim().slice(0, 30),
            level: parseInt(h.tagName.charAt(1))
          })),
          headingOrder: (() => {
            let previousLevel = 0
            let isValid = true
            
            for (const heading of headings) {
              const currentLevel = parseInt(heading.tagName.charAt(1))
              
              if (previousLevel === 0) {
                // First heading should be H1
                if (currentLevel !== 1) {
                  isValid = false
                  break
                }
              } else {
                // Subsequent headings should not skip levels (allow going back to any previous level)
                if (currentLevel > previousLevel + 1) {
                  isValid = false
                  break
                }
              }
              
              previousLevel = Math.min(previousLevel, currentLevel) // Allow going back to previous levels
            }
            
            return isValid
          })()
        }
      })
      
      console.log(`\n=== ${url} ===`)
      console.log('H1 count:', headingInfo.h1Count)
      console.log('Heading order valid:', headingInfo.headingOrder)
      console.log('Headings:', headingInfo.headings.slice(0, 5)) // First 5 headings
      
      if (!headingInfo.headingOrder) {
        console.log('All headings:', headingInfo.headings)
      }
    }
  })
})
