#!/usr/bin/env node

const { execSync } = require('child_process')
const fs = require('fs')
const path = require('path')

// Production build script for Sri Lanka Tourist Guide
console.log('🚀 Starting production build process...')

const BUILD_DIR = path.join(__dirname, '../dist')
const PUBLIC_DIR = path.join(__dirname, '../public')

// Build steps configuration
const BUILD_STEPS = [
  {
    name: 'Clean previous build',
    command: () => {
      if (fs.existsSync(BUILD_DIR)) {
        fs.rmSync(BUILD_DIR, { recursive: true, force: true })
        console.log('✅ Cleaned previous build directory')
      }
    }
  },
  {
    name: 'Install dependencies',
    command: 'npm ci --production=false'
  },
  {
    name: 'Run type checking',
    command: 'npm run type-check'
  },
  {
    name: 'Run linting',
    command: 'npm run lint'
  },
  {
    name: 'Run tests',
    command: 'npm run test:ci'
  },
  {
    name: 'Generate SEO files',
    command: 'node scripts/generateSitemap.js'
  },
  {
    name: 'Build application',
    command: 'npm run build'
  },
  {
    name: 'Optimize images',
    command: () => optimizeImages()
  },
  {
    name: 'Generate service worker',
    command: () => generateServiceWorker()
  },
  {
    name: 'Validate build',
    command: () => validateBuild()
  },
  {
    name: 'Generate build report',
    command: () => generateBuildReport()
  }
]

// Execute command with error handling
function executeCommand(command, stepName) {
  try {
    if (typeof command === 'function') {
      command()
    } else {
      console.log(`⏳ ${stepName}...`)
      execSync(command, { stdio: 'inherit', cwd: path.join(__dirname, '..') })
      console.log(`✅ ${stepName} completed`)
    }
  } catch (error) {
    console.error(`❌ ${stepName} failed:`, error.message)
    process.exit(1)
  }
}

// Optimize images in build directory
function optimizeImages() {
  console.log('🖼️ Optimizing images...')
  
  const imageDir = path.join(BUILD_DIR, 'assets/images')
  if (!fs.existsSync(imageDir)) {
    console.log('ℹ️ No images directory found, skipping optimization')
    return
  }
  
  // In a real implementation, you would use image optimization tools
  // For now, we'll just log the optimization step
  const imageFiles = fs.readdirSync(imageDir, { recursive: true })
    .filter(file => /\.(jpg|jpeg|png|webp|svg)$/i.test(file))
  
  console.log(`✅ Found ${imageFiles.length} images for optimization`)
  
  // TODO: Implement actual image optimization
  // - Convert to WebP format
  // - Generate responsive image sizes
  // - Compress images
  // - Generate blur placeholders
}

// Generate service worker for caching
function generateServiceWorker() {
  console.log('⚙️ Generating service worker...')
  
  const swContent = `// Service Worker for Sri Lanka Tourist Guide
// Generated on: ${new Date().toISOString()}

const CACHE_NAME = 'sri-lanka-guide-v1'
const STATIC_CACHE_URLS = [
  '/',
  '/articles',
  '/destinations',
  '/activities',
  '/culture',
  '/travel-tips',
  '/contact',
  '/offline.html'
]

// Install event
self.addEventListener('install', (event) => {
  event.waitUntil(
    caches.open(CACHE_NAME)
      .then((cache) => cache.addAll(STATIC_CACHE_URLS))
      .then(() => self.skipWaiting())
  )
})

// Activate event
self.addEventListener('activate', (event) => {
  event.waitUntil(
    caches.keys().then((cacheNames) => {
      return Promise.all(
        cacheNames.map((cacheName) => {
          if (cacheName !== CACHE_NAME) {
            return caches.delete(cacheName)
          }
        })
      )
    }).then(() => self.clients.claim())
  )
})

// Fetch event
self.addEventListener('fetch', (event) => {
  event.respondWith(
    caches.match(event.request)
      .then((response) => {
        // Return cached version or fetch from network
        return response || fetch(event.request)
      })
      .catch(() => {
        // Return offline page for navigation requests
        if (event.request.mode === 'navigate') {
          return caches.match('/offline.html')
        }
      })
  )
})
`

  fs.writeFileSync(path.join(BUILD_DIR, 'sw.js'), swContent)
  console.log('✅ Service worker generated')
}

// Validate build output
function validateBuild() {
  console.log('🔍 Validating build output...')
  
  const requiredFiles = [
    'index.html',
    'assets/js',
    'assets/css',
    'sitemap.xml',
    'robots.txt'
  ]
  
  const missingFiles = []
  
  requiredFiles.forEach(file => {
    const filePath = path.join(BUILD_DIR, file)
    if (!fs.existsSync(filePath)) {
      missingFiles.push(file)
    }
  })
  
  if (missingFiles.length > 0) {
    console.error('❌ Missing required files:', missingFiles)
    process.exit(1)
  }
  
  // Check index.html content
  const indexPath = path.join(BUILD_DIR, 'index.html')
  const indexContent = fs.readFileSync(indexPath, 'utf-8')
  
  const requiredElements = [
    '<title>',
    '<meta name="description"',
    '<meta property="og:title"',
    '<script type="application/ld+json">'
  ]
  
  const missingElements = requiredElements.filter(element => 
    !indexContent.includes(element)
  )
  
  if (missingElements.length > 0) {
    console.warn('⚠️ Missing SEO elements in index.html:', missingElements)
  }
  
  console.log('✅ Build validation completed')
}

// Generate build report
function generateBuildReport() {
  console.log('📊 Generating build report...')
  
  const buildStats = {
    buildTime: new Date().toISOString(),
    buildSize: getBuildSize(),
    fileCount: getFileCount(),
    chunkAnalysis: getChunkAnalysis()
  }
  
  const reportContent = `# Sri Lanka Tourist Guide - Build Report

**Build Date:** ${buildStats.buildTime}
**Total Size:** ${buildStats.buildSize.total} MB
**File Count:** ${buildStats.fileCount}

## Size Breakdown
- JavaScript: ${buildStats.buildSize.js} MB
- CSS: ${buildStats.buildSize.css} MB
- Images: ${buildStats.buildSize.images} MB
- Other: ${buildStats.buildSize.other} MB

## Chunk Analysis
${buildStats.chunkAnalysis.map(chunk => 
  `- ${chunk.name}: ${chunk.size} KB`
).join('\n')}

## Performance Recommendations
- Total bundle size is ${buildStats.buildSize.total < 1 ? 'optimal' : 'large'}
- ${buildStats.fileCount} files generated
- Consider code splitting if bundle size > 1MB

## Next Steps
1. Deploy to staging environment
2. Run Lighthouse audit
3. Test Core Web Vitals
4. Submit sitemap to search engines
`

  fs.writeFileSync(path.join(BUILD_DIR, 'BUILD_REPORT.md'), reportContent)
  console.log('✅ Build report generated')
  
  // Display summary
  console.log('\n📊 Build Summary:')
  console.log(`   Total Size: ${buildStats.buildSize.total} MB`)
  console.log(`   Files: ${buildStats.fileCount}`)
  console.log(`   JavaScript: ${buildStats.buildSize.js} MB`)
  console.log(`   CSS: ${buildStats.buildSize.css} MB`)
}

// Calculate build size
function getBuildSize() {
  const getDirectorySize = (dir) => {
    if (!fs.existsSync(dir)) return 0
    
    let size = 0
    const files = fs.readdirSync(dir, { withFileTypes: true })
    
    files.forEach(file => {
      const filePath = path.join(dir, file.name)
      if (file.isDirectory()) {
        size += getDirectorySize(filePath)
      } else {
        size += fs.statSync(filePath).size
      }
    })
    
    return size
  }
  
  const totalSize = getDirectorySize(BUILD_DIR)
  const jsSize = getDirectorySize(path.join(BUILD_DIR, 'assets/js'))
  const cssSize = getDirectorySize(path.join(BUILD_DIR, 'assets/css'))
  const imagesSize = getDirectorySize(path.join(BUILD_DIR, 'assets/images'))
  const otherSize = totalSize - jsSize - cssSize - imagesSize
  
  return {
    total: (totalSize / 1024 / 1024).toFixed(2),
    js: (jsSize / 1024 / 1024).toFixed(2),
    css: (cssSize / 1024 / 1024).toFixed(2),
    images: (imagesSize / 1024 / 1024).toFixed(2),
    other: (otherSize / 1024 / 1024).toFixed(2)
  }
}

// Count files in build
function getFileCount() {
  const countFiles = (dir) => {
    if (!fs.existsSync(dir)) return 0
    
    let count = 0
    const files = fs.readdirSync(dir, { withFileTypes: true })
    
    files.forEach(file => {
      const filePath = path.join(dir, file.name)
      if (file.isDirectory()) {
        count += countFiles(filePath)
      } else {
        count++
      }
    })
    
    return count
  }
  
  return countFiles(BUILD_DIR)
}

// Analyze chunks
function getChunkAnalysis() {
  const jsDir = path.join(BUILD_DIR, 'assets/js')
  if (!fs.existsSync(jsDir)) return []
  
  const jsFiles = fs.readdirSync(jsDir)
    .filter(file => file.endsWith('.js'))
    .map(file => {
      const filePath = path.join(jsDir, file)
      const size = fs.statSync(filePath).size
      return {
        name: file,
        size: (size / 1024).toFixed(2)
      }
    })
    .sort((a, b) => parseFloat(b.size) - parseFloat(a.size))
  
  return jsFiles
}

// Main build process
function main() {
  const startTime = Date.now()
  
  console.log('🏗️ Sri Lanka Tourist Guide - Production Build')
  console.log('=' .repeat(50))
  
  BUILD_STEPS.forEach((step, index) => {
    console.log(`\n[${index + 1}/${BUILD_STEPS.length}] ${step.name}`)
    executeCommand(step.command, step.name)
  })
  
  const buildTime = ((Date.now() - startTime) / 1000).toFixed(2)
  
  console.log('\n🎉 Production build completed successfully!')
  console.log(`⏱️ Total build time: ${buildTime} seconds`)
  console.log(`📁 Build output: ${BUILD_DIR}`)
  console.log('\n🚀 Ready for deployment!')
}

if (require.main === module) {
  main()
}

module.exports = {
  executeCommand,
  optimizeImages,
  generateServiceWorker,
  validateBuild,
  generateBuildReport
}
