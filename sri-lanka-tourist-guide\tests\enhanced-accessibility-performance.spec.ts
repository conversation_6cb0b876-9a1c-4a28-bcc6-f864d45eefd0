import { test, expect, Page } from '@playwright/test'

test.describe('Enhanced Accessibility & Performance Testing', () => {
  const pages = [
    { name: 'Home', url: '/' },
    { name: 'Destinations', url: '/destinations' },
    { name: 'Activities', url: '/activities' },
    { name: 'Culture', url: '/culture' },
    { name: 'Articles', url: '/articles' },
    { name: 'Travel Tips', url: '/travel-tips' },
    { name: 'Contact', url: '/contact' }
  ]

  // Helper function for comprehensive accessibility check
  async function checkAccessibility(page: Page) {
    return await page.evaluate(() => {
      const checks = {
        // Document structure
        hasDoctype: document.doctype !== null,
        hasLang: document.documentElement.hasAttribute('lang'),
        hasTitle: document.title.length > 0,
        titleLength: document.title.length,
        hasMetaViewport: document.querySelector('meta[name="viewport"]') !== null,
        hasMetaDescription: document.querySelector('meta[name="description"]') !== null,
        
        // Heading structure
        hasH1: document.querySelector('h1') !== null,
        h1Count: document.querySelectorAll('h1').length,
        headingOrder: (() => {
          const headings = Array.from(document.querySelectorAll('h1, h2, h3, h4, h5, h6'))
          const levels = headings.map(h => parseInt(h.tagName.charAt(1)))
          let isValid = true
          for (let i = 1; i < levels.length; i++) {
            if (levels[i] > levels[i-1] + 1) {
              isValid = false
              break
            }
          }
          return isValid
        })(),
        
        // Images
        imagesWithoutAlt: Array.from(document.querySelectorAll('img')).filter(img => {
          const alt = img.alt;
          const src = img.src;
          const className = img.className;

          // Skip Leaflet map tiles and markers (these are decorative)
          if (className.includes('leaflet-tile') ||
              className.includes('leaflet-marker-shadow') ||
              src.includes('openstreetmap.org') ||
              src.includes('leaflet')) {
            return false;
          }

          return !alt || alt.trim() === '';
        }).length,
        decorativeImages: Array.from(document.querySelectorAll('img[alt=""]')).length,
        
        // Links
        linksWithoutText: Array.from(document.querySelectorAll('a')).filter(link => 
          !link.textContent?.trim() && !link.getAttribute('aria-label') && !link.querySelector('img[alt]')
        ).length,
        
        // Buttons
        buttonsWithoutText: Array.from(document.querySelectorAll('button')).filter(btn => 
          !btn.textContent?.trim() && !btn.getAttribute('aria-label')
        ).length,
        
        // Form elements
        inputsWithoutLabels: Array.from(document.querySelectorAll('input:not([type="hidden"])')).filter(input => {
          const id = input.id
          const hasLabel = id && document.querySelector(`label[for="${id}"]`)
          const hasAriaLabel = input.getAttribute('aria-label')
          const hasAriaLabelledby = input.getAttribute('aria-labelledby')
          return !hasLabel && !hasAriaLabel && !hasAriaLabelledby
        }).length,
        
        // Color contrast (basic check)
        lowContrastElements: (() => {
          const elements = Array.from(document.querySelectorAll('*'))
          let lowContrast = 0
          elements.forEach(el => {
            const styles = window.getComputedStyle(el as HTMLElement)
            const color = styles.color
            const backgroundColor = styles.backgroundColor
            
            // Simple contrast check (this is a basic implementation)
            if (color === 'rgb(128, 128, 128)' && backgroundColor === 'rgb(255, 255, 255)') {
              lowContrast++
            }
          })
          return lowContrast
        })(),
        
        // Keyboard navigation
        focusableElements: document.querySelectorAll('a, button, input, select, textarea, [tabindex]:not([tabindex="-1"])').length,
        
        // ARIA
        ariaLabels: document.querySelectorAll('[aria-label]').length,
        ariaDescribedby: document.querySelectorAll('[aria-describedby]').length,
        ariaLabelledby: document.querySelectorAll('[aria-labelledby]').length,
        
        // Skip links
        hasSkipLink: document.querySelector('a[href="#main"], a[href="#content"]') !== null
      }
      
      return checks
    })
  }

  // Helper function for performance metrics
  async function getPerformanceMetrics(page: Page) {
    return await page.evaluate(() => {
      const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming
      const paint = performance.getEntriesByType('paint')
      
      return {
        // Navigation timing
        domContentLoaded: navigation.domContentLoadedEventEnd - navigation.domContentLoadedEventStart,
        loadComplete: navigation.loadEventEnd - navigation.loadEventStart,
        
        // Paint timing
        firstPaint: paint.find(p => p.name === 'first-paint')?.startTime || 0,
        firstContentfulPaint: paint.find(p => p.name === 'first-contentful-paint')?.startTime || 0,
        
        // Resource counts
        totalResources: performance.getEntriesByType('resource').length,
        imageResources: performance.getEntriesByType('resource').filter(r => r.name.match(/\.(jpg|jpeg|png|gif|webp|svg)$/i)).length,
        jsResources: performance.getEntriesByType('resource').filter(r => r.name.includes('.js')).length,
        cssResources: performance.getEntriesByType('resource').filter(r => r.name.includes('.css')).length,
        
        // Memory (if available)
        memory: (performance as any).memory ? {
          usedJSHeapSize: (performance as any).memory.usedJSHeapSize,
          totalJSHeapSize: (performance as any).memory.totalJSHeapSize,
          jsHeapSizeLimit: (performance as any).memory.jsHeapSizeLimit
        } : null
      }
    })
  }

  test.describe('WCAG 2.1 AA Compliance', () => {
    for (const pageInfo of pages) {
      test(`${pageInfo.name} - WCAG 2.1 AA compliance`, async ({ page }) => {
        await page.goto(pageInfo.url)
        await page.waitForLoadState('networkidle')
        
        const accessibility = await checkAccessibility(page)
        
        // Document structure requirements
        expect(accessibility.hasDoctype).toBe(true)
        expect(accessibility.hasLang).toBe(true)
        expect(accessibility.hasTitle).toBe(true)
        expect(accessibility.titleLength).toBeGreaterThan(10)
        expect(accessibility.titleLength).toBeLessThan(60)
        expect(accessibility.hasMetaViewport).toBe(true)
        expect(accessibility.hasMetaDescription).toBe(true)
        
        // Heading structure
        expect(accessibility.hasH1).toBe(true)
        expect(accessibility.h1Count).toBe(1) // Should have exactly one H1
        expect(accessibility.headingOrder).toBe(true)
        
        // Images
        expect(accessibility.imagesWithoutAlt).toBe(0)
        
        // Interactive elements
        expect(accessibility.linksWithoutText).toBe(0)
        expect(accessibility.buttonsWithoutText).toBe(0)
        expect(accessibility.inputsWithoutLabels).toBe(0)
        
        // Keyboard navigation
        expect(accessibility.focusableElements).toBeGreaterThan(0)
        
        console.log(`${pageInfo.name} accessibility check:`, accessibility)
      })
    }
  })

  test.describe('Keyboard Navigation', () => {
    test('Tab navigation works correctly', async ({ page }) => {
      await page.goto('/')
      await page.waitForLoadState('networkidle')
      
      // Start tabbing through the page
      let tabCount = 0
      const maxTabs = 20
      const focusedElements: string[] = []
      
      while (tabCount < maxTabs) {
        await page.keyboard.press('Tab')
        tabCount++
        
        const focusedElement = await page.evaluate(() => {
          const el = document.activeElement
          if (el) {
            return {
              tagName: el.tagName,
              className: (el as HTMLElement).className,
              id: el.id,
              text: (el as HTMLElement).textContent?.trim().substring(0, 50)
            }
          }
          return null
        })
        
        if (focusedElement) {
          focusedElements.push(`${focusedElement.tagName}${focusedElement.id ? '#' + focusedElement.id : ''}`)
          
          // Check if element is visible
          const isVisible = await page.evaluate(() => {
            const el = document.activeElement as HTMLElement
            if (!el) return false
            
            const rect = el.getBoundingClientRect()
            const styles = window.getComputedStyle(el)
            
            return rect.width > 0 && rect.height > 0 && 
                   styles.visibility !== 'hidden' && 
                   styles.display !== 'none'
          })
          
          expect(isVisible).toBe(true)
        }
      }
      
      expect(focusedElements.length).toBeGreaterThan(5)
      console.log('Tab navigation elements:', focusedElements)
    })

    test('Skip link functionality', async ({ page }) => {
      await page.goto('/')
      
      // Press Tab to focus skip link
      await page.keyboard.press('Tab')
      
      const skipLink = await page.evaluate(() => {
        const el = document.activeElement
        return el && el.textContent?.toLowerCase().includes('skip')
      })
      
      if (skipLink) {
        // Press Enter to activate skip link
        await page.keyboard.press('Enter')
        
        // Check if focus moved to main content
        const mainFocused = await page.evaluate(() => {
          const el = document.activeElement
          return el && (el.id === 'main' || el.closest('main'))
        })
        
        expect(mainFocused).toBe(true)
      }
    })
  })

  test.describe('Performance Optimization', () => {
    for (const pageInfo of pages) {
      test(`${pageInfo.name} - Performance metrics`, async ({ page }) => {
        const startTime = Date.now()
        
        await page.goto(pageInfo.url)
        await page.waitForLoadState('networkidle')
        
        const loadTime = Date.now() - startTime
        const metrics = await getPerformanceMetrics(page)
        
        // Performance assertions
        expect(loadTime).toBeLessThan(5000) // Page load < 5s
        expect(metrics.firstContentfulPaint).toBeLessThan(2500) // FCP < 2.5s
        expect(metrics.totalResources).toBeLessThan(100) // Reasonable resource count
        
        // Memory usage (if available)
        if (metrics.memory) {
          expect(metrics.memory.usedJSHeapSize).toBeLessThan(50 * 1024 * 1024) // < 50MB
        }
        
        console.log(`${pageInfo.name} performance:`, {
          loadTime,
          fcp: metrics.firstContentfulPaint,
          resources: metrics.totalResources,
          memory: metrics.memory?.usedJSHeapSize
        })
      })
    }
  })

  test.describe('Lighthouse Scores Simulation', () => {
    test('Lighthouse-style checks', async ({ page }) => {
      for (const pageInfo of pages) {
        await page.goto(pageInfo.url)
        await page.waitForLoadState('networkidle')
        
        // Simulate Lighthouse checks
        const lighthouseChecks = await page.evaluate(() => {
          return {
            // Performance
            hasLazyLoading: Array.from(document.querySelectorAll('img[loading="lazy"]')).length > 0,
            hasWebpImages: Array.from(document.querySelectorAll('img')).some(img => img.src.includes('.webp')),
            hasMinifiedCSS: Array.from(document.querySelectorAll('link[rel="stylesheet"]')).length < 10,
            
            // SEO
            hasMetaDescription: document.querySelector('meta[name="description"]') !== null,
            hasCanonicalUrl: document.querySelector('link[rel="canonical"]') !== null,
            hasStructuredData: document.querySelector('script[type="application/ld+json"]') !== null,
            
            // Best Practices
            hasHttpsLinks: Array.from(document.querySelectorAll('a[href^="http:"]')).length === 0,
            hasSecureImages: Array.from(document.querySelectorAll('img')).every(img => 
              img.src.startsWith('https:') || img.src.startsWith('/')
            ),
            
            // Accessibility
            hasAltText: Array.from(document.querySelectorAll('img')).every(img => img.alt !== undefined),
            hasAriaLabels: document.querySelectorAll('[aria-label]').length > 0,
            hasProperHeadings: document.querySelector('h1') !== null
          }
        })
        
        // Assert Lighthouse-style requirements
        expect(lighthouseChecks.hasMetaDescription).toBe(true)
        expect(lighthouseChecks.hasAltText).toBe(true)
        expect(lighthouseChecks.hasProperHeadings).toBe(true)
        expect(lighthouseChecks.hasHttpsLinks).toBe(true)
        
        console.log(`${pageInfo.name} Lighthouse checks:`, lighthouseChecks)
      }
    })
  })

  test.describe('Error Handling', () => {
    test('No JavaScript errors on page load', async ({ page }) => {
      const jsErrors: string[] = []
      
      page.on('console', msg => {
        if (msg.type() === 'error') {
          jsErrors.push(msg.text())
        }
      })
      
      page.on('pageerror', error => {
        jsErrors.push(error.message)
      })
      
      for (const pageInfo of pages) {
        await page.goto(pageInfo.url)
        await page.waitForLoadState('networkidle')
        await page.waitForTimeout(2000) // Wait for any delayed scripts
      }
      
      // Filter out known acceptable errors
      const filteredErrors = jsErrors.filter(error => 
        !error.includes('favicon.ico') &&
        !error.includes('404') &&
        !error.includes('net::ERR_') &&
        !error.includes('Non-Error promise rejection')
      )
      
      expect(filteredErrors.length).toBeLessThan(3) // Allow minimal errors
      
      if (filteredErrors.length > 0) {
        console.log('JavaScript errors found:', filteredErrors)
      }
    })

    test('Broken links detection', async ({ page }) => {
      await page.goto('/')
      await page.waitForLoadState('networkidle')
      
      // Get all internal links
      const internalLinks = await page.evaluate(() => {
        return Array.from(document.querySelectorAll('a[href^="/"], a[href^="./"], a[href^="../"]'))
          .map(link => (link as HTMLAnchorElement).href)
          .filter(href => href && !href.includes('#'))
          .slice(0, 10) // Test first 10 links
      })
      
      const brokenLinks: string[] = []
      
      for (const link of internalLinks) {
        try {
          const response = await page.goto(link)
          if (response && response.status() >= 400) {
            brokenLinks.push(`${link} - Status: ${response.status()}`)
          }
        } catch (error) {
          brokenLinks.push(`${link} - Error: ${error}`)
        }
      }
      
      expect(brokenLinks.length).toBe(0)
      
      if (brokenLinks.length > 0) {
        console.log('Broken links found:', brokenLinks)
      }
    })
  })
})
