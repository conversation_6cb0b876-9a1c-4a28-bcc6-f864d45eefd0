<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Map Test</title>
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
        }
        #map {
            height: 500px;
            width: 100%;
            border: 2px solid #ccc;
            border-radius: 8px;
        }
        .test-info {
            margin-bottom: 20px;
            padding: 10px;
            background: #f0f0f0;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <div class="test-info">
        <h1>Leaflet Map Test</h1>
        <p>This is a simple test to verify that Leaflet maps are working correctly.</p>
    </div>
    
    <div id="map"></div>

    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>
    <script>
        // Initialize the map
        var map = L.map('map').setView([7.8731, 80.7718], 8);

        // Add tile layer
        L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
            attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
        }).addTo(map);

        // Test destinations data
        var destinations = [
            { name: 'Sigiriya', lat: 7.9568, lng: 80.7592, category: 'cultural' },
            { name: 'Kandy', lat: 7.2906, lng: 80.6337, category: 'cultural' },
            { name: 'Galle', lat: 6.0329, lng: 80.217, category: 'cultural' },
            { name: 'Ella', lat: 6.8667, lng: 81.0467, category: 'nature' },
            { name: 'Trincomalee', lat: 8.5874, lng: 81.2152, category: 'beach' }
        ];

        // Add markers
        destinations.forEach(function(dest) {
            var marker = L.marker([dest.lat, dest.lng]).addTo(map);
            marker.bindPopup('<b>' + dest.name + '</b><br>Category: ' + dest.category);
        });

        // Fit map to show all markers
        var group = new L.featureGroup(destinations.map(dest => L.marker([dest.lat, dest.lng])));
        map.fitBounds(group.getBounds().pad(0.1));

        console.log('Map initialized successfully');
        console.log('Map container:', document.getElementById('map'));
        console.log('Map object:', map);
    </script>
</body>
</html>
