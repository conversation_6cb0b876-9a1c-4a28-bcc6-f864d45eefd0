import { test, expect } from '@playwright/test'
import { articles } from '../src/data/articles'

test.describe('Article Content Rendering Validation', () => {
  const articleIds = Object.keys(articles)
  
  test('Check all articles for rendering errors', async ({ page }) => {
    const failedArticles: string[] = []
    const renderingIssues: { id: string, issue: string }[] = []
    
    console.log(`Testing ${articleIds.length} articles for rendering issues...`)
    
    for (const articleId of articleIds) {
      try {
        await page.goto(`/articles/${articleId}`)
        await page.waitForLoadState('networkidle')
        
        // Check for error rendering content
        const errorElement = page.locator('text=Error rendering content')
        const hasError = await errorElement.count() > 0
        
        if (hasError) {
          failedArticles.push(articleId)
          renderingIssues.push({ id: articleId, issue: 'Error rendering content' })
        }
        
        // Check if article content is actually displayed
        const articleContent = page.locator('.article-content')
        const hasContent = await articleContent.count() > 0
        
        if (!hasContent) {
          renderingIssues.push({ id: articleId, issue: 'No article content found' })
        }
        
        // Check for empty content
        const contentText = await articleContent.textContent()
        if (contentText && contentText.trim().length < 100) {
          renderingIssues.push({ id: articleId, issue: 'Content too short or empty' })
        }
        
        // Check for proper headings
        const headings = page.locator('.article-content h1, .article-content h2, .article-content h3')
        const headingCount = await headings.count()
        
        if (headingCount === 0) {
          renderingIssues.push({ id: articleId, issue: 'No headings found in content' })
        }
        
        // Check for images
        const images = page.locator('.article-content img')
        const imageCount = await images.count()
        
        console.log(`${articleId}: ${hasError ? 'ERROR' : 'OK'} - Content: ${contentText?.length || 0} chars, Headings: ${headingCount}, Images: ${imageCount}`)
        
      } catch (error) {
        failedArticles.push(articleId)
        renderingIssues.push({ id: articleId, issue: `Navigation error: ${error}` })
        console.log(`Failed to test ${articleId}: ${error}`)
      }
    }
    
    // Report results
    console.log('\n=== ARTICLE RENDERING VALIDATION RESULTS ===')
    console.log(`Total articles tested: ${articleIds.length}`)
    console.log(`Articles with issues: ${renderingIssues.length}`)
    console.log(`Failed articles: ${failedArticles.length}`)
    
    if (renderingIssues.length > 0) {
      console.log('\nIssues found:')
      renderingIssues.forEach(issue => {
        console.log(`- ${issue.id}: ${issue.issue}`)
      })
    }
    
    // Save detailed report
    const report = {
      timestamp: new Date().toISOString(),
      totalArticles: articleIds.length,
      articlesWithIssues: renderingIssues.length,
      failedArticles: failedArticles,
      issues: renderingIssues
    }
    
    await page.evaluate((reportData) => {
      console.log('ARTICLE_VALIDATION_REPORT:', JSON.stringify(reportData, null, 2))
    }, report)
    
    // Assert that no critical rendering errors exist
    expect(failedArticles.length).toBeLessThan(5) // Allow some minor issues but flag major problems
  })

  test('Test specific problematic articles', async ({ page }) => {
    const problematicArticles = [
      'adams-peak-pilgrimage-hiking-guide-2025',
      'sigiriya-complete-guide',
      'kandy-cultural-guide',
      'ella-travel-guide'
    ]
    
    for (const articleId of problematicArticles) {
      if (articles[articleId]) {
        await page.goto(`/articles/${articleId}`)
        await page.waitForLoadState('networkidle')
        
        // Take screenshot for visual verification
        await page.screenshot({
          path: `test-results/article-screenshots/${articleId}.png`,
          fullPage: true
        })
        
        // Check for specific rendering issues
        const errorText = page.locator('text=Error rendering content')
        const hasError = await errorText.count() > 0
        
        if (hasError) {
          console.log(`ERROR: ${articleId} has rendering error`)
          
          // Check console errors
          const consoleErrors: string[] = []
          page.on('console', msg => {
            if (msg.type() === 'error') {
              consoleErrors.push(msg.text())
            }
          })
          
          await page.reload()
          await page.waitForLoadState('networkidle')
          
          if (consoleErrors.length > 0) {
            console.log(`Console errors for ${articleId}:`, consoleErrors)
          }
        }
        
        // Verify basic article structure
        await expect(page.locator('h1')).toBeVisible()
        await expect(page.locator('.article-content')).toBeVisible()
        
        console.log(`${articleId}: ${hasError ? 'FAILED' : 'PASSED'}`)
      }
    }
  })

  test('Test article content structure and formatting', async ({ page }) => {
    // Test a few representative articles for proper formatting
    const testArticles = Object.keys(articles).slice(0, 5)
    
    for (const articleId of testArticles) {
      await page.goto(`/articles/${articleId}`)
      await page.waitForLoadState('networkidle')
      
      // Check for proper prose styling
      const proseContainer = page.locator('.prose')
      await expect(proseContainer).toBeVisible()
      
      // Check for proper heading hierarchy
      const h1Count = await page.locator('.article-content h1').count()
      const h2Count = await page.locator('.article-content h2').count()
      
      // Should have at least one main heading
      expect(h1Count + h2Count).toBeGreaterThan(0)
      
      // Check for paragraphs
      const paragraphs = await page.locator('.article-content p').count()
      expect(paragraphs).toBeGreaterThan(2)
      
      // Check for proper spacing and styling
      const contentHeight = await page.locator('.article-content').boundingBox()
      expect(contentHeight?.height).toBeGreaterThan(500) // Should have substantial content
      
      console.log(`${articleId}: Structure check passed - H1: ${h1Count}, H2: ${h2Count}, P: ${paragraphs}`)
    }
  })

  test('Test table of contents generation', async ({ page }) => {
    // Test articles that should have table of contents
    const longArticles = Object.keys(articles).filter(id => 
      articles[id].content.length > 5000
    ).slice(0, 3)
    
    for (const articleId of longArticles) {
      await page.goto(`/articles/${articleId}`)
      await page.waitForLoadState('networkidle')
      
      // Check if table of contents is generated
      const tocContainer = page.locator('.table-of-contents, [data-testid="table-of-contents"]')
      const hasToc = await tocContainer.count() > 0
      
      if (hasToc) {
        const tocLinks = await tocContainer.locator('a').count()
        expect(tocLinks).toBeGreaterThan(0)
        
        // Test TOC link functionality
        const firstLink = tocContainer.locator('a').first()
        await firstLink.click()
        await page.waitForTimeout(500)
        
        console.log(`${articleId}: TOC generated with ${tocLinks} links`)
      } else {
        console.log(`${articleId}: No TOC generated (content length: ${articles[articleId].content.length})`)
      }
    }
  })

  test('Test article metadata and SEO elements', async ({ page }) => {
    const testArticles = Object.keys(articles).slice(0, 3)
    
    for (const articleId of testArticles) {
      await page.goto(`/articles/${articleId}`)
      await page.waitForLoadState('networkidle')
      
      // Check page title
      const title = await page.title()
      expect(title.length).toBeGreaterThan(10)
      expect(title.length).toBeLessThan(60)
      
      // Check meta description
      const metaDescription = page.locator('meta[name="description"]')
      const hasMetaDescription = await metaDescription.count() > 0
      expect(hasMetaDescription).toBe(true)
      
      // Check article metadata display
      const category = page.locator('.article-category, [data-testid="article-category"]')
      const readTime = page.locator('.read-time, [data-testid="read-time"]')
      const publishDate = page.locator('.publish-date, [data-testid="publish-date"]')
      
      // At least some metadata should be visible
      const metadataVisible = await category.count() > 0 || 
                             await readTime.count() > 0 || 
                             await publishDate.count() > 0
      
      expect(metadataVisible).toBe(true)
      
      console.log(`${articleId}: Metadata check passed`)
    }
  })
})
