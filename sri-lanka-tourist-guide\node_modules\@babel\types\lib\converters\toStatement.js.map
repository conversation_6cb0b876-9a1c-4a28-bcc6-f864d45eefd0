{"version": 3, "names": ["_index", "require", "_index2", "_default", "exports", "default", "toStatement", "node", "ignore", "isStatement", "mustHaveId", "newType", "isClass", "isFunction", "isAssignmentExpression", "expressionStatement", "id", "Error", "type"], "sources": ["../../src/converters/toStatement.ts"], "sourcesContent": ["import {\n  isStatement,\n  isFunction,\n  isClass,\n  isAssignmentExpression,\n} from \"../validators/generated/index.ts\";\nimport { expressionStatement } from \"../builders/generated/index.ts\";\nimport type * as t from \"../index.ts\";\n\nexport default toStatement as {\n  (node: t.AssignmentExpression, ignore?: boolean): t.ExpressionStatement;\n\n  <T extends t.Statement>(node: T, ignore: false): T;\n  <T extends t.Statement>(node: T, ignore?: boolean): T | false;\n\n  (node: t.Class, ignore: false): t.ClassDeclaration;\n  (node: t.Class, ignore?: boolean): t.ClassDeclaration | false;\n\n  (node: t.Function, ignore: false): t.FunctionDeclaration;\n  (node: t.Function, ignore?: boolean): t.FunctionDeclaration | false;\n\n  (node: t.Node, ignore: false): t.Statement;\n  (node: t.Node, ignore?: boolean): t.Statement | false;\n};\n\nfunction toStatement(node: t.Node, ignore?: boolean): t.Statement | false {\n  if (isStatement(node)) {\n    return node;\n  }\n\n  let mustHaveId = false;\n  let newType;\n\n  if (isClass(node)) {\n    mustHaveId = true;\n    newType = \"ClassDeclaration\" as const;\n  } else if (isFunction(node)) {\n    mustHaveId = true;\n    newType = \"FunctionDeclaration\" as const;\n  } else if (isAssignmentExpression(node)) {\n    return expressionStatement(node);\n  }\n\n  // @ts-expect-error todo(flow->ts): node.id might be missing\n  if (mustHaveId && !node.id) {\n    newType = false;\n  }\n\n  if (!newType) {\n    if (ignore) {\n      return false;\n    } else {\n      throw new Error(`cannot turn ${node.type} to a statement`);\n    }\n  }\n\n  // @ts-expect-error manipulating node.type\n  node.type = newType;\n\n  // @ts-expect-error todo(flow->ts) refactor to avoid type unsafe mutations like reassigning node type above\n  return node;\n}\n"], "mappings": ";;;;;;AAAA,IAAAA,MAAA,GAAAC,OAAA;AAMA,IAAAC,OAAA,GAAAD,OAAA;AAAqE,IAAAE,QAAA,GAAAC,OAAA,CAAAC,OAAA,GAGtDC,WAAW;AAgB1B,SAASA,WAAWA,CAACC,IAAY,EAAEC,MAAgB,EAAuB;EACxE,IAAI,IAAAC,kBAAW,EAACF,IAAI,CAAC,EAAE;IACrB,OAAOA,IAAI;EACb;EAEA,IAAIG,UAAU,GAAG,KAAK;EACtB,IAAIC,OAAO;EAEX,IAAI,IAAAC,cAAO,EAACL,IAAI,CAAC,EAAE;IACjBG,UAAU,GAAG,IAAI;IACjBC,OAAO,GAAG,kBAA2B;EACvC,CAAC,MAAM,IAAI,IAAAE,iBAAU,EAACN,IAAI,CAAC,EAAE;IAC3BG,UAAU,GAAG,IAAI;IACjBC,OAAO,GAAG,qBAA8B;EAC1C,CAAC,MAAM,IAAI,IAAAG,6BAAsB,EAACP,IAAI,CAAC,EAAE;IACvC,OAAO,IAAAQ,2BAAmB,EAACR,IAAI,CAAC;EAClC;EAGA,IAAIG,UAAU,IAAI,CAACH,IAAI,CAACS,EAAE,EAAE;IAC1BL,OAAO,GAAG,KAAK;EACjB;EAEA,IAAI,CAACA,OAAO,EAAE;IACZ,IAAIH,MAAM,EAAE;MACV,OAAO,KAAK;IACd,CAAC,MAAM;MACL,MAAM,IAAIS,KAAK,CAAC,eAAeV,IAAI,CAACW,IAAI,iBAAiB,CAAC;IAC5D;EACF;EAGAX,IAAI,CAACW,IAAI,GAAGP,OAAO;EAGnB,OAAOJ,IAAI;AACb", "ignoreList": []}