import { test, expect } from '@playwright/test'

test.describe('Homepage Best Practices Audit 2025', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/')
    await page.waitForLoadState('networkidle')
  })

  test('Principle 1: Easy Access to Homepage', async ({ page }) => {
    // 1.1 Check for homepage links (logo and explicit)
    const logoLink = page.locator('header a[href="/"]').first()
    await expect(logoLink).toBeVisible()
    await expect(logoLink).toContainText('Sri Lanka Guide')

    // Check logo is in top-left corner
    const logoPosition = await logoLink.boundingBox()
    expect(logoPosition?.x).toBeLessThan(200) // Should be in left area
    expect(logoPosition?.y).toBeLessThan(100) // Should be in top area
    
    // 1.2 Check URL is simple and predictable
    expect(page.url()).toMatch(/localhost:5173\/?$/)
    
    // 1.3 Check homepage is visually distinct
    const pageTitle = await page.title()
    expect(pageTitle).toContain('Sri Lanka Travel Guide 2025')
    
    // Check for unique homepage elements
    const heroSection = page.locator('section').first()
    await expect(heroSection).toContainText('Sri Lanka Travel Guide 2025')
    await expect(heroSection).toContainText('Pearl of the Indian Ocean')
  })

  test('Principle 2: Communicate Who You Are and What You Do', async ({ page }) => {
    // 2.1 Company name and logo prominently displayed
    const logo = page.locator('header a[href="/"] div').first()
    await expect(logo).toBeVisible()
    await expect(logo).toContainText('Sri Lanka Guide')
    
    // 2.2 Clear tagline explaining what the site does
    const tagline = page.locator('h1')
    await expect(tagline).toContainText('Sri Lanka Travel Guide 2025')
    await expect(tagline).toContainText('Pearl of the Indian Ocean')
    
    const description = page.locator('section p').first()
    await expect(description).toContainText('Discover Sri Lanka\'s best destinations')
    await expect(description).toContainText('cultural treasures')
    await expect(description).toContainText('wildlife adventures')
    
    // 2.3 Unique value proposition
    const valueProps = page.locator('text=Why Visit Sri Lanka?')
    await expect(valueProps).toBeVisible()
    
    // Check for differentiation content
    const uniqueFeatures = page.locator('text=incredible diversity of experiences')
    await expect(uniqueFeatures).toBeVisible()
    
    // 2.4 Relevant imagery
    const heroGradient = page.locator('.hero-gradient')
    await expect(heroGradient).toBeVisible()
  })

  test('Principle 3: Reveal Content Through Examples', async ({ page }) => {
    // 3.1 Important content above the fold
    const heroContent = page.locator('h1').first()
    const heroBox = await heroContent.boundingBox()
    expect(heroBox?.y).toBeLessThan(800) // Should be above typical fold (adjusted for full-height hero)
    
    // Check for content continuation indicators
    const sections = page.locator('section')
    const sectionCount = await sections.count()
    expect(sectionCount).toBeGreaterThan(3) // Multiple sections to explore
    
    // 3.2 Specific examples of content
    const featuredDestinations = page.locator('text=Featured Destinations')
    await expect(featuredDestinations).toBeVisible()
    
    // Check for actual destination examples
    const destinationCards = page.locator('[class*="card"]').filter({ hasText: 'Learn More' })
    const cardCount = await destinationCards.count()
    expect(cardCount).toBeGreaterThanOrEqual(3)
    
    // Check for statistics/facts
    const statsSection = page.locator('text=Sri Lanka by the Numbers')
    await expect(statsSection).toBeVisible()
    
    const statNumbers = page.locator('text=UNESCO World Heritage Sites').last()
    await expect(statNumbers).toBeVisible()
  })

  test('Principle 4: Prompt Actions and Navigations', async ({ page }) => {
    // 4.1 Clear, descriptive link labels
    const primaryCTA = page.locator('text=Explore Destinations')
    await expect(primaryCTA).toBeVisible()
    
    const secondaryCTA = page.locator('text=Discover Activities')
    await expect(secondaryCTA).toBeVisible()
    
    // Check for specific, non-generic labels
    const learnMoreLinks = page.locator('text=Learn More →')
    const learnMoreCount = await learnMoreLinks.count()
    expect(learnMoreCount).toBeGreaterThan(0)
    
    // Check for user-focused language
    const travelTipsLink = page.locator('text=Get Travel Tips')
    await expect(travelTipsLink).toBeVisible()
    
    // 4.2 Clear visual hierarchy for high-priority tasks
    const primaryButtons = page.locator('.btn-primary')
    const primaryCount = await primaryButtons.count()
    expect(primaryCount).toBeGreaterThanOrEqual(2)
    
    // 4.3 Primary navigation easily accessible
    const mainNav = page.locator('nav[aria-label="Main navigation"]')
    await expect(mainNav).toBeVisible()
    
    // Check navigation items are descriptive
    const navItems = ['Destinations', 'Activities', 'Culture', 'Articles']
    for (const item of navItems) {
      const navLink = page.locator(`nav a:has-text("${item}")`).first()
      await expect(navLink).toBeVisible()
    }
  })

  test('Principle 5: Keep Homepages Simple', async ({ page }) => {
    // 5.1 Simple, standard design
    const header = page.locator('header')
    await expect(header).toBeVisible()
    
    const navigation = page.locator('nav').first()
    await expect(navigation).toBeVisible()
    
    const footer = page.locator('footer')
    await expect(footer).toBeVisible()
    
    // 5.2 Minimal motion and animation
    // Check for autoplay videos (should not exist)
    const videos = page.locator('video[autoplay]')
    const autoplayCount = await videos.count()
    expect(autoplayCount).toBe(0)
    
    // Check for excessive animations
    const animatedElements = page.locator('[class*="animate-bounce"]')
    const animatedCount = await animatedElements.count()
    expect(animatedCount).toBeLessThan(5) // Minimal decorative animations only
    
    // 5.3 Immediate access to content
    const loadTime = await page.evaluate(() => {
      return performance.timing.loadEventEnd - performance.timing.navigationStart
    })
    expect(loadTime).toBeLessThan(5000) // Should load within 5 seconds
    
    // Check main content is immediately visible
    const mainContent = page.locator('main, section').first()
    await expect(mainContent).toBeVisible()
    
    // 5.4 No popup windows or splash screens
    const popups = page.locator('[role="dialog"], .modal, .popup')
    const popupCount = await popups.count()
    expect(popupCount).toBe(0) // No intrusive popups
  })

  test('Additional 2025 Best Practices', async ({ page }) => {
    // Mobile-first responsive design
    await page.setViewportSize({ width: 375, height: 667 })
    const mobileMenu = page.locator('.mobile-menu-button')
    await expect(mobileMenu).toBeVisible()
    
    // Accessibility features
    const skipLink = page.locator('a[href="#main-content"]')
    await expect(skipLink).toBeAttached()
    
    // SEO optimization
    const metaDescription = page.locator('meta[name="description"]').last()
    await expect(metaDescription).toHaveAttribute('content', /Sri Lanka.*travel.*guide.*2025/i)
    
    // Performance indicators
    const images = page.locator('img')
    const imageCount = await images.count()
    // Should have some images but not excessive
    expect(imageCount).toBeLessThan(20)
    
    // Social proof/credibility
    const statsSection = page.locator('text=Sri Lanka by the Numbers')
    await expect(statsSection).toBeVisible()
    
    // Clear value propositions
    const valueProps = [
      'Beautiful Beaches',
      'Rich Heritage', 
      'Natural Wonders'
    ]
    
    for (const prop of valueProps) {
      const element = page.locator(`text=${prop}`)
      await expect(element).toBeVisible()
    }
  })

  test('Content Quality and User Experience', async ({ page }) => {
    // Check for comprehensive content
    const sections = page.locator('main section, section')
    const sectionCount = await sections.count()
    expect(sectionCount).toBeGreaterThanOrEqual(5)
    
    // Check for search functionality
    const searchInput = page.locator('input[type="text"], input[placeholder*="search" i]')
    await expect(searchInput).toBeVisible()
    
    // Check for clear calls-to-action
    const ctaButtons = page.locator('a[class*="btn"], button[class*="btn"]')
    const ctaCount = await ctaButtons.count()
    expect(ctaCount).toBeGreaterThanOrEqual(3)
    
    // Check for contact information accessibility
    const contactLink = page.locator('text=Contact Us').first()
    await expect(contactLink).toBeVisible()
    
    // Check for social proof elements
    const unescoText = page.locator('text=UNESCO World Heritage Sites')
    const nationalParksText = page.locator('text=National Parks')
    const historyText = page.locator('text=Years of Recorded History')

    const unescoCount = await unescoText.count()
    const parksCount = await nationalParksText.count()
    const historyCount = await historyText.count()

    expect(unescoCount + parksCount + historyCount).toBeGreaterThan(0)
  })

  test('Technical Excellence', async ({ page }) => {
    // Check for proper heading hierarchy
    const h1 = page.locator('h1')
    const h1Count = await h1.count()
    expect(h1Count).toBe(1) // Only one H1 per page
    
    const h2 = page.locator('h2')
    const h2Count = await h2.count()
    expect(h2Count).toBeGreaterThanOrEqual(3) // Multiple H2s for sections
    
    // Check for semantic HTML
    const main = page.locator('main')
    const sections = page.locator('section')
    const nav = page.locator('nav')
    const header = page.locator('header')
    
    await expect(header).toBeVisible()
    await expect(nav.first()).toBeVisible()
    const sectionCount = await sections.count()
    expect(sectionCount).toBeGreaterThan(3)
    
    // Check for structured data
    const structuredData = page.locator('script[type="application/ld+json"]')
    await expect(structuredData).toBeAttached()
    
    // Check for proper meta tags
    const ogTitle = page.locator('meta[property="og:title"]').first()
    await expect(ogTitle).toBeAttached()

    const twitterCard = page.locator('meta[property="twitter:card"]').first()
    await expect(twitterCard).toBeAttached()
  })
})
