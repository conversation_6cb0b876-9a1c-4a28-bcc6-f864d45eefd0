{"config": {"configFile": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\playwright.config.ts", "rootDir": "E:/Augment Code Testing/sri-lanka-tourist-guide/tests", "forbidOnly": false, "fullyParallel": true, "globalSetup": null, "globalTeardown": null, "globalTimeout": 0, "grep": {}, "grepInvert": null, "maxFailures": 0, "metadata": {"actualWorkers": 2}, "preserveOutput": "always", "reporter": [["html", null], ["json", {"outputFile": "test-results/results.json"}], ["junit", {"outputFile": "test-results/results.xml"}]], "reportSlowTests": {"max": 5, "threshold": 300000}, "quiet": false, "projects": [{"outputDir": "E:/Augment Code Testing/sri-lanka-tourist-guide/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 2}, "id": "chromium", "name": "chromium", "testDir": "E:/Augment Code Testing/sri-lanka-tourist-guide/tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "E:/Augment Code Testing/sri-lanka-tourist-guide/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 2}, "id": "firefox", "name": "firefox", "testDir": "E:/Augment Code Testing/sri-lanka-tourist-guide/tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "E:/Augment Code Testing/sri-lanka-tourist-guide/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 2}, "id": "webkit", "name": "webkit", "testDir": "E:/Augment Code Testing/sri-lanka-tourist-guide/tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "E:/Augment Code Testing/sri-lanka-tourist-guide/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 2}, "id": "Mobile Chrome", "name": "Mobile Chrome", "testDir": "E:/Augment Code Testing/sri-lanka-tourist-guide/tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "E:/Augment Code Testing/sri-lanka-tourist-guide/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 2}, "id": "Mobile Safari", "name": "Mobile Safari", "testDir": "E:/Augment Code Testing/sri-lanka-tourist-guide/tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}], "shard": null, "updateSnapshots": "missing", "updateSourceMethod": "patch", "version": "1.53.1", "workers": 2, "webServer": {"command": "npm run dev", "url": "http://localhost:5173", "reuseExistingServer": true, "timeout": 120000}}, "suites": [{"title": "visual.spec.ts", "file": "visual.spec.ts", "column": 0, "line": 0, "specs": [], "suites": [{"title": "Visual Testing", "file": "visual.spec.ts", "line": 3, "column": 6, "specs": [{"title": "Map component rendering", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 0, "parallelIndex": 0, "status": "failed", "duration": 25164, "error": {"message": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoHaveScreenshot\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\n  Timeout 5000ms exceeded.\n\nExpected: \u001b[33mE:\\Augment Code Testing\\sri-lanka-tourist-guide\\tests\\visual.spec.ts-snapshots\\interactive-map-chromium-win32.png\u001b[39m\nReceived: \u001b[33mE:\\Augment Code Testing\\sri-lanka-tourist-guide\\test-results\\visual-Visual-Testing-Map-component-rendering-chromium\\interactive-map-actual.png\u001b[39m\n    Diff: \u001b[33mE:\\Augment Code Testing\\sri-lanka-tourist-guide\\test-results\\visual-Visual-Testing-Map-component-rendering-chromium\\interactive-map-diff.png\u001b[39m\n\nCall log:\n\u001b[2m  - Expect \"toHaveScreenshot(interactive-map.png)\" with timeout 5000ms\u001b[22m\n\u001b[2m    - verifying given screenshot expectation\u001b[22m\n\u001b[2m  - waiting for locator('.leaflet-container').first()\u001b[22m\n\u001b[2m    - locator resolved to <div tabindex=\"0\" class=\"z-0 leaflet-map-container leaflet-container leaflet-touch leaflet-fade-anim leaflet-grab leaflet-touch-drag leaflet-touch-zoom\">…</div>\u001b[22m\n\u001b[2m  - taking element screenshot\u001b[22m\n\u001b[2m    - disabled all CSS animations\u001b[22m\n\u001b[2m  - waiting for fonts to load...\u001b[22m\n\u001b[2m  - fonts loaded\u001b[22m\n\u001b[2m  - attempting scroll into view action\u001b[22m\n\u001b[2m    - waiting for element to be stable\u001b[22m\n\u001b[2m  - 39850 pixels (ratio 0.07 of all image pixels) are different.\u001b[22m\n\u001b[2m  - waiting 100ms before taking screenshot\u001b[22m\n\u001b[2m  - waiting for locator('.leaflet-container').first()\u001b[22m\n\u001b[2m    - locator resolved to <div tabindex=\"0\" class=\"z-0 leaflet-map-container leaflet-container leaflet-touch leaflet-fade-anim leaflet-grab leaflet-touch-drag leaflet-touch-zoom\">…</div>\u001b[22m\n\u001b[2m  - taking element screenshot\u001b[22m\n\u001b[2m    - disabled all CSS animations\u001b[22m\n\u001b[2m  - waiting for fonts to load...\u001b[22m\n\u001b[2m  - fonts loaded\u001b[22m\n\u001b[2m  - attempting scroll into view action\u001b[22m\n\u001b[2m    - waiting for element to be stable\u001b[22m\n\u001b[2m  - Timeout 5000ms exceeded.\u001b[22m\n", "stack": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoHaveScreenshot\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\n  Timeout 5000ms exceeded.\n\nExpected: \u001b[33mE:\\Augment Code Testing\\sri-lanka-tourist-guide\\tests\\visual.spec.ts-snapshots\\interactive-map-chromium-win32.png\u001b[39m\nReceived: \u001b[33mE:\\Augment Code Testing\\sri-lanka-tourist-guide\\test-results\\visual-Visual-Testing-Map-component-rendering-chromium\\interactive-map-actual.png\u001b[39m\n    Diff: \u001b[33mE:\\Augment Code Testing\\sri-lanka-tourist-guide\\test-results\\visual-Visual-Testing-Map-component-rendering-chromium\\interactive-map-diff.png\u001b[39m\n\nCall log:\n\u001b[2m  - Expect \"toHaveScreenshot(interactive-map.png)\" with timeout 5000ms\u001b[22m\n\u001b[2m    - verifying given screenshot expectation\u001b[22m\n\u001b[2m  - waiting for locator('.leaflet-container').first()\u001b[22m\n\u001b[2m    - locator resolved to <div tabindex=\"0\" class=\"z-0 leaflet-map-container leaflet-container leaflet-touch leaflet-fade-anim leaflet-grab leaflet-touch-drag leaflet-touch-zoom\">…</div>\u001b[22m\n\u001b[2m  - taking element screenshot\u001b[22m\n\u001b[2m    - disabled all CSS animations\u001b[22m\n\u001b[2m  - waiting for fonts to load...\u001b[22m\n\u001b[2m  - fonts loaded\u001b[22m\n\u001b[2m  - attempting scroll into view action\u001b[22m\n\u001b[2m    - waiting for element to be stable\u001b[22m\n\u001b[2m  - 39850 pixels (ratio 0.07 of all image pixels) are different.\u001b[22m\n\u001b[2m  - waiting 100ms before taking screenshot\u001b[22m\n\u001b[2m  - waiting for locator('.leaflet-container').first()\u001b[22m\n\u001b[2m    - locator resolved to <div tabindex=\"0\" class=\"z-0 leaflet-map-container leaflet-container leaflet-touch leaflet-fade-anim leaflet-grab leaflet-touch-drag leaflet-touch-zoom\">…</div>\u001b[22m\n\u001b[2m  - taking element screenshot\u001b[22m\n\u001b[2m    - disabled all CSS animations\u001b[22m\n\u001b[2m  - waiting for fonts to load...\u001b[22m\n\u001b[2m  - fonts loaded\u001b[22m\n\u001b[2m  - attempting scroll into view action\u001b[22m\n\u001b[2m    - waiting for element to be stable\u001b[22m\n\u001b[2m  - Timeout 5000ms exceeded.\u001b[22m\n\n    at E:\\Augment Code Testing\\sri-lanka-tourist-guide\\tests\\visual.spec.ts:97:62", "location": {"file": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\tests\\visual.spec.ts", "column": 62, "line": 97}, "snippet": "\u001b[0m \u001b[90m  95 |\u001b[39m     \n \u001b[90m  96 |\u001b[39m     \u001b[90m// Take screenshot of map area\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m  97 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'.leaflet-container'\u001b[39m)\u001b[33m.\u001b[39mfirst())\u001b[33m.\u001b[39mtoHaveScreenshot(\u001b[32m'interactive-map.png'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                                                              \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m  98 |\u001b[39m   })\u001b[33m;\u001b[39m\n \u001b[90m  99 |\u001b[39m })\u001b[33m;\u001b[39m\n \u001b[90m 100 |\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\tests\\visual.spec.ts", "column": 62, "line": 97}, "message": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoHaveScreenshot\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\n  Timeout 5000ms exceeded.\n\nExpected: \u001b[33mE:\\Augment Code Testing\\sri-lanka-tourist-guide\\tests\\visual.spec.ts-snapshots\\interactive-map-chromium-win32.png\u001b[39m\nReceived: \u001b[33mE:\\Augment Code Testing\\sri-lanka-tourist-guide\\test-results\\visual-Visual-Testing-Map-component-rendering-chromium\\interactive-map-actual.png\u001b[39m\n    Diff: \u001b[33mE:\\Augment Code Testing\\sri-lanka-tourist-guide\\test-results\\visual-Visual-Testing-Map-component-rendering-chromium\\interactive-map-diff.png\u001b[39m\n\nCall log:\n\u001b[2m  - Expect \"toHaveScreenshot(interactive-map.png)\" with timeout 5000ms\u001b[22m\n\u001b[2m    - verifying given screenshot expectation\u001b[22m\n\u001b[2m  - waiting for locator('.leaflet-container').first()\u001b[22m\n\u001b[2m    - locator resolved to <div tabindex=\"0\" class=\"z-0 leaflet-map-container leaflet-container leaflet-touch leaflet-fade-anim leaflet-grab leaflet-touch-drag leaflet-touch-zoom\">…</div>\u001b[22m\n\u001b[2m  - taking element screenshot\u001b[22m\n\u001b[2m    - disabled all CSS animations\u001b[22m\n\u001b[2m  - waiting for fonts to load...\u001b[22m\n\u001b[2m  - fonts loaded\u001b[22m\n\u001b[2m  - attempting scroll into view action\u001b[22m\n\u001b[2m    - waiting for element to be stable\u001b[22m\n\u001b[2m  - 39850 pixels (ratio 0.07 of all image pixels) are different.\u001b[22m\n\u001b[2m  - waiting 100ms before taking screenshot\u001b[22m\n\u001b[2m  - waiting for locator('.leaflet-container').first()\u001b[22m\n\u001b[2m    - locator resolved to <div tabindex=\"0\" class=\"z-0 leaflet-map-container leaflet-container leaflet-touch leaflet-fade-anim leaflet-grab leaflet-touch-drag leaflet-touch-zoom\">…</div>\u001b[22m\n\u001b[2m  - taking element screenshot\u001b[22m\n\u001b[2m    - disabled all CSS animations\u001b[22m\n\u001b[2m  - waiting for fonts to load...\u001b[22m\n\u001b[2m  - fonts loaded\u001b[22m\n\u001b[2m  - attempting scroll into view action\u001b[22m\n\u001b[2m    - waiting for element to be stable\u001b[22m\n\u001b[2m  - Timeout 5000ms exceeded.\u001b[22m\n\n\n   95 |     \n   96 |     // Take screenshot of map area\n>  97 |     await expect(page.locator('.leaflet-container').first()).toHaveScreenshot('interactive-map.png');\n      |                                                              ^\n   98 |   });\n   99 | });\n  100 |\n    at E:\\Augment Code Testing\\sri-lanka-tourist-guide\\tests\\visual.spec.ts:97:62"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-06-24T05:51:14.575Z", "annotations": [], "attachments": [{"name": "interactive-map-expected.png", "contentType": "image/png", "path": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\tests\\visual.spec.ts-snapshots\\interactive-map-chromium-win32.png"}, {"name": "interactive-map-actual.png", "contentType": "image/png", "path": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\test-results\\visual-Visual-Testing-Map-component-rendering-chromium\\interactive-map-actual.png"}, {"name": "interactive-map-diff.png", "contentType": "image/png", "path": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\test-results\\visual-Visual-Testing-Map-component-rendering-chromium\\interactive-map-diff.png"}, {"name": "screenshot", "contentType": "image/png", "path": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\test-results\\visual-Visual-Testing-Map-component-rendering-chromium\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\test-results\\visual-Visual-Testing-Map-component-rendering-chromium\\video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\test-results\\visual-Visual-Testing-Map-component-rendering-chromium\\error-context.md"}], "errorLocation": {"file": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\tests\\visual.spec.ts", "column": 62, "line": 97}}], "status": "unexpected"}], "id": "8324bb52c49185e48ff4-9b5a128ab04d4be7a71b", "file": "visual.spec.ts", "line": 88, "column": 3}, {"title": "Map component rendering", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [{"workerIndex": 1, "parallelIndex": 1, "status": "failed", "duration": 28453, "error": {"message": "Error: \u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoHaveScreenshot\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\n  450151 pixels (ratio 0.74 of all image pixels) are different.\n\nExpected: \u001b[33mE:\\Augment Code Testing\\sri-lanka-tourist-guide\\tests\\visual.spec.ts-snapshots\\interactive-map-firefox-win32.png\u001b[39m\nReceived: \u001b[33mE:\\Augment Code Testing\\sri-lanka-tourist-guide\\test-results\\visual-Visual-Testing-Map-component-rendering-firefox\\interactive-map-actual.png\u001b[39m\n    Diff: \u001b[33mE:\\Augment Code Testing\\sri-lanka-tourist-guide\\test-results\\visual-Visual-Testing-Map-component-rendering-firefox\\interactive-map-diff.png\u001b[39m\n\nCall log:\n\u001b[2m  - Expect \"toHaveScreenshot(interactive-map.png)\" with timeout 5000ms\u001b[22m\n\u001b[2m    - verifying given screenshot expectation\u001b[22m\n\u001b[2m  - waiting for locator('.leaflet-container').first()\u001b[22m\n\u001b[2m    - locator resolved to <div tabindex=\"0\" class=\"z-0 leaflet-map-container leaflet-container leaflet-touch leaflet-fade-anim leaflet-grab leaflet-touch-drag leaflet-touch-zoom\">…</div>\u001b[22m\n\u001b[2m  - taking element screenshot\u001b[22m\n\u001b[2m    - disabled all CSS animations\u001b[22m\n\u001b[2m  - waiting for fonts to load...\u001b[22m\n\u001b[2m  - fonts loaded\u001b[22m\n\u001b[2m  - attempting scroll into view action\u001b[22m\n\u001b[2m    - waiting for element to be stable\u001b[22m\n\u001b[2m  - 39806 pixels (ratio 0.07 of all image pixels) are different.\u001b[22m\n\u001b[2m  - waiting 100ms before taking screenshot\u001b[22m\n\u001b[2m  - waiting for locator('.leaflet-container').first()\u001b[22m\n\u001b[2m    - locator resolved to <div tabindex=\"0\" class=\"z-0 leaflet-map-container leaflet-container leaflet-touch leaflet-fade-anim leaflet-grab leaflet-touch-drag leaflet-touch-zoom\">…</div>\u001b[22m\n\u001b[2m  - taking element screenshot\u001b[22m\n\u001b[2m    - disabled all CSS animations\u001b[22m\n\u001b[2m  - waiting for fonts to load...\u001b[22m\n\u001b[2m  - fonts loaded\u001b[22m\n\u001b[2m  - attempting scroll into view action\u001b[22m\n\u001b[2m    - waiting for element to be stable\u001b[22m\n\u001b[2m  - 507906 pixels (ratio 0.84 of all image pixels) are different.\u001b[22m\n\u001b[2m  - waiting 250ms before taking screenshot\u001b[22m\n\u001b[2m  - waiting for locator('.leaflet-container').first()\u001b[22m\n\u001b[2m    - locator resolved to <div tabindex=\"0\" class=\"z-0 leaflet-map-container leaflet-container leaflet-touch leaflet-fade-anim leaflet-grab leaflet-touch-drag leaflet-touch-zoom\">…</div>\u001b[22m\n\u001b[2m  - taking element screenshot\u001b[22m\n\u001b[2m    - disabled all CSS animations\u001b[22m\n\u001b[2m  - waiting for fonts to load...\u001b[22m\n\u001b[2m  - fonts loaded\u001b[22m\n\u001b[2m  - attempting scroll into view action\u001b[22m\n\u001b[2m    - waiting for element to be stable\u001b[22m\n\u001b[2m  - captured a stable screenshot\u001b[22m\n\u001b[2m  - 450151 pixels (ratio 0.74 of all image pixels) are different.\u001b[22m\n", "stack": "Error: \u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoHaveScreenshot\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\n  450151 pixels (ratio 0.74 of all image pixels) are different.\n\nExpected: \u001b[33mE:\\Augment Code Testing\\sri-lanka-tourist-guide\\tests\\visual.spec.ts-snapshots\\interactive-map-firefox-win32.png\u001b[39m\nReceived: \u001b[33mE:\\Augment Code Testing\\sri-lanka-tourist-guide\\test-results\\visual-Visual-Testing-Map-component-rendering-firefox\\interactive-map-actual.png\u001b[39m\n    Diff: \u001b[33mE:\\Augment Code Testing\\sri-lanka-tourist-guide\\test-results\\visual-Visual-Testing-Map-component-rendering-firefox\\interactive-map-diff.png\u001b[39m\n\nCall log:\n\u001b[2m  - Expect \"toHaveScreenshot(interactive-map.png)\" with timeout 5000ms\u001b[22m\n\u001b[2m    - verifying given screenshot expectation\u001b[22m\n\u001b[2m  - waiting for locator('.leaflet-container').first()\u001b[22m\n\u001b[2m    - locator resolved to <div tabindex=\"0\" class=\"z-0 leaflet-map-container leaflet-container leaflet-touch leaflet-fade-anim leaflet-grab leaflet-touch-drag leaflet-touch-zoom\">…</div>\u001b[22m\n\u001b[2m  - taking element screenshot\u001b[22m\n\u001b[2m    - disabled all CSS animations\u001b[22m\n\u001b[2m  - waiting for fonts to load...\u001b[22m\n\u001b[2m  - fonts loaded\u001b[22m\n\u001b[2m  - attempting scroll into view action\u001b[22m\n\u001b[2m    - waiting for element to be stable\u001b[22m\n\u001b[2m  - 39806 pixels (ratio 0.07 of all image pixels) are different.\u001b[22m\n\u001b[2m  - waiting 100ms before taking screenshot\u001b[22m\n\u001b[2m  - waiting for locator('.leaflet-container').first()\u001b[22m\n\u001b[2m    - locator resolved to <div tabindex=\"0\" class=\"z-0 leaflet-map-container leaflet-container leaflet-touch leaflet-fade-anim leaflet-grab leaflet-touch-drag leaflet-touch-zoom\">…</div>\u001b[22m\n\u001b[2m  - taking element screenshot\u001b[22m\n\u001b[2m    - disabled all CSS animations\u001b[22m\n\u001b[2m  - waiting for fonts to load...\u001b[22m\n\u001b[2m  - fonts loaded\u001b[22m\n\u001b[2m  - attempting scroll into view action\u001b[22m\n\u001b[2m    - waiting for element to be stable\u001b[22m\n\u001b[2m  - 507906 pixels (ratio 0.84 of all image pixels) are different.\u001b[22m\n\u001b[2m  - waiting 250ms before taking screenshot\u001b[22m\n\u001b[2m  - waiting for locator('.leaflet-container').first()\u001b[22m\n\u001b[2m    - locator resolved to <div tabindex=\"0\" class=\"z-0 leaflet-map-container leaflet-container leaflet-touch leaflet-fade-anim leaflet-grab leaflet-touch-drag leaflet-touch-zoom\">…</div>\u001b[22m\n\u001b[2m  - taking element screenshot\u001b[22m\n\u001b[2m    - disabled all CSS animations\u001b[22m\n\u001b[2m  - waiting for fonts to load...\u001b[22m\n\u001b[2m  - fonts loaded\u001b[22m\n\u001b[2m  - attempting scroll into view action\u001b[22m\n\u001b[2m    - waiting for element to be stable\u001b[22m\n\u001b[2m  - captured a stable screenshot\u001b[22m\n\u001b[2m  - 450151 pixels (ratio 0.74 of all image pixels) are different.\u001b[22m\n\n    at E:\\Augment Code Testing\\sri-lanka-tourist-guide\\tests\\visual.spec.ts:97:62", "location": {"file": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\tests\\visual.spec.ts", "column": 62, "line": 97}, "snippet": "\u001b[0m \u001b[90m  95 |\u001b[39m     \n \u001b[90m  96 |\u001b[39m     \u001b[90m// Take screenshot of map area\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m  97 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'.leaflet-container'\u001b[39m)\u001b[33m.\u001b[39mfirst())\u001b[33m.\u001b[39mtoHaveScreenshot(\u001b[32m'interactive-map.png'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                                                              \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m  98 |\u001b[39m   })\u001b[33m;\u001b[39m\n \u001b[90m  99 |\u001b[39m })\u001b[33m;\u001b[39m\n \u001b[90m 100 |\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\tests\\visual.spec.ts", "column": 62, "line": 97}, "message": "Error: \u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoHaveScreenshot\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\n  450151 pixels (ratio 0.74 of all image pixels) are different.\n\nExpected: \u001b[33mE:\\Augment Code Testing\\sri-lanka-tourist-guide\\tests\\visual.spec.ts-snapshots\\interactive-map-firefox-win32.png\u001b[39m\nReceived: \u001b[33mE:\\Augment Code Testing\\sri-lanka-tourist-guide\\test-results\\visual-Visual-Testing-Map-component-rendering-firefox\\interactive-map-actual.png\u001b[39m\n    Diff: \u001b[33mE:\\Augment Code Testing\\sri-lanka-tourist-guide\\test-results\\visual-Visual-Testing-Map-component-rendering-firefox\\interactive-map-diff.png\u001b[39m\n\nCall log:\n\u001b[2m  - Expect \"toHaveScreenshot(interactive-map.png)\" with timeout 5000ms\u001b[22m\n\u001b[2m    - verifying given screenshot expectation\u001b[22m\n\u001b[2m  - waiting for locator('.leaflet-container').first()\u001b[22m\n\u001b[2m    - locator resolved to <div tabindex=\"0\" class=\"z-0 leaflet-map-container leaflet-container leaflet-touch leaflet-fade-anim leaflet-grab leaflet-touch-drag leaflet-touch-zoom\">…</div>\u001b[22m\n\u001b[2m  - taking element screenshot\u001b[22m\n\u001b[2m    - disabled all CSS animations\u001b[22m\n\u001b[2m  - waiting for fonts to load...\u001b[22m\n\u001b[2m  - fonts loaded\u001b[22m\n\u001b[2m  - attempting scroll into view action\u001b[22m\n\u001b[2m    - waiting for element to be stable\u001b[22m\n\u001b[2m  - 39806 pixels (ratio 0.07 of all image pixels) are different.\u001b[22m\n\u001b[2m  - waiting 100ms before taking screenshot\u001b[22m\n\u001b[2m  - waiting for locator('.leaflet-container').first()\u001b[22m\n\u001b[2m    - locator resolved to <div tabindex=\"0\" class=\"z-0 leaflet-map-container leaflet-container leaflet-touch leaflet-fade-anim leaflet-grab leaflet-touch-drag leaflet-touch-zoom\">…</div>\u001b[22m\n\u001b[2m  - taking element screenshot\u001b[22m\n\u001b[2m    - disabled all CSS animations\u001b[22m\n\u001b[2m  - waiting for fonts to load...\u001b[22m\n\u001b[2m  - fonts loaded\u001b[22m\n\u001b[2m  - attempting scroll into view action\u001b[22m\n\u001b[2m    - waiting for element to be stable\u001b[22m\n\u001b[2m  - 507906 pixels (ratio 0.84 of all image pixels) are different.\u001b[22m\n\u001b[2m  - waiting 250ms before taking screenshot\u001b[22m\n\u001b[2m  - waiting for locator('.leaflet-container').first()\u001b[22m\n\u001b[2m    - locator resolved to <div tabindex=\"0\" class=\"z-0 leaflet-map-container leaflet-container leaflet-touch leaflet-fade-anim leaflet-grab leaflet-touch-drag leaflet-touch-zoom\">…</div>\u001b[22m\n\u001b[2m  - taking element screenshot\u001b[22m\n\u001b[2m    - disabled all CSS animations\u001b[22m\n\u001b[2m  - waiting for fonts to load...\u001b[22m\n\u001b[2m  - fonts loaded\u001b[22m\n\u001b[2m  - attempting scroll into view action\u001b[22m\n\u001b[2m    - waiting for element to be stable\u001b[22m\n\u001b[2m  - captured a stable screenshot\u001b[22m\n\u001b[2m  - 450151 pixels (ratio 0.74 of all image pixels) are different.\u001b[22m\n\n\n   95 |     \n   96 |     // Take screenshot of map area\n>  97 |     await expect(page.locator('.leaflet-container').first()).toHaveScreenshot('interactive-map.png');\n      |                                                              ^\n   98 |   });\n   99 | });\n  100 |\n    at E:\\Augment Code Testing\\sri-lanka-tourist-guide\\tests\\visual.spec.ts:97:62"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-06-24T05:51:14.676Z", "annotations": [], "attachments": [{"name": "interactive-map-expected.png", "contentType": "image/png", "path": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\tests\\visual.spec.ts-snapshots\\interactive-map-firefox-win32.png"}, {"name": "interactive-map-actual.png", "contentType": "image/png", "path": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\test-results\\visual-Visual-Testing-Map-component-rendering-firefox\\interactive-map-actual.png"}, {"name": "interactive-map-diff.png", "contentType": "image/png", "path": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\test-results\\visual-Visual-Testing-Map-component-rendering-firefox\\interactive-map-diff.png"}, {"name": "screenshot", "contentType": "image/png", "path": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\test-results\\visual-Visual-Testing-Map-component-rendering-firefox\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\test-results\\visual-Visual-Testing-Map-component-rendering-firefox\\video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\test-results\\visual-Visual-Testing-Map-component-rendering-firefox\\error-context.md"}], "errorLocation": {"file": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\tests\\visual.spec.ts", "column": 62, "line": 97}}], "status": "unexpected"}], "id": "8324bb52c49185e48ff4-0bf59633852b02224fa9", "file": "visual.spec.ts", "line": 88, "column": 3}, {"title": "Map component rendering", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [{"workerIndex": 2, "parallelIndex": 0, "status": "failed", "duration": 21758, "error": {"message": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoHaveScreenshot\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nLocator: locator('.leaflet-container').first()\n  Timeout 5000ms exceeded.\n\nCall log:\n\u001b[2m  - Expect \"toHaveScreenshot(interactive-map.png)\" with timeout 5000ms\u001b[22m\n\u001b[2m    - generating new stable screenshot expectation\u001b[22m\n\u001b[2m  - waiting for locator('.leaflet-container').first()\u001b[22m\n\u001b[2m    - locator resolved to <div tabindex=\"0\" class=\"z-0 leaflet-map-container leaflet-container leaflet-touch leaflet-retina leaflet-safari leaflet-fade-anim leaflet-grab leaflet-touch-drag leaflet-touch-zoom\">…</div>\u001b[22m\n\u001b[2m  - taking element screenshot\u001b[22m\n\u001b[2m    - disabled all CSS animations\u001b[22m\n\u001b[2m  - waiting for fonts to load...\u001b[22m\n\u001b[2m  - fonts loaded\u001b[22m\n\u001b[2m  - attempting scroll into view action\u001b[22m\n\u001b[2m    - waiting for element to be stable\u001b[22m\n\u001b[2m  - waiting 100ms before taking screenshot\u001b[22m\n\u001b[2m  - waiting for locator('.leaflet-container').first()\u001b[22m\n\u001b[2m    - locator resolved to <div tabindex=\"0\" class=\"z-0 leaflet-map-container leaflet-container leaflet-touch leaflet-retina leaflet-safari leaflet-fade-anim leaflet-grab leaflet-touch-drag leaflet-touch-zoom\">…</div>\u001b[22m\n\u001b[2m  - Timeout 5000ms exceeded.\u001b[22m\n", "stack": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoHaveScreenshot\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nLocator: locator('.leaflet-container').first()\n  Timeout 5000ms exceeded.\n\nCall log:\n\u001b[2m  - Expect \"toHaveScreenshot(interactive-map.png)\" with timeout 5000ms\u001b[22m\n\u001b[2m    - generating new stable screenshot expectation\u001b[22m\n\u001b[2m  - waiting for locator('.leaflet-container').first()\u001b[22m\n\u001b[2m    - locator resolved to <div tabindex=\"0\" class=\"z-0 leaflet-map-container leaflet-container leaflet-touch leaflet-retina leaflet-safari leaflet-fade-anim leaflet-grab leaflet-touch-drag leaflet-touch-zoom\">…</div>\u001b[22m\n\u001b[2m  - taking element screenshot\u001b[22m\n\u001b[2m    - disabled all CSS animations\u001b[22m\n\u001b[2m  - waiting for fonts to load...\u001b[22m\n\u001b[2m  - fonts loaded\u001b[22m\n\u001b[2m  - attempting scroll into view action\u001b[22m\n\u001b[2m    - waiting for element to be stable\u001b[22m\n\u001b[2m  - waiting 100ms before taking screenshot\u001b[22m\n\u001b[2m  - waiting for locator('.leaflet-container').first()\u001b[22m\n\u001b[2m    - locator resolved to <div tabindex=\"0\" class=\"z-0 leaflet-map-container leaflet-container leaflet-touch leaflet-retina leaflet-safari leaflet-fade-anim leaflet-grab leaflet-touch-drag leaflet-touch-zoom\">…</div>\u001b[22m\n\u001b[2m  - Timeout 5000ms exceeded.\u001b[22m\n\n    at E:\\Augment Code Testing\\sri-lanka-tourist-guide\\tests\\visual.spec.ts:97:62", "location": {"file": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\tests\\visual.spec.ts", "column": 62, "line": 97}, "snippet": "\u001b[0m \u001b[90m  95 |\u001b[39m     \n \u001b[90m  96 |\u001b[39m     \u001b[90m// Take screenshot of map area\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m  97 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'.leaflet-container'\u001b[39m)\u001b[33m.\u001b[39mfirst())\u001b[33m.\u001b[39mtoHaveScreenshot(\u001b[32m'interactive-map.png'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                                                              \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m  98 |\u001b[39m   })\u001b[33m;\u001b[39m\n \u001b[90m  99 |\u001b[39m })\u001b[33m;\u001b[39m\n \u001b[90m 100 |\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\tests\\visual.spec.ts", "column": 62, "line": 97}, "message": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoHaveScreenshot\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nLocator: locator('.leaflet-container').first()\n  Timeout 5000ms exceeded.\n\nCall log:\n\u001b[2m  - Expect \"toHaveScreenshot(interactive-map.png)\" with timeout 5000ms\u001b[22m\n\u001b[2m    - generating new stable screenshot expectation\u001b[22m\n\u001b[2m  - waiting for locator('.leaflet-container').first()\u001b[22m\n\u001b[2m    - locator resolved to <div tabindex=\"0\" class=\"z-0 leaflet-map-container leaflet-container leaflet-touch leaflet-retina leaflet-safari leaflet-fade-anim leaflet-grab leaflet-touch-drag leaflet-touch-zoom\">…</div>\u001b[22m\n\u001b[2m  - taking element screenshot\u001b[22m\n\u001b[2m    - disabled all CSS animations\u001b[22m\n\u001b[2m  - waiting for fonts to load...\u001b[22m\n\u001b[2m  - fonts loaded\u001b[22m\n\u001b[2m  - attempting scroll into view action\u001b[22m\n\u001b[2m    - waiting for element to be stable\u001b[22m\n\u001b[2m  - waiting 100ms before taking screenshot\u001b[22m\n\u001b[2m  - waiting for locator('.leaflet-container').first()\u001b[22m\n\u001b[2m    - locator resolved to <div tabindex=\"0\" class=\"z-0 leaflet-map-container leaflet-container leaflet-touch leaflet-retina leaflet-safari leaflet-fade-anim leaflet-grab leaflet-touch-drag leaflet-touch-zoom\">…</div>\u001b[22m\n\u001b[2m  - Timeout 5000ms exceeded.\u001b[22m\n\n\n   95 |     \n   96 |     // Take screenshot of map area\n>  97 |     await expect(page.locator('.leaflet-container').first()).toHaveScreenshot('interactive-map.png');\n      |                                                              ^\n   98 |   });\n   99 | });\n  100 |\n    at E:\\Augment Code Testing\\sri-lanka-tourist-guide\\tests\\visual.spec.ts:97:62"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-06-24T05:51:55.666Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\test-results\\visual-Visual-Testing-Map-component-rendering-webkit\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\test-results\\visual-Visual-Testing-Map-component-rendering-webkit\\video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\test-results\\visual-Visual-Testing-Map-component-rendering-webkit\\error-context.md"}], "errorLocation": {"file": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\tests\\visual.spec.ts", "column": 62, "line": 97}}], "status": "unexpected"}], "id": "8324bb52c49185e48ff4-74f17edc94f66419b0ca", "file": "visual.spec.ts", "line": 88, "column": 3}, {"title": "Map component rendering", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [{"workerIndex": 3, "parallelIndex": 1, "status": "failed", "duration": 12389, "error": {"message": "Error: \u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoHaveScreenshot\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\n  66339 pixels (ratio 0.37 of all image pixels) are different.\n\nExpected: \u001b[33mE:\\Augment Code Testing\\sri-lanka-tourist-guide\\tests\\visual.spec.ts-snapshots\\interactive-map-Mobile-Chrome-win32.png\u001b[39m\nReceived: \u001b[33mE:\\Augment Code Testing\\sri-lanka-tourist-guide\\test-results\\visual-Visual-Testing-Map-component-rendering-Mobile-Chrome\\interactive-map-actual.png\u001b[39m\n    Diff: \u001b[33mE:\\Augment Code Testing\\sri-lanka-tourist-guide\\test-results\\visual-Visual-Testing-Map-component-rendering-Mobile-Chrome\\interactive-map-diff.png\u001b[39m\n\nCall log:\n\u001b[2m  - Expect \"toHaveScreenshot(interactive-map.png)\" with timeout 5000ms\u001b[22m\n\u001b[2m    - verifying given screenshot expectation\u001b[22m\n\u001b[2m  - waiting for locator('.leaflet-container').first()\u001b[22m\n\u001b[2m    - locator resolved to <div tabindex=\"0\" class=\"z-0 leaflet-map-container leaflet-container leaflet-touch leaflet-retina leaflet-fade-anim leaflet-grab leaflet-touch-drag leaflet-touch-zoom\">…</div>\u001b[22m\n\u001b[2m  - taking element screenshot\u001b[22m\n\u001b[2m    - disabled all CSS animations\u001b[22m\n\u001b[2m  - waiting for fonts to load...\u001b[22m\n\u001b[2m  - fonts loaded\u001b[22m\n\u001b[2m  - attempting scroll into view action\u001b[22m\n\u001b[2m    - waiting for element to be stable\u001b[22m\n\u001b[2m  - 61950 pixels (ratio 0.35 of all image pixels) are different.\u001b[22m\n\u001b[2m  - waiting 100ms before taking screenshot\u001b[22m\n\u001b[2m  - waiting for locator('.leaflet-container').first()\u001b[22m\n\u001b[2m    - locator resolved to <div tabindex=\"0\" class=\"z-0 leaflet-map-container leaflet-container leaflet-touch leaflet-retina leaflet-fade-anim leaflet-grab leaflet-touch-drag leaflet-touch-zoom\">…</div>\u001b[22m\n\u001b[2m  - taking element screenshot\u001b[22m\n\u001b[2m    - disabled all CSS animations\u001b[22m\n\u001b[2m  - waiting for fonts to load...\u001b[22m\n\u001b[2m  - fonts loaded\u001b[22m\n\u001b[2m  - attempting scroll into view action\u001b[22m\n\u001b[2m    - waiting for element to be stable\u001b[22m\n\u001b[2m  - 123705 pixels (ratio 0.69 of all image pixels) are different.\u001b[22m\n\u001b[2m  - waiting 250ms before taking screenshot\u001b[22m\n\u001b[2m  - waiting for locator('.leaflet-container').first()\u001b[22m\n\u001b[2m    - locator resolved to <div tabindex=\"0\" class=\"z-0 leaflet-map-container leaflet-container leaflet-touch leaflet-retina leaflet-fade-anim leaflet-grab leaflet-touch-drag leaflet-touch-zoom\">…</div>\u001b[22m\n\u001b[2m  - taking element screenshot\u001b[22m\n\u001b[2m    - disabled all CSS animations\u001b[22m\n\u001b[2m  - waiting for fonts to load...\u001b[22m\n\u001b[2m  - fonts loaded\u001b[22m\n\u001b[2m  - attempting scroll into view action\u001b[22m\n\u001b[2m    - waiting for element to be stable\u001b[22m\n\u001b[2m  - captured a stable screenshot\u001b[22m\n\u001b[2m  - 66339 pixels (ratio 0.37 of all image pixels) are different.\u001b[22m\n", "stack": "Error: \u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoHaveScreenshot\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\n  66339 pixels (ratio 0.37 of all image pixels) are different.\n\nExpected: \u001b[33mE:\\Augment Code Testing\\sri-lanka-tourist-guide\\tests\\visual.spec.ts-snapshots\\interactive-map-Mobile-Chrome-win32.png\u001b[39m\nReceived: \u001b[33mE:\\Augment Code Testing\\sri-lanka-tourist-guide\\test-results\\visual-Visual-Testing-Map-component-rendering-Mobile-Chrome\\interactive-map-actual.png\u001b[39m\n    Diff: \u001b[33mE:\\Augment Code Testing\\sri-lanka-tourist-guide\\test-results\\visual-Visual-Testing-Map-component-rendering-Mobile-Chrome\\interactive-map-diff.png\u001b[39m\n\nCall log:\n\u001b[2m  - Expect \"toHaveScreenshot(interactive-map.png)\" with timeout 5000ms\u001b[22m\n\u001b[2m    - verifying given screenshot expectation\u001b[22m\n\u001b[2m  - waiting for locator('.leaflet-container').first()\u001b[22m\n\u001b[2m    - locator resolved to <div tabindex=\"0\" class=\"z-0 leaflet-map-container leaflet-container leaflet-touch leaflet-retina leaflet-fade-anim leaflet-grab leaflet-touch-drag leaflet-touch-zoom\">…</div>\u001b[22m\n\u001b[2m  - taking element screenshot\u001b[22m\n\u001b[2m    - disabled all CSS animations\u001b[22m\n\u001b[2m  - waiting for fonts to load...\u001b[22m\n\u001b[2m  - fonts loaded\u001b[22m\n\u001b[2m  - attempting scroll into view action\u001b[22m\n\u001b[2m    - waiting for element to be stable\u001b[22m\n\u001b[2m  - 61950 pixels (ratio 0.35 of all image pixels) are different.\u001b[22m\n\u001b[2m  - waiting 100ms before taking screenshot\u001b[22m\n\u001b[2m  - waiting for locator('.leaflet-container').first()\u001b[22m\n\u001b[2m    - locator resolved to <div tabindex=\"0\" class=\"z-0 leaflet-map-container leaflet-container leaflet-touch leaflet-retina leaflet-fade-anim leaflet-grab leaflet-touch-drag leaflet-touch-zoom\">…</div>\u001b[22m\n\u001b[2m  - taking element screenshot\u001b[22m\n\u001b[2m    - disabled all CSS animations\u001b[22m\n\u001b[2m  - waiting for fonts to load...\u001b[22m\n\u001b[2m  - fonts loaded\u001b[22m\n\u001b[2m  - attempting scroll into view action\u001b[22m\n\u001b[2m    - waiting for element to be stable\u001b[22m\n\u001b[2m  - 123705 pixels (ratio 0.69 of all image pixels) are different.\u001b[22m\n\u001b[2m  - waiting 250ms before taking screenshot\u001b[22m\n\u001b[2m  - waiting for locator('.leaflet-container').first()\u001b[22m\n\u001b[2m    - locator resolved to <div tabindex=\"0\" class=\"z-0 leaflet-map-container leaflet-container leaflet-touch leaflet-retina leaflet-fade-anim leaflet-grab leaflet-touch-drag leaflet-touch-zoom\">…</div>\u001b[22m\n\u001b[2m  - taking element screenshot\u001b[22m\n\u001b[2m    - disabled all CSS animations\u001b[22m\n\u001b[2m  - waiting for fonts to load...\u001b[22m\n\u001b[2m  - fonts loaded\u001b[22m\n\u001b[2m  - attempting scroll into view action\u001b[22m\n\u001b[2m    - waiting for element to be stable\u001b[22m\n\u001b[2m  - captured a stable screenshot\u001b[22m\n\u001b[2m  - 66339 pixels (ratio 0.37 of all image pixels) are different.\u001b[22m\n\n    at E:\\Augment Code Testing\\sri-lanka-tourist-guide\\tests\\visual.spec.ts:97:62", "location": {"file": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\tests\\visual.spec.ts", "column": 62, "line": 97}, "snippet": "\u001b[0m \u001b[90m  95 |\u001b[39m     \n \u001b[90m  96 |\u001b[39m     \u001b[90m// Take screenshot of map area\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m  97 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'.leaflet-container'\u001b[39m)\u001b[33m.\u001b[39mfirst())\u001b[33m.\u001b[39mtoHaveScreenshot(\u001b[32m'interactive-map.png'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                                                              \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m  98 |\u001b[39m   })\u001b[33m;\u001b[39m\n \u001b[90m  99 |\u001b[39m })\u001b[33m;\u001b[39m\n \u001b[90m 100 |\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\tests\\visual.spec.ts", "column": 62, "line": 97}, "message": "Error: \u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoHaveScreenshot\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\n  66339 pixels (ratio 0.37 of all image pixels) are different.\n\nExpected: \u001b[33mE:\\Augment Code Testing\\sri-lanka-tourist-guide\\tests\\visual.spec.ts-snapshots\\interactive-map-Mobile-Chrome-win32.png\u001b[39m\nReceived: \u001b[33mE:\\Augment Code Testing\\sri-lanka-tourist-guide\\test-results\\visual-Visual-Testing-Map-component-rendering-Mobile-Chrome\\interactive-map-actual.png\u001b[39m\n    Diff: \u001b[33mE:\\Augment Code Testing\\sri-lanka-tourist-guide\\test-results\\visual-Visual-Testing-Map-component-rendering-Mobile-Chrome\\interactive-map-diff.png\u001b[39m\n\nCall log:\n\u001b[2m  - Expect \"toHaveScreenshot(interactive-map.png)\" with timeout 5000ms\u001b[22m\n\u001b[2m    - verifying given screenshot expectation\u001b[22m\n\u001b[2m  - waiting for locator('.leaflet-container').first()\u001b[22m\n\u001b[2m    - locator resolved to <div tabindex=\"0\" class=\"z-0 leaflet-map-container leaflet-container leaflet-touch leaflet-retina leaflet-fade-anim leaflet-grab leaflet-touch-drag leaflet-touch-zoom\">…</div>\u001b[22m\n\u001b[2m  - taking element screenshot\u001b[22m\n\u001b[2m    - disabled all CSS animations\u001b[22m\n\u001b[2m  - waiting for fonts to load...\u001b[22m\n\u001b[2m  - fonts loaded\u001b[22m\n\u001b[2m  - attempting scroll into view action\u001b[22m\n\u001b[2m    - waiting for element to be stable\u001b[22m\n\u001b[2m  - 61950 pixels (ratio 0.35 of all image pixels) are different.\u001b[22m\n\u001b[2m  - waiting 100ms before taking screenshot\u001b[22m\n\u001b[2m  - waiting for locator('.leaflet-container').first()\u001b[22m\n\u001b[2m    - locator resolved to <div tabindex=\"0\" class=\"z-0 leaflet-map-container leaflet-container leaflet-touch leaflet-retina leaflet-fade-anim leaflet-grab leaflet-touch-drag leaflet-touch-zoom\">…</div>\u001b[22m\n\u001b[2m  - taking element screenshot\u001b[22m\n\u001b[2m    - disabled all CSS animations\u001b[22m\n\u001b[2m  - waiting for fonts to load...\u001b[22m\n\u001b[2m  - fonts loaded\u001b[22m\n\u001b[2m  - attempting scroll into view action\u001b[22m\n\u001b[2m    - waiting for element to be stable\u001b[22m\n\u001b[2m  - 123705 pixels (ratio 0.69 of all image pixels) are different.\u001b[22m\n\u001b[2m  - waiting 250ms before taking screenshot\u001b[22m\n\u001b[2m  - waiting for locator('.leaflet-container').first()\u001b[22m\n\u001b[2m    - locator resolved to <div tabindex=\"0\" class=\"z-0 leaflet-map-container leaflet-container leaflet-touch leaflet-retina leaflet-fade-anim leaflet-grab leaflet-touch-drag leaflet-touch-zoom\">…</div>\u001b[22m\n\u001b[2m  - taking element screenshot\u001b[22m\n\u001b[2m    - disabled all CSS animations\u001b[22m\n\u001b[2m  - waiting for fonts to load...\u001b[22m\n\u001b[2m  - fonts loaded\u001b[22m\n\u001b[2m  - attempting scroll into view action\u001b[22m\n\u001b[2m    - waiting for element to be stable\u001b[22m\n\u001b[2m  - captured a stable screenshot\u001b[22m\n\u001b[2m  - 66339 pixels (ratio 0.37 of all image pixels) are different.\u001b[22m\n\n\n   95 |     \n   96 |     // Take screenshot of map area\n>  97 |     await expect(page.locator('.leaflet-container').first()).toHaveScreenshot('interactive-map.png');\n      |                                                              ^\n   98 |   });\n   99 | });\n  100 |\n    at E:\\Augment Code Testing\\sri-lanka-tourist-guide\\tests\\visual.spec.ts:97:62"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-06-24T05:51:57.976Z", "annotations": [], "attachments": [{"name": "interactive-map-expected.png", "contentType": "image/png", "path": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\tests\\visual.spec.ts-snapshots\\interactive-map-Mobile-Chrome-win32.png"}, {"name": "interactive-map-actual.png", "contentType": "image/png", "path": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\test-results\\visual-Visual-Testing-Map-component-rendering-Mobile-Chrome\\interactive-map-actual.png"}, {"name": "interactive-map-diff.png", "contentType": "image/png", "path": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\test-results\\visual-Visual-Testing-Map-component-rendering-Mobile-Chrome\\interactive-map-diff.png"}, {"name": "screenshot", "contentType": "image/png", "path": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\test-results\\visual-Visual-Testing-Map-component-rendering-Mobile-Chrome\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\test-results\\visual-Visual-Testing-Map-component-rendering-Mobile-Chrome\\video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\test-results\\visual-Visual-Testing-Map-component-rendering-Mobile-Chrome\\error-context.md"}], "errorLocation": {"file": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\tests\\visual.spec.ts", "column": 62, "line": 97}}], "status": "unexpected"}], "id": "8324bb52c49185e48ff4-aafe598a45518adb5bf3", "file": "visual.spec.ts", "line": 88, "column": 3}, {"title": "Map component rendering", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [{"workerIndex": 4, "parallelIndex": 0, "status": "failed", "duration": 15585, "error": {"message": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoHaveScreenshot\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nLocator: locator('.leaflet-container').first()\n  Timeout 5000ms exceeded.\n\nCall log:\n\u001b[2m  - Expect \"toHaveScreenshot(interactive-map.png)\" with timeout 5000ms\u001b[22m\n\u001b[2m    - generating new stable screenshot expectation\u001b[22m\n\u001b[2m  - waiting for locator('.leaflet-container').first()\u001b[22m\n\u001b[2m    - locator resolved to <div tabindex=\"0\" class=\"z-0 leaflet-map-container leaflet-container leaflet-touch leaflet-retina leaflet-safari leaflet-fade-anim leaflet-grab leaflet-touch-drag leaflet-touch-zoom\">…</div>\u001b[22m\n\u001b[2m  - taking element screenshot\u001b[22m\n\u001b[2m    - disabled all CSS animations\u001b[22m\n\u001b[2m  - waiting for fonts to load...\u001b[22m\n\u001b[2m  - fonts loaded\u001b[22m\n\u001b[2m  - attempting scroll into view action\u001b[22m\n\u001b[2m    - waiting for element to be stable\u001b[22m\n\u001b[2m  - waiting 100ms before taking screenshot\u001b[22m\n\u001b[2m  - waiting for locator('.leaflet-container').first()\u001b[22m\n\u001b[2m    - locator resolved to <div tabindex=\"0\" class=\"z-0 leaflet-map-container leaflet-container leaflet-touch leaflet-retina leaflet-safari leaflet-fade-anim leaflet-grab leaflet-touch-drag leaflet-touch-zoom\">…</div>\u001b[22m\n\u001b[2m  - taking element screenshot\u001b[22m\n\u001b[2m    - disabled all CSS animations\u001b[22m\n\u001b[2m  - waiting for fonts to load...\u001b[22m\n\u001b[2m  - fonts loaded\u001b[22m\n\u001b[2m  - attempting scroll into view action\u001b[22m\n\u001b[2m    - waiting for element to be stable\u001b[22m\n\u001b[2m  - Timeout 5000ms exceeded.\u001b[22m\n", "stack": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoHaveScreenshot\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nLocator: locator('.leaflet-container').first()\n  Timeout 5000ms exceeded.\n\nCall log:\n\u001b[2m  - Expect \"toHaveScreenshot(interactive-map.png)\" with timeout 5000ms\u001b[22m\n\u001b[2m    - generating new stable screenshot expectation\u001b[22m\n\u001b[2m  - waiting for locator('.leaflet-container').first()\u001b[22m\n\u001b[2m    - locator resolved to <div tabindex=\"0\" class=\"z-0 leaflet-map-container leaflet-container leaflet-touch leaflet-retina leaflet-safari leaflet-fade-anim leaflet-grab leaflet-touch-drag leaflet-touch-zoom\">…</div>\u001b[22m\n\u001b[2m  - taking element screenshot\u001b[22m\n\u001b[2m    - disabled all CSS animations\u001b[22m\n\u001b[2m  - waiting for fonts to load...\u001b[22m\n\u001b[2m  - fonts loaded\u001b[22m\n\u001b[2m  - attempting scroll into view action\u001b[22m\n\u001b[2m    - waiting for element to be stable\u001b[22m\n\u001b[2m  - waiting 100ms before taking screenshot\u001b[22m\n\u001b[2m  - waiting for locator('.leaflet-container').first()\u001b[22m\n\u001b[2m    - locator resolved to <div tabindex=\"0\" class=\"z-0 leaflet-map-container leaflet-container leaflet-touch leaflet-retina leaflet-safari leaflet-fade-anim leaflet-grab leaflet-touch-drag leaflet-touch-zoom\">…</div>\u001b[22m\n\u001b[2m  - taking element screenshot\u001b[22m\n\u001b[2m    - disabled all CSS animations\u001b[22m\n\u001b[2m  - waiting for fonts to load...\u001b[22m\n\u001b[2m  - fonts loaded\u001b[22m\n\u001b[2m  - attempting scroll into view action\u001b[22m\n\u001b[2m    - waiting for element to be stable\u001b[22m\n\u001b[2m  - Timeout 5000ms exceeded.\u001b[22m\n\n    at E:\\Augment Code Testing\\sri-lanka-tourist-guide\\tests\\visual.spec.ts:97:62", "location": {"file": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\tests\\visual.spec.ts", "column": 62, "line": 97}, "snippet": "\u001b[0m \u001b[90m  95 |\u001b[39m     \n \u001b[90m  96 |\u001b[39m     \u001b[90m// Take screenshot of map area\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m  97 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'.leaflet-container'\u001b[39m)\u001b[33m.\u001b[39mfirst())\u001b[33m.\u001b[39mtoHaveScreenshot(\u001b[32m'interactive-map.png'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                                                              \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m  98 |\u001b[39m   })\u001b[33m;\u001b[39m\n \u001b[90m  99 |\u001b[39m })\u001b[33m;\u001b[39m\n \u001b[90m 100 |\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\tests\\visual.spec.ts", "column": 62, "line": 97}, "message": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoHaveScreenshot\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nLocator: locator('.leaflet-container').first()\n  Timeout 5000ms exceeded.\n\nCall log:\n\u001b[2m  - Expect \"toHaveScreenshot(interactive-map.png)\" with timeout 5000ms\u001b[22m\n\u001b[2m    - generating new stable screenshot expectation\u001b[22m\n\u001b[2m  - waiting for locator('.leaflet-container').first()\u001b[22m\n\u001b[2m    - locator resolved to <div tabindex=\"0\" class=\"z-0 leaflet-map-container leaflet-container leaflet-touch leaflet-retina leaflet-safari leaflet-fade-anim leaflet-grab leaflet-touch-drag leaflet-touch-zoom\">…</div>\u001b[22m\n\u001b[2m  - taking element screenshot\u001b[22m\n\u001b[2m    - disabled all CSS animations\u001b[22m\n\u001b[2m  - waiting for fonts to load...\u001b[22m\n\u001b[2m  - fonts loaded\u001b[22m\n\u001b[2m  - attempting scroll into view action\u001b[22m\n\u001b[2m    - waiting for element to be stable\u001b[22m\n\u001b[2m  - waiting 100ms before taking screenshot\u001b[22m\n\u001b[2m  - waiting for locator('.leaflet-container').first()\u001b[22m\n\u001b[2m    - locator resolved to <div tabindex=\"0\" class=\"z-0 leaflet-map-container leaflet-container leaflet-touch leaflet-retina leaflet-safari leaflet-fade-anim leaflet-grab leaflet-touch-drag leaflet-touch-zoom\">…</div>\u001b[22m\n\u001b[2m  - taking element screenshot\u001b[22m\n\u001b[2m    - disabled all CSS animations\u001b[22m\n\u001b[2m  - waiting for fonts to load...\u001b[22m\n\u001b[2m  - fonts loaded\u001b[22m\n\u001b[2m  - attempting scroll into view action\u001b[22m\n\u001b[2m    - waiting for element to be stable\u001b[22m\n\u001b[2m  - Timeout 5000ms exceeded.\u001b[22m\n\n\n   95 |     \n   96 |     // Take screenshot of map area\n>  97 |     await expect(page.locator('.leaflet-container').first()).toHaveScreenshot('interactive-map.png');\n      |                                                              ^\n   98 |   });\n   99 | });\n  100 |\n    at E:\\Augment Code Testing\\sri-lanka-tourist-guide\\tests\\visual.spec.ts:97:62"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-06-24T05:52:22.049Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\test-results\\visual-Visual-Testing-Map-component-rendering-Mobile-Safari\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\test-results\\visual-Visual-Testing-Map-component-rendering-Mobile-Safari\\video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\test-results\\visual-Visual-Testing-Map-component-rendering-Mobile-Safari\\error-context.md"}], "errorLocation": {"file": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\tests\\visual.spec.ts", "column": 62, "line": 97}}], "status": "unexpected"}], "id": "8324bb52c49185e48ff4-a3caaf7968ac05acd50e", "file": "visual.spec.ts", "line": 88, "column": 3}]}]}], "errors": [], "stats": {"startTime": "2025-06-24T05:51:08.668Z", "duration": 89246.773, "expected": 0, "skipped": 0, "unexpected": 5, "flaky": 0}}