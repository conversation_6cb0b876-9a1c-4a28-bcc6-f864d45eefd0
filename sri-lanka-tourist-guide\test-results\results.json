{"config": {"configFile": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\playwright.config.ts", "rootDir": "E:/Augment Code Testing/sri-lanka-tourist-guide/tests", "forbidOnly": false, "fullyParallel": true, "globalSetup": null, "globalTeardown": null, "globalTimeout": 0, "grep": {}, "grepInvert": null, "maxFailures": 0, "metadata": {"actualWorkers": 2}, "preserveOutput": "always", "reporter": [["html", null], ["json", {"outputFile": "test-results/results.json"}], ["junit", {"outputFile": "test-results/results.xml"}]], "reportSlowTests": {"max": 5, "threshold": 300000}, "quiet": false, "projects": [{"outputDir": "E:/Augment Code Testing/sri-lanka-tourist-guide/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 2}, "id": "chromium", "name": "chromium", "testDir": "E:/Augment Code Testing/sri-lanka-tourist-guide/tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "E:/Augment Code Testing/sri-lanka-tourist-guide/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 2}, "id": "firefox", "name": "firefox", "testDir": "E:/Augment Code Testing/sri-lanka-tourist-guide/tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "E:/Augment Code Testing/sri-lanka-tourist-guide/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 2}, "id": "webkit", "name": "webkit", "testDir": "E:/Augment Code Testing/sri-lanka-tourist-guide/tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "E:/Augment Code Testing/sri-lanka-tourist-guide/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 2}, "id": "Mobile Chrome", "name": "Mobile Chrome", "testDir": "E:/Augment Code Testing/sri-lanka-tourist-guide/tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "E:/Augment Code Testing/sri-lanka-tourist-guide/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 2}, "id": "Mobile Safari", "name": "Mobile Safari", "testDir": "E:/Augment Code Testing/sri-lanka-tourist-guide/tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}], "shard": null, "updateSnapshots": "missing", "updateSourceMethod": "patch", "version": "1.53.1", "workers": 2, "webServer": {"command": "npm run dev", "url": "http://localhost:5173", "reuseExistingServer": true, "timeout": 120000}}, "suites": [{"title": "articles.spec.ts", "file": "articles.spec.ts", "column": 0, "line": 0, "specs": [], "suites": [{"title": "Articles Page Tests", "file": "articles.spec.ts", "line": 3, "column": 6, "specs": [{"title": "should display articles page correctly", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 0, "parallelIndex": 0, "status": "passed", "duration": 10309, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-06-24T06:24:53.143Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "27dcd81ca19f76ce26fc-9adb3c4f19da471a1189", "file": "articles.spec.ts", "line": 8, "column": 3}, {"title": "should display articles page correctly", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [{"workerIndex": 1, "parallelIndex": 1, "status": "passed", "duration": 11166, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-06-24T06:24:53.143Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "27dcd81ca19f76ce26fc-b0e44c8465c7a5e0c9fc", "file": "articles.spec.ts", "line": 8, "column": 3}, {"title": "should display articles page correctly", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [{"workerIndex": 3, "parallelIndex": 0, "status": "passed", "duration": 5978, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-06-24T06:25:15.752Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "27dcd81ca19f76ce26fc-011fea280242dc3e3c9f", "file": "articles.spec.ts", "line": 8, "column": 3}, {"title": "should display articles page correctly", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [{"workerIndex": 2, "parallelIndex": 1, "status": "passed", "duration": 3613, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-06-24T06:25:13.858Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "27dcd81ca19f76ce26fc-d7ea55ae7915dba010e1", "file": "articles.spec.ts", "line": 8, "column": 3}, {"title": "should display articles page correctly", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [{"workerIndex": 4, "parallelIndex": 1, "status": "passed", "duration": 4678, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-06-24T06:25:22.220Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "27dcd81ca19f76ce26fc-027d10f1f7cdcb7f0e2e", "file": "articles.spec.ts", "line": 8, "column": 3}]}]}], "errors": [], "stats": {"startTime": "2025-06-24T06:24:51.209Z", "duration": 35994.228, "expected": 5, "skipped": 0, "unexpected": 0, "flaky": 0}}