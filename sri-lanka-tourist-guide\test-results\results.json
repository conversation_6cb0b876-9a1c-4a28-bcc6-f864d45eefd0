{"config": {"configFile": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\playwright.config.ts", "rootDir": "E:/Augment Code Testing/sri-lanka-tourist-guide/tests", "forbidOnly": false, "fullyParallel": true, "globalSetup": null, "globalTeardown": null, "globalTimeout": 0, "grep": {}, "grepInvert": null, "maxFailures": 0, "metadata": {"actualWorkers": 2}, "preserveOutput": "always", "reporter": [["html", null], ["json", {"outputFile": "test-results/results.json"}], ["junit", {"outputFile": "test-results/results.xml"}]], "reportSlowTests": {"max": 5, "threshold": 300000}, "quiet": false, "projects": [{"outputDir": "E:/Augment Code Testing/sri-lanka-tourist-guide/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 2}, "id": "chromium", "name": "chromium", "testDir": "E:/Augment Code Testing/sri-lanka-tourist-guide/tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "E:/Augment Code Testing/sri-lanka-tourist-guide/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 2}, "id": "firefox", "name": "firefox", "testDir": "E:/Augment Code Testing/sri-lanka-tourist-guide/tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "E:/Augment Code Testing/sri-lanka-tourist-guide/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 2}, "id": "webkit", "name": "webkit", "testDir": "E:/Augment Code Testing/sri-lanka-tourist-guide/tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "E:/Augment Code Testing/sri-lanka-tourist-guide/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 2}, "id": "Mobile Chrome", "name": "Mobile Chrome", "testDir": "E:/Augment Code Testing/sri-lanka-tourist-guide/tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "E:/Augment Code Testing/sri-lanka-tourist-guide/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 2}, "id": "Mobile Safari", "name": "Mobile Safari", "testDir": "E:/Augment Code Testing/sri-lanka-tourist-guide/tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}], "shard": null, "updateSnapshots": "missing", "updateSourceMethod": "patch", "version": "1.53.1", "workers": 2, "webServer": {"command": "npm run dev", "url": "http://localhost:5173", "reuseExistingServer": true, "timeout": 120000}}, "suites": [{"title": "accessibility.spec.ts", "file": "accessibility.spec.ts", "column": 0, "line": 0, "specs": [], "suites": [{"title": "Accessibility Testing", "file": "accessibility.spec.ts", "line": 3, "column": 6, "specs": [{"title": "Alternative Text for Images", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 0, "parallelIndex": 0, "status": "failed", "duration": 16456, "error": {"message": "Error: \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBe\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // Object.is equality\u001b[22m\n\nExpected: \u001b[32m0\u001b[39m\nReceived: \u001b[31m35\u001b[39m", "stack": "Error: \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBe\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // Object.is equality\u001b[22m\n\nExpected: \u001b[32m0\u001b[39m\nReceived: \u001b[31m35\u001b[39m\n    at E:\\Augment Code Testing\\sri-lanka-tourist-guide\\tests\\accessibility.spec.ts:246:34", "location": {"file": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\tests\\accessibility.spec.ts", "column": 34, "line": 246}, "snippet": "\u001b[0m \u001b[90m 244 |\u001b[39m       \n \u001b[90m 245 |\u001b[39m       console\u001b[33m.\u001b[39mlog(\u001b[32m`${url} - Images without alt text:`\u001b[39m\u001b[33m,\u001b[39m imageIssues\u001b[33m.\u001b[39mlength)\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 246 |\u001b[39m       expect(imageIssues\u001b[33m.\u001b[39mlength)\u001b[33m.\u001b[39mtoBe(\u001b[35m0\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                                  \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 247 |\u001b[39m     }\n \u001b[90m 248 |\u001b[39m   })\u001b[33m;\u001b[39m\n \u001b[90m 249 |\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\tests\\accessibility.spec.ts", "column": 34, "line": 246}, "message": "Error: \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBe\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // Object.is equality\u001b[22m\n\nExpected: \u001b[32m0\u001b[39m\nReceived: \u001b[31m35\u001b[39m\n\n\u001b[0m \u001b[90m 244 |\u001b[39m       \n \u001b[90m 245 |\u001b[39m       console\u001b[33m.\u001b[39mlog(\u001b[32m`${url} - Images without alt text:`\u001b[39m\u001b[33m,\u001b[39m imageIssues\u001b[33m.\u001b[39mlength)\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 246 |\u001b[39m       expect(imageIssues\u001b[33m.\u001b[39mlength)\u001b[33m.\u001b[39mtoBe(\u001b[35m0\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                                  \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 247 |\u001b[39m     }\n \u001b[90m 248 |\u001b[39m   })\u001b[33m;\u001b[39m\n \u001b[90m 249 |\u001b[39m\u001b[0m\n\u001b[2m    at E:\\Augment Code Testing\\sri-lanka-tourist-guide\\tests\\accessibility.spec.ts:246:34\u001b[22m"}], "stdout": [{"text": "/ - Images without alt text: \u001b[33m0\u001b[39m\n"}, {"text": "/destinations - Images without alt text: \u001b[33m35\u001b[39m\n"}], "stderr": [], "retry": 0, "startTime": "2025-06-24T06:41:43.841Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\test-results\\accessibility-Accessibilit-e553e-Alternative-Text-for-Images-chromium\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\test-results\\accessibility-Accessibilit-e553e-Alternative-Text-for-Images-chromium\\video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\test-results\\accessibility-Accessibilit-e553e-Alternative-Text-for-Images-chromium\\error-context.md"}], "errorLocation": {"file": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\tests\\accessibility.spec.ts", "column": 34, "line": 246}}], "status": "unexpected"}], "id": "ddd864604af8a6a0198f-b3b1938616930c525929", "file": "accessibility.spec.ts", "line": 230, "column": 3}, {"title": "Alternative Text for Images", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [{"workerIndex": 1, "parallelIndex": 1, "status": "failed", "duration": 18053, "error": {"message": "Error: \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBe\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // Object.is equality\u001b[22m\n\nExpected: \u001b[32m0\u001b[39m\nReceived: \u001b[31m35\u001b[39m", "stack": "Error: \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBe\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // Object.is equality\u001b[22m\n\nExpected: \u001b[32m0\u001b[39m\nReceived: \u001b[31m35\u001b[39m\n    at E:\\Augment Code Testing\\sri-lanka-tourist-guide\\tests\\accessibility.spec.ts:246:34", "location": {"file": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\tests\\accessibility.spec.ts", "column": 34, "line": 246}, "snippet": "\u001b[0m \u001b[90m 244 |\u001b[39m       \n \u001b[90m 245 |\u001b[39m       console\u001b[33m.\u001b[39mlog(\u001b[32m`${url} - Images without alt text:`\u001b[39m\u001b[33m,\u001b[39m imageIssues\u001b[33m.\u001b[39mlength)\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 246 |\u001b[39m       expect(imageIssues\u001b[33m.\u001b[39mlength)\u001b[33m.\u001b[39mtoBe(\u001b[35m0\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                                  \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 247 |\u001b[39m     }\n \u001b[90m 248 |\u001b[39m   })\u001b[33m;\u001b[39m\n \u001b[90m 249 |\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\tests\\accessibility.spec.ts", "column": 34, "line": 246}, "message": "Error: \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBe\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // Object.is equality\u001b[22m\n\nExpected: \u001b[32m0\u001b[39m\nReceived: \u001b[31m35\u001b[39m\n\n\u001b[0m \u001b[90m 244 |\u001b[39m       \n \u001b[90m 245 |\u001b[39m       console\u001b[33m.\u001b[39mlog(\u001b[32m`${url} - Images without alt text:`\u001b[39m\u001b[33m,\u001b[39m imageIssues\u001b[33m.\u001b[39mlength)\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 246 |\u001b[39m       expect(imageIssues\u001b[33m.\u001b[39mlength)\u001b[33m.\u001b[39mtoBe(\u001b[35m0\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                                  \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 247 |\u001b[39m     }\n \u001b[90m 248 |\u001b[39m   })\u001b[33m;\u001b[39m\n \u001b[90m 249 |\u001b[39m\u001b[0m\n\u001b[2m    at E:\\Augment Code Testing\\sri-lanka-tourist-guide\\tests\\accessibility.spec.ts:246:34\u001b[22m"}], "stdout": [{"text": "/ - Images without alt text: \u001b[33m0\u001b[39m\n"}, {"text": "/destinations - Images without alt text: \u001b[33m35\u001b[39m\n"}], "stderr": [], "retry": 0, "startTime": "2025-06-24T06:41:43.715Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\test-results\\accessibility-Accessibilit-e553e-Alternative-Text-for-Images-firefox\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\test-results\\accessibility-Accessibilit-e553e-Alternative-Text-for-Images-firefox\\video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\test-results\\accessibility-Accessibilit-e553e-Alternative-Text-for-Images-firefox\\error-context.md"}], "errorLocation": {"file": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\tests\\accessibility.spec.ts", "column": 34, "line": 246}}], "status": "unexpected"}], "id": "ddd864604af8a6a0198f-e74004c07f3d1583ba3c", "file": "accessibility.spec.ts", "line": 230, "column": 3}, {"title": "Alternative Text for Images", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [{"workerIndex": 2, "parallelIndex": 0, "status": "skipped", "duration": 1, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-06-24T06:42:48.936Z", "annotations": [], "attachments": []}], "status": "skipped"}], "id": "ddd864604af8a6a0198f-390344b7c732eba464aa", "file": "accessibility.spec.ts", "line": 230, "column": 3}, {"title": "Alternative Text for Images", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "ddd864604af8a6a0198f-f1570a2d2146560a98f9", "file": "accessibility.spec.ts", "line": 230, "column": 3}, {"title": "Alternative Text for Images", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "ddd864604af8a6a0198f-eafc578f5b7c2af99a19", "file": "accessibility.spec.ts", "line": 230, "column": 3}]}]}], "errors": [], "stats": {"startTime": "2025-06-24T06:41:39.814Z", "duration": 69318.00200000001, "expected": 0, "skipped": 3, "unexpected": 2, "flaky": 0}}