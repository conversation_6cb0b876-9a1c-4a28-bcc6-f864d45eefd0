{"config": {"configFile": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\playwright.config.ts", "rootDir": "E:/Augment Code Testing/sri-lanka-tourist-guide/tests", "forbidOnly": false, "fullyParallel": true, "globalSetup": null, "globalTeardown": null, "globalTimeout": 0, "grep": {}, "grepInvert": null, "maxFailures": 0, "metadata": {"actualWorkers": 2}, "preserveOutput": "always", "reporter": [["html", null], ["json", {"outputFile": "test-results/results.json"}], ["junit", {"outputFile": "test-results/results.xml"}]], "reportSlowTests": {"max": 5, "threshold": 300000}, "quiet": false, "projects": [{"outputDir": "E:/Augment Code Testing/sri-lanka-tourist-guide/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 2}, "id": "chromium", "name": "chromium", "testDir": "E:/Augment Code Testing/sri-lanka-tourist-guide/tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "E:/Augment Code Testing/sri-lanka-tourist-guide/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 2}, "id": "firefox", "name": "firefox", "testDir": "E:/Augment Code Testing/sri-lanka-tourist-guide/tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "E:/Augment Code Testing/sri-lanka-tourist-guide/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 2}, "id": "webkit", "name": "webkit", "testDir": "E:/Augment Code Testing/sri-lanka-tourist-guide/tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "E:/Augment Code Testing/sri-lanka-tourist-guide/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 2}, "id": "Mobile Chrome", "name": "Mobile Chrome", "testDir": "E:/Augment Code Testing/sri-lanka-tourist-guide/tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "E:/Augment Code Testing/sri-lanka-tourist-guide/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 2}, "id": "Mobile Safari", "name": "Mobile Safari", "testDir": "E:/Augment Code Testing/sri-lanka-tourist-guide/tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}], "shard": null, "updateSnapshots": "missing", "updateSourceMethod": "patch", "version": "1.53.1", "workers": 2, "webServer": {"command": "npm run dev", "url": "http://localhost:5173", "reuseExistingServer": true, "timeout": 120000}}, "suites": [{"title": "mobile-timeline.spec.ts", "file": "mobile-timeline.spec.ts", "column": 0, "line": 0, "specs": [], "suites": [{"title": "Mobile Timeline Layout", "file": "mobile-timeline.spec.ts", "line": 3, "column": 6, "specs": [{"title": "should display timeline correctly on mobile viewport", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 0, "parallelIndex": 0, "status": "failed", "duration": 14956, "error": {"message": "TimeoutError: page.waitForSelector: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('[data-testid=\"enhanced-timeline\"]') to be visible\u001b[22m\n", "stack": "TimeoutError: page.waitForSelector: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('[data-testid=\"enhanced-timeline\"]') to be visible\u001b[22m\n\n    at E:\\Augment Code Testing\\sri-lanka-tourist-guide\\tests\\mobile-timeline.spec.ts:14:16", "location": {"file": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\tests\\mobile-timeline.spec.ts", "column": 16, "line": 14}, "snippet": "\u001b[0m \u001b[90m 12 |\u001b[39m     \n \u001b[90m 13 |\u001b[39m     \u001b[90m// Wait for timeline to load\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 14 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForSelector(\u001b[32m'[data-testid=\"enhanced-timeline\"]'\u001b[39m\u001b[33m,\u001b[39m { timeout\u001b[33m:\u001b[39m \u001b[35m10000\u001b[39m })\n \u001b[90m    |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 15 |\u001b[39m     \n \u001b[90m 16 |\u001b[39m     \u001b[90m// Check that timeline line is positioned on the left for mobile\u001b[39m\n \u001b[90m 17 |\u001b[39m     \u001b[36mconst\u001b[39m timelineLine \u001b[33m=\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m'.absolute.left-6'\u001b[39m)\u001b[0m"}, "errors": [{"location": {"file": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\tests\\mobile-timeline.spec.ts", "column": 16, "line": 14}, "message": "TimeoutError: page.waitForSelector: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('[data-testid=\"enhanced-timeline\"]') to be visible\u001b[22m\n\n\n  12 |     \n  13 |     // Wait for timeline to load\n> 14 |     await page.waitForSelector('[data-testid=\"enhanced-timeline\"]', { timeout: 10000 })\n     |                ^\n  15 |     \n  16 |     // Check that timeline line is positioned on the left for mobile\n  17 |     const timelineLine = page.locator('.absolute.left-6')\n    at E:\\Augment Code Testing\\sri-lanka-tourist-guide\\tests\\mobile-timeline.spec.ts:14:16"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-06-24T10:31:04.201Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\test-results\\mobile-timeline-Mobile-Tim-846c2-orrectly-on-mobile-viewport-chromium\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\test-results\\mobile-timeline-Mobile-Tim-846c2-orrectly-on-mobile-viewport-chromium\\video.webm"}], "errorLocation": {"file": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\tests\\mobile-timeline.spec.ts", "column": 16, "line": 14}}], "status": "unexpected"}], "id": "5a25f56fc10133cc8503-b5c85553544c49c54b1d", "file": "mobile-timeline.spec.ts", "line": 9, "column": 3}, {"title": "should display timeline correctly on desktop viewport", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 1, "parallelIndex": 1, "status": "failed", "duration": 16118, "error": {"message": "TimeoutError: page.waitForSelector: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('[data-testid=\"enhanced-timeline\"]') to be visible\u001b[22m\n", "stack": "TimeoutError: page.waitForSelector: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('[data-testid=\"enhanced-timeline\"]') to be visible\u001b[22m\n\n    at E:\\Augment Code Testing\\sri-lanka-tourist-guide\\tests\\mobile-timeline.spec.ts:45:16", "location": {"file": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\tests\\mobile-timeline.spec.ts", "column": 16, "line": 45}, "snippet": "\u001b[0m \u001b[90m 43 |\u001b[39m     \n \u001b[90m 44 |\u001b[39m     \u001b[90m// Wait for timeline to load\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 45 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForSelector(\u001b[32m'[data-testid=\"enhanced-timeline\"]'\u001b[39m\u001b[33m,\u001b[39m { timeout\u001b[33m:\u001b[39m \u001b[35m10000\u001b[39m })\n \u001b[90m    |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 46 |\u001b[39m     \n \u001b[90m 47 |\u001b[39m     \u001b[90m// Check that timeline line is centered for desktop\u001b[39m\n \u001b[90m 48 |\u001b[39m     \u001b[36mconst\u001b[39m timelineLine \u001b[33m=\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m'.md\\\\:left-1\\\\/2'\u001b[39m)\u001b[0m"}, "errors": [{"location": {"file": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\tests\\mobile-timeline.spec.ts", "column": 16, "line": 45}, "message": "TimeoutError: page.waitForSelector: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('[data-testid=\"enhanced-timeline\"]') to be visible\u001b[22m\n\n\n  43 |     \n  44 |     // Wait for timeline to load\n> 45 |     await page.waitForSelector('[data-testid=\"enhanced-timeline\"]', { timeout: 10000 })\n     |                ^\n  46 |     \n  47 |     // Check that timeline line is centered for desktop\n  48 |     const timelineLine = page.locator('.md\\\\:left-1\\\\/2')\n    at E:\\Augment Code Testing\\sri-lanka-tourist-guide\\tests\\mobile-timeline.spec.ts:45:16"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-06-24T10:31:04.163Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\test-results\\mobile-timeline-Mobile-Tim-ddc63-rrectly-on-desktop-viewport-chromium\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\test-results\\mobile-timeline-Mobile-Tim-ddc63-rrectly-on-desktop-viewport-chromium\\video.webm"}], "errorLocation": {"file": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\tests\\mobile-timeline.spec.ts", "column": 16, "line": 45}}], "status": "unexpected"}], "id": "5a25f56fc10133cc8503-3329d855e042a3ec6252", "file": "mobile-timeline.spec.ts", "line": 40, "column": 3}, {"title": "should have responsive text sizes", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 2, "parallelIndex": 0, "status": "failed", "duration": 8665, "error": {"message": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoHaveClass\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nLocator: locator('h3').first()\nExpected pattern: \u001b[32m/text-lg/\u001b[39m\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toHaveClass\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('h3').first()\u001b[22m\n", "stack": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoHaveClass\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nLocator: locator('h3').first()\nExpected pattern: \u001b[32m/text-lg/\u001b[39m\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toHaveClass\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('h3').first()\u001b[22m\n\n    at E:\\Augment Code Testing\\sri-lanka-tourist-guide\\tests\\mobile-timeline.spec.ts:75:25", "location": {"file": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\tests\\mobile-timeline.spec.ts", "column": 25, "line": 75}, "snippet": "\u001b[0m \u001b[90m 73 |\u001b[39m     \n \u001b[90m 74 |\u001b[39m     \u001b[36mconst\u001b[39m title \u001b[33m=\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m'h3'\u001b[39m)\u001b[33m.\u001b[39mfirst()\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 75 |\u001b[39m     \u001b[36mawait\u001b[39m expect(title)\u001b[33m.\u001b[39mtoHaveClass(\u001b[35m/text-lg/\u001b[39m)\n \u001b[90m    |\u001b[39m                         \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 76 |\u001b[39m     \u001b[36mawait\u001b[39m expect(title)\u001b[33m.\u001b[39mtoHaveClass(\u001b[35m/md:text-2xl/\u001b[39m)\n \u001b[90m 77 |\u001b[39m     \n \u001b[90m 78 |\u001b[39m     \u001b[36mconst\u001b[39m description \u001b[33m=\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m'.timeline-content p'\u001b[39m)\u001b[33m.\u001b[39mfirst()\u001b[0m"}, "errors": [{"location": {"file": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\tests\\mobile-timeline.spec.ts", "column": 25, "line": 75}, "message": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoHaveClass\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nLocator: locator('h3').first()\nExpected pattern: \u001b[32m/text-lg/\u001b[39m\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toHaveClass\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('h3').first()\u001b[22m\n\n\n  73 |     \n  74 |     const title = page.locator('h3').first()\n> 75 |     await expect(title).toHaveClass(/text-lg/)\n     |                         ^\n  76 |     await expect(title).toHaveClass(/md:text-2xl/)\n  77 |     \n  78 |     const description = page.locator('.timeline-content p').first()\n    at E:\\Augment Code Testing\\sri-lanka-tourist-guide\\tests\\mobile-timeline.spec.ts:75:25"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-06-24T10:31:21.542Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\test-results\\mobile-timeline-Mobile-Tim-07475--have-responsive-text-sizes-chromium\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\test-results\\mobile-timeline-Mobile-Tim-07475--have-responsive-text-sizes-chromium\\video.webm"}], "errorLocation": {"file": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\tests\\mobile-timeline.spec.ts", "column": 25, "line": 75}}], "status": "unexpected"}], "id": "5a25f56fc10133cc8503-aa23e9402f7d3ee8e2e8", "file": "mobile-timeline.spec.ts", "line": 70, "column": 3}, {"title": "should handle scroll-triggered animations on mobile", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 3, "parallelIndex": 1, "status": "failed", "duration": 10141, "error": {"message": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('.timeline-content').first()\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('.timeline-content').first()\u001b[22m\n", "stack": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('.timeline-content').first()\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('.timeline-content').first()\u001b[22m\n\n    at E:\\Augment Code Testing\\sri-lanka-tourist-guide\\tests\\mobile-timeline.spec.ts:98:29", "location": {"file": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\tests\\mobile-timeline.spec.ts", "column": 29, "line": 98}, "snippet": "\u001b[0m \u001b[90m  96 |\u001b[39m     \n \u001b[90m  97 |\u001b[39m     \u001b[90m// The item should be visible after scroll trigger\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m  98 |\u001b[39m     \u001b[36mawait\u001b[39m expect(firstItem)\u001b[33m.\u001b[39mtoBeVisible()\n \u001b[90m     |\u001b[39m                             \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m  99 |\u001b[39m   })\n \u001b[90m 100 |\u001b[39m\n \u001b[90m 101 |\u001b[39m   test(\u001b[32m'should maintain accessibility on mobile'\u001b[39m\u001b[33m,\u001b[39m \u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\u001b[0m"}, "errors": [{"location": {"file": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\tests\\mobile-timeline.spec.ts", "column": 29, "line": 98}, "message": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('.timeline-content').first()\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('.timeline-content').first()\u001b[22m\n\n\n   96 |     \n   97 |     // The item should be visible after scroll trigger\n>  98 |     await expect(firstItem).toBeVisible()\n      |                             ^\n   99 |   })\n  100 |\n  101 |   test('should maintain accessibility on mobile', async ({ page }) => {\n    at E:\\Augment Code Testing\\sri-lanka-tourist-guide\\tests\\mobile-timeline.spec.ts:98:29"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-06-24T10:31:24.135Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\test-results\\mobile-timeline-Mobile-Tim-161fb-ggered-animations-on-mobile-chromium\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\test-results\\mobile-timeline-Mobile-Tim-161fb-ggered-animations-on-mobile-chromium\\video.webm"}], "errorLocation": {"file": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\tests\\mobile-timeline.spec.ts", "column": 29, "line": 98}}], "status": "unexpected"}], "id": "5a25f56fc10133cc8503-31a8e083392b7485c384", "file": "mobile-timeline.spec.ts", "line": 83, "column": 3}, {"title": "should maintain accessibility on mobile", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 4, "parallelIndex": 0, "status": "failed", "duration": 3072, "error": {"message": "Error: \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBeGreater<PERSON>han\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nExpected: > \u001b[32m0\u001b[39m\nReceived:   \u001b[31m0\u001b[39m", "stack": "Error: \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBeGreaterThan\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nExpected: > \u001b[32m0\u001b[39m\nReceived:   \u001b[31m0\u001b[39m\n    at E:\\Augment Code Testing\\sri-lanka-tourist-guide\\tests\\mobile-timeline.spec.ts:107:26", "location": {"file": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\tests\\mobile-timeline.spec.ts", "column": 26, "line": 107}, "snippet": "\u001b[0m \u001b[90m 105 |\u001b[39m     \u001b[36mconst\u001b[39m headings \u001b[33m=\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m'h3, h4'\u001b[39m)\n \u001b[90m 106 |\u001b[39m     \u001b[36mconst\u001b[39m headingCount \u001b[33m=\u001b[39m \u001b[36mawait\u001b[39m headings\u001b[33m.\u001b[39mcount()\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 107 |\u001b[39m     expect(headingCount)\u001b[33m.\u001b[39mtoBeGreaterThan(\u001b[35m0\u001b[39m)\n \u001b[90m     |\u001b[39m                          \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 108 |\u001b[39m     \n \u001b[90m 109 |\u001b[39m     \u001b[90m// Verify text contrast and readability\u001b[39m\n \u001b[90m 110 |\u001b[39m     \u001b[36mconst\u001b[39m textElements \u001b[33m=\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m'.text-gray-600'\u001b[39m)\u001b[0m"}, "errors": [{"location": {"file": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\tests\\mobile-timeline.spec.ts", "column": 26, "line": 107}, "message": "Error: \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBeGreaterThan\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nExpected: > \u001b[32m0\u001b[39m\nReceived:   \u001b[31m0\u001b[39m\n\n  105 |     const headings = page.locator('h3, h4')\n  106 |     const headingCount = await headings.count()\n> 107 |     expect(headingCount).toBeGreaterThan(0)\n      |                          ^\n  108 |     \n  109 |     // Verify text contrast and readability\n  110 |     const textElements = page.locator('.text-gray-600')\n    at E:\\Augment Code Testing\\sri-lanka-tourist-guide\\tests\\mobile-timeline.spec.ts:107:26"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-06-24T10:31:32.012Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\test-results\\mobile-timeline-Mobile-Tim-6fecf-ain-accessibility-on-mobile-chromium\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\test-results\\mobile-timeline-Mobile-Tim-6fecf-ain-accessibility-on-mobile-chromium\\video.webm"}], "errorLocation": {"file": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\tests\\mobile-timeline.spec.ts", "column": 26, "line": 107}}], "status": "unexpected"}], "id": "5a25f56fc10133cc8503-f2187d66a3cb6e48fe45", "file": "mobile-timeline.spec.ts", "line": 101, "column": 3}, {"title": "should handle content overflow properly on mobile", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 5, "parallelIndex": 1, "status": "failed", "duration": 15380, "error": {"message": "TimeoutError: locator.boundingBox: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('.timeline-content').first()\u001b[22m\n", "stack": "TimeoutError: locator.boundingBox: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('.timeline-content').first()\u001b[22m\n\n    at E:\\Augment Code Testing\\sri-lanka-tourist-guide\\tests\\mobile-timeline.spec.ts:131:47", "location": {"file": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\tests\\mobile-timeline.spec.ts", "column": 47, "line": 131}, "snippet": "\u001b[0m \u001b[90m 129 |\u001b[39m     \u001b[90m// Check that content doesn't overflow horizontally\u001b[39m\n \u001b[90m 130 |\u001b[39m     \u001b[36mconst\u001b[39m timelineContent \u001b[33m=\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m'.timeline-content'\u001b[39m)\u001b[33m.\u001b[39mfirst()\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 131 |\u001b[39m     \u001b[36mconst\u001b[39m boundingBox \u001b[33m=\u001b[39m \u001b[36mawait\u001b[39m timelineContent\u001b[33m.\u001b[39mboundingBox()\n \u001b[90m     |\u001b[39m                                               \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 132 |\u001b[39m     \n \u001b[90m 133 |\u001b[39m     \u001b[36mif\u001b[39m (boundingBox) {\n \u001b[90m 134 |\u001b[39m       \u001b[90m// Content should not exceed viewport width minus margins\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\tests\\mobile-timeline.spec.ts", "column": 47, "line": 131}, "message": "TimeoutError: locator.boundingBox: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('.timeline-content').first()\u001b[22m\n\n\n  129 |     // Check that content doesn't overflow horizontally\n  130 |     const timelineContent = page.locator('.timeline-content').first()\n> 131 |     const boundingBox = await timelineContent.boundingBox()\n      |                                               ^\n  132 |     \n  133 |     if (boundingBox) {\n  134 |       // Content should not exceed viewport width minus margins\n    at E:\\Augment Code Testing\\sri-lanka-tourist-guide\\tests\\mobile-timeline.spec.ts:131:47"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-06-24T10:31:36.553Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\test-results\\mobile-timeline-Mobile-Tim-86076-overflow-properly-on-mobile-chromium\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\test-results\\mobile-timeline-Mobile-Tim-86076-overflow-properly-on-mobile-chromium\\video.webm"}], "errorLocation": {"file": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\tests\\mobile-timeline.spec.ts", "column": 47, "line": 131}}], "status": "unexpected"}], "id": "5a25f56fc10133cc8503-4789b611e529eba08704", "file": "mobile-timeline.spec.ts", "line": 126, "column": 3}, {"title": "should display era badges responsively", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 6, "parallelIndex": 0, "status": "failed", "duration": 7875, "error": {"message": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('.rounded-full').first()\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('.rounded-full').first()\u001b[22m\n", "stack": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('.rounded-full').first()\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('.rounded-full').first()\u001b[22m\n\n    at E:\\Augment Code Testing\\sri-lanka-tourist-guide\\tests\\mobile-timeline.spec.ts:149:28", "location": {"file": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\tests\\mobile-timeline.spec.ts", "column": 28, "line": 149}, "snippet": "\u001b[0m \u001b[90m 147 |\u001b[39m     \n \u001b[90m 148 |\u001b[39m     \u001b[36mconst\u001b[39m eraBadge \u001b[33m=\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m'.rounded-full'\u001b[39m)\u001b[33m.\u001b[39mfirst()\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 149 |\u001b[39m     \u001b[36mawait\u001b[39m expect(eraBadge)\u001b[33m.\u001b[39mtoBeVisible()\n \u001b[90m     |\u001b[39m                            \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 150 |\u001b[39m     \n \u001b[90m 151 |\u001b[39m     \u001b[90m// Badge should have mobile-appropriate text size\u001b[39m\n \u001b[90m 152 |\u001b[39m     \u001b[36mawait\u001b[39m expect(eraBadge)\u001b[33m.\u001b[39mtoHaveClass(\u001b[35m/text-xs/\u001b[39m)\u001b[0m"}, "errors": [{"location": {"file": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\tests\\mobile-timeline.spec.ts", "column": 28, "line": 149}, "message": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('.rounded-full').first()\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('.rounded-full').first()\u001b[22m\n\n\n  147 |     \n  148 |     const eraBadge = page.locator('.rounded-full').first()\n> 149 |     await expect(eraBadge).toBeVisible()\n      |                            ^\n  150 |     \n  151 |     // Badge should have mobile-appropriate text size\n  152 |     await expect(eraBadge).toHaveClass(/text-xs/)\n    at E:\\Augment Code Testing\\sri-lanka-tourist-guide\\tests\\mobile-timeline.spec.ts:149:28"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-06-24T10:31:38.621Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\test-results\\mobile-timeline-Mobile-Tim-6f717-lay-era-badges-responsively-chromium\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\test-results\\mobile-timeline-Mobile-Tim-6f717-lay-era-badges-responsively-chromium\\video.webm"}], "errorLocation": {"file": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\tests\\mobile-timeline.spec.ts", "column": 28, "line": 149}}], "status": "unexpected"}], "id": "5a25f56fc10133cc8503-897c6972a25a99265af2", "file": "mobile-timeline.spec.ts", "line": 144, "column": 3}, {"title": "should handle touch interactions on mobile", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 7, "parallelIndex": 0, "status": "failed", "duration": 2862, "error": {"message": "Error: locator.tap: The page does not support tap. Use hasTouch context option to enable touch support.", "stack": "Error: locator.tap: The page does not support tap. Use hasTouch context option to enable touch support.\n    at E:\\Augment Code Testing\\sri-lanka-tourist-guide\\tests\\mobile-timeline.spec.ts:169:29", "location": {"file": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\tests\\mobile-timeline.spec.ts", "column": 29, "line": 169}, "snippet": "\u001b[0m \u001b[90m 167 |\u001b[39m     \n \u001b[90m 168 |\u001b[39m     \u001b[90m// Touch should not cause layout issues\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 169 |\u001b[39m     \u001b[36mawait\u001b[39m firstTimelineItem\u001b[33m.\u001b[39mtap()\n \u001b[90m     |\u001b[39m                             \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 170 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForTimeout(\u001b[35m500\u001b[39m)\n \u001b[90m 171 |\u001b[39m     \n \u001b[90m 172 |\u001b[39m     \u001b[90m// Item should still be visible and properly positioned\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\tests\\mobile-timeline.spec.ts", "column": 29, "line": 169}, "message": "Error: locator.tap: The page does not support tap. Use hasTouch context option to enable touch support.\n\n  167 |     \n  168 |     // Touch should not cause layout issues\n> 169 |     await firstTimelineItem.tap()\n      |                             ^\n  170 |     await page.waitForTimeout(500)\n  171 |     \n  172 |     // Item should still be visible and properly positioned\n    at E:\\Augment Code Testing\\sri-lanka-tourist-guide\\tests\\mobile-timeline.spec.ts:169:29"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-06-24T10:31:48.409Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\test-results\\mobile-timeline-Mobile-Tim-eeae3-ouch-interactions-on-mobile-chromium\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\test-results\\mobile-timeline-Mobile-Tim-eeae3-ouch-interactions-on-mobile-chromium\\video.webm"}], "errorLocation": {"file": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\tests\\mobile-timeline.spec.ts", "column": 29, "line": 169}}], "status": "unexpected"}], "id": "5a25f56fc10133cc8503-ba34ca72cb69ccdd7632", "file": "mobile-timeline.spec.ts", "line": 162, "column": 3}, {"title": "should display timeline correctly on mobile viewport", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [{"workerIndex": 8, "parallelIndex": 0, "status": "timedOut", "duration": 30481, "error": {"message": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m", "stack": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m"}, "errors": [{"message": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m"}, {"location": {"file": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\tests\\mobile-timeline.spec.ts", "column": 16, "line": 14}, "message": "Error: page.waitForSelector: Test timeout of 30000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('[data-testid=\"enhanced-timeline\"]') to be visible\u001b[22m\n\n\n  12 |     \n  13 |     // Wait for timeline to load\n> 14 |     await page.waitForSelector('[data-testid=\"enhanced-timeline\"]', { timeout: 10000 })\n     |                ^\n  15 |     \n  16 |     // Check that timeline line is positioned on the left for mobile\n  17 |     const timelineLine = page.locator('.absolute.left-6')\n    at E:\\Augment Code Testing\\sri-lanka-tourist-guide\\tests\\mobile-timeline.spec.ts:14:16"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-06-24T10:31:53.626Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\test-results\\mobile-timeline-Mobile-Tim-846c2-orrectly-on-mobile-viewport-firefox\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\test-results\\mobile-timeline-Mobile-Tim-846c2-orrectly-on-mobile-viewport-firefox\\video.webm"}]}], "status": "unexpected"}], "id": "5a25f56fc10133cc8503-58ff23f811b40bb70344", "file": "mobile-timeline.spec.ts", "line": 9, "column": 3}, {"title": "should display timeline correctly on desktop viewport", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [{"workerIndex": 9, "parallelIndex": 1, "status": "failed", "duration": 18770, "error": {"message": "TimeoutError: page.waitForSelector: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('[data-testid=\"enhanced-timeline\"]') to be visible\u001b[22m\n", "stack": "TimeoutError: page.waitForSelector: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('[data-testid=\"enhanced-timeline\"]') to be visible\u001b[22m\n\n    at E:\\Augment Code Testing\\sri-lanka-tourist-guide\\tests\\mobile-timeline.spec.ts:45:16", "location": {"file": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\tests\\mobile-timeline.spec.ts", "column": 16, "line": 45}, "snippet": "\u001b[0m \u001b[90m 43 |\u001b[39m     \n \u001b[90m 44 |\u001b[39m     \u001b[90m// Wait for timeline to load\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 45 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForSelector(\u001b[32m'[data-testid=\"enhanced-timeline\"]'\u001b[39m\u001b[33m,\u001b[39m { timeout\u001b[33m:\u001b[39m \u001b[35m10000\u001b[39m })\n \u001b[90m    |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 46 |\u001b[39m     \n \u001b[90m 47 |\u001b[39m     \u001b[90m// Check that timeline line is centered for desktop\u001b[39m\n \u001b[90m 48 |\u001b[39m     \u001b[36mconst\u001b[39m timelineLine \u001b[33m=\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m'.md\\\\:left-1\\\\/2'\u001b[39m)\u001b[0m"}, "errors": [{"location": {"file": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\tests\\mobile-timeline.spec.ts", "column": 16, "line": 45}, "message": "TimeoutError: page.waitForSelector: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('[data-testid=\"enhanced-timeline\"]') to be visible\u001b[22m\n\n\n  43 |     \n  44 |     // Wait for timeline to load\n> 45 |     await page.waitForSelector('[data-testid=\"enhanced-timeline\"]', { timeout: 10000 })\n     |                ^\n  46 |     \n  47 |     // Check that timeline line is centered for desktop\n  48 |     const timelineLine = page.locator('.md\\\\:left-1\\\\/2')\n    at E:\\Augment Code Testing\\sri-lanka-tourist-guide\\tests\\mobile-timeline.spec.ts:45:16"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-06-24T10:31:54.149Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\test-results\\mobile-timeline-Mobile-Tim-ddc63-rrectly-on-desktop-viewport-firefox\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\test-results\\mobile-timeline-Mobile-Tim-ddc63-rrectly-on-desktop-viewport-firefox\\video.webm"}], "errorLocation": {"file": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\tests\\mobile-timeline.spec.ts", "column": 16, "line": 45}}], "status": "unexpected"}], "id": "5a25f56fc10133cc8503-f8275ae58def4a152c14", "file": "mobile-timeline.spec.ts", "line": 40, "column": 3}, {"title": "should have responsive text sizes", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [{"workerIndex": 10, "parallelIndex": 1, "status": "failed", "duration": 13422, "error": {"message": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoHaveClass\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nLocator: locator('h3').first()\nExpected pattern: \u001b[32m/text-lg/\u001b[39m\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toHaveClass\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('h3').first()\u001b[22m\n", "stack": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoHaveClass\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nLocator: locator('h3').first()\nExpected pattern: \u001b[32m/text-lg/\u001b[39m\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toHaveClass\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('h3').first()\u001b[22m\n\n    at E:\\Augment Code Testing\\sri-lanka-tourist-guide\\tests\\mobile-timeline.spec.ts:75:25", "location": {"file": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\tests\\mobile-timeline.spec.ts", "column": 25, "line": 75}, "snippet": "\u001b[0m \u001b[90m 73 |\u001b[39m     \n \u001b[90m 74 |\u001b[39m     \u001b[36mconst\u001b[39m title \u001b[33m=\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m'h3'\u001b[39m)\u001b[33m.\u001b[39mfirst()\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 75 |\u001b[39m     \u001b[36mawait\u001b[39m expect(title)\u001b[33m.\u001b[39mtoHaveClass(\u001b[35m/text-lg/\u001b[39m)\n \u001b[90m    |\u001b[39m                         \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 76 |\u001b[39m     \u001b[36mawait\u001b[39m expect(title)\u001b[33m.\u001b[39mtoHaveClass(\u001b[35m/md:text-2xl/\u001b[39m)\n \u001b[90m 77 |\u001b[39m     \n \u001b[90m 78 |\u001b[39m     \u001b[36mconst\u001b[39m description \u001b[33m=\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m'.timeline-content p'\u001b[39m)\u001b[33m.\u001b[39mfirst()\u001b[0m"}, "errors": [{"location": {"file": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\tests\\mobile-timeline.spec.ts", "column": 25, "line": 75}, "message": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoHaveClass\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nLocator: locator('h3').first()\nExpected pattern: \u001b[32m/text-lg/\u001b[39m\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toHaveClass\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('h3').first()\u001b[22m\n\n\n  73 |     \n  74 |     const title = page.locator('h3').first()\n> 75 |     await expect(title).toHaveClass(/text-lg/)\n     |                         ^\n  76 |     await expect(title).toHaveClass(/md:text-2xl/)\n  77 |     \n  78 |     const description = page.locator('.timeline-content p').first()\n    at E:\\Augment Code Testing\\sri-lanka-tourist-guide\\tests\\mobile-timeline.spec.ts:75:25"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-06-24T10:32:22.498Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\test-results\\mobile-timeline-Mobile-Tim-07475--have-responsive-text-sizes-firefox\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\test-results\\mobile-timeline-Mobile-Tim-07475--have-responsive-text-sizes-firefox\\video.webm"}], "errorLocation": {"file": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\tests\\mobile-timeline.spec.ts", "column": 25, "line": 75}}], "status": "unexpected"}], "id": "5a25f56fc10133cc8503-79e9ed76c95fa4a138cb", "file": "mobile-timeline.spec.ts", "line": 70, "column": 3}, {"title": "should handle scroll-triggered animations on mobile", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [{"workerIndex": 11, "parallelIndex": 0, "status": "passed", "duration": 12455, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-06-24T10:32:31.002Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "5a25f56fc10133cc8503-be20a584c1501d06327c", "file": "mobile-timeline.spec.ts", "line": 83, "column": 3}, {"title": "should maintain accessibility on mobile", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [{"workerIndex": 12, "parallelIndex": 1, "status": "failed", "duration": 9715, "error": {"message": "Error: \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBeGreaterThanOrEqual\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nExpected: >= \u001b[32m16\u001b[39m\nReceived:    \u001b[31m14.400001525878906\u001b[39m", "stack": "Error: \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBeGreaterThanOrEqual\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nExpected: >= \u001b[32m16\u001b[39m\nReceived:    \u001b[31m14.400001525878906\u001b[39m\n    at E:\\Augment Code Testing\\sri-lanka-tourist-guide\\tests\\mobile-timeline.spec.ts:121:33", "location": {"file": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\tests\\mobile-timeline.spec.ts", "column": 33, "line": 121}, "snippet": "\u001b[0m \u001b[90m 119 |\u001b[39m     \u001b[36mif\u001b[39m (boundingBox) {\n \u001b[90m 120 |\u001b[39m       \u001b[90m// On mobile, dots should be at least 16px (w-4 h-4)\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 121 |\u001b[39m       expect(boundingBox\u001b[33m.\u001b[39mwidth)\u001b[33m.\u001b[39mtoBeGreaterThanOrEqual(\u001b[35m16\u001b[39m)\n \u001b[90m     |\u001b[39m                                 \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 122 |\u001b[39m       expect(boundingBox\u001b[33m.\u001b[39mheight)\u001b[33m.\u001b[39mtoBeGreaterThanOrEqual(\u001b[35m16\u001b[39m)\n \u001b[90m 123 |\u001b[39m     }\n \u001b[90m 124 |\u001b[39m   })\u001b[0m"}, "errors": [{"location": {"file": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\tests\\mobile-timeline.spec.ts", "column": 33, "line": 121}, "message": "Error: \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBeGreaterThanOrEqual\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nExpected: >= \u001b[32m16\u001b[39m\nReceived:    \u001b[31m14.400001525878906\u001b[39m\n\n  119 |     if (boundingBox) {\n  120 |       // On mobile, dots should be at least 16px (w-4 h-4)\n> 121 |       expect(boundingBox.width).toBeGreaterThanOrEqual(16)\n      |                                 ^\n  122 |       expect(boundingBox.height).toBeGreaterThanOrEqual(16)\n  123 |     }\n  124 |   })\n    at E:\\Augment Code Testing\\sri-lanka-tourist-guide\\tests\\mobile-timeline.spec.ts:121:33"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-06-24T10:32:43.678Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\test-results\\mobile-timeline-Mobile-Tim-6fecf-ain-accessibility-on-mobile-firefox\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\test-results\\mobile-timeline-Mobile-Tim-6fecf-ain-accessibility-on-mobile-firefox\\video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\test-results\\mobile-timeline-Mobile-Tim-6fecf-ain-accessibility-on-mobile-firefox\\error-context.md"}], "errorLocation": {"file": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\tests\\mobile-timeline.spec.ts", "column": 33, "line": 121}}], "status": "unexpected"}], "id": "5a25f56fc10133cc8503-138cc3148771f2ab7ed9", "file": "mobile-timeline.spec.ts", "line": 101, "column": 3}, {"title": "should handle content overflow properly on mobile", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [{"workerIndex": 11, "parallelIndex": 0, "status": "failed", "duration": 16182, "error": {"message": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoHaveCSS\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nLocator: locator('.timeline-content p').first()\nExpected string: \u001b[32m\"break-word\"\u001b[39m\nReceived string: \u001b[31m\"normal\"\u001b[39m\nCall log:\n\u001b[2m  - Expect \"toHaveCSS\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('.timeline-content p').first()\u001b[22m\n\u001b[2m    5 × locator resolved to <p class=\"text-sm md:text-base text-gray-600 mb-4 md:mb-6 leading-relaxed\">The earliest human settlements in Sri Lanka, span…</p>\u001b[22m\n\u001b[2m      - unexpected value \"normal\"\u001b[22m\n", "stack": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoHaveCSS\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nLocator: locator('.timeline-content p').first()\nExpected string: \u001b[32m\"break-word\"\u001b[39m\nReceived string: \u001b[31m\"normal\"\u001b[39m\nCall log:\n\u001b[2m  - Expect \"toHaveCSS\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('.timeline-content p').first()\u001b[22m\n\u001b[2m    5 × locator resolved to <p class=\"text-sm md:text-base text-gray-600 mb-4 md:mb-6 leading-relaxed\">The earliest human settlements in Sri Lanka, span…</p>\u001b[22m\n\u001b[2m      - unexpected value \"normal\"\u001b[22m\n\n    at E:\\Augment Code Testing\\sri-lanka-tourist-guide\\tests\\mobile-timeline.spec.ts:141:29", "location": {"file": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\tests\\mobile-timeline.spec.ts", "column": 29, "line": 141}, "snippet": "\u001b[0m \u001b[90m 139 |\u001b[39m     \u001b[36mconst\u001b[39m longTexts \u001b[33m=\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m'.timeline-content p'\u001b[39m)\n \u001b[90m 140 |\u001b[39m     \u001b[36mconst\u001b[39m firstText \u001b[33m=\u001b[39m longTexts\u001b[33m.\u001b[39mfirst()\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 141 |\u001b[39m     \u001b[36mawait\u001b[39m expect(firstText)\u001b[33m.\u001b[39mtoHaveCSS(\u001b[32m'word-wrap'\u001b[39m\u001b[33m,\u001b[39m \u001b[32m'break-word'\u001b[39m)\n \u001b[90m     |\u001b[39m                             \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 142 |\u001b[39m   })\n \u001b[90m 143 |\u001b[39m\n \u001b[90m 144 |\u001b[39m   test(\u001b[32m'should display era badges responsively'\u001b[39m\u001b[33m,\u001b[39m \u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\u001b[0m"}, "errors": [{"location": {"file": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\tests\\mobile-timeline.spec.ts", "column": 29, "line": 141}, "message": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoHaveCSS\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nLocator: locator('.timeline-content p').first()\nExpected string: \u001b[32m\"break-word\"\u001b[39m\nReceived string: \u001b[31m\"normal\"\u001b[39m\nCall log:\n\u001b[2m  - Expect \"toHaveCSS\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('.timeline-content p').first()\u001b[22m\n\u001b[2m    5 × locator resolved to <p class=\"text-sm md:text-base text-gray-600 mb-4 md:mb-6 leading-relaxed\">The earliest human settlements in Sri Lanka, span…</p>\u001b[22m\n\u001b[2m      - unexpected value \"normal\"\u001b[22m\n\n\n  139 |     const longTexts = page.locator('.timeline-content p')\n  140 |     const firstText = longTexts.first()\n> 141 |     await expect(firstText).toHaveCSS('word-wrap', 'break-word')\n      |                             ^\n  142 |   })\n  143 |\n  144 |   test('should display era badges responsively', async ({ page }) => {\n    at E:\\Augment Code Testing\\sri-lanka-tourist-guide\\tests\\mobile-timeline.spec.ts:141:29"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-06-24T10:32:44.361Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\test-results\\mobile-timeline-Mobile-Tim-86076-overflow-properly-on-mobile-firefox\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\test-results\\mobile-timeline-Mobile-Tim-86076-overflow-properly-on-mobile-firefox\\video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\test-results\\mobile-timeline-Mobile-Tim-86076-overflow-properly-on-mobile-firefox\\error-context.md"}], "errorLocation": {"file": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\tests\\mobile-timeline.spec.ts", "column": 29, "line": 141}}], "status": "unexpected"}], "id": "5a25f56fc10133cc8503-1ad6c1f9718ec6b06f5f", "file": "mobile-timeline.spec.ts", "line": 126, "column": 3}, {"title": "should display era badges responsively", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [{"workerIndex": 13, "parallelIndex": 0, "status": "failed", "duration": 20999, "error": {"message": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoHaveClass\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nLocator: locator('.rounded-full').first()\nExpected pattern: \u001b[32m/text-xs/\u001b[39m\nReceived string:  \u001b[31m\"inline-block px-3 py-1 bg-orange-100 text-orange-700 text-sm rounded-full\"\u001b[39m\nCall log:\n\u001b[2m  - Expect \"toHaveClass\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('.rounded-full').first()\u001b[22m\n\u001b[2m    8 × locator resolved to <span class=\"inline-block px-3 py-1 bg-orange-100 text-orange-700 text-sm rounded-full\">Sacred Tooth Relic</span>\u001b[22m\n\u001b[2m      - unexpected value \"inline-block px-3 py-1 bg-orange-100 text-orange-700 text-sm rounded-full\"\u001b[22m\n", "stack": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoHaveClass\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nLocator: locator('.rounded-full').first()\nExpected pattern: \u001b[32m/text-xs/\u001b[39m\nReceived string:  \u001b[31m\"inline-block px-3 py-1 bg-orange-100 text-orange-700 text-sm rounded-full\"\u001b[39m\nCall log:\n\u001b[2m  - Expect \"toHaveClass\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('.rounded-full').first()\u001b[22m\n\u001b[2m    8 × locator resolved to <span class=\"inline-block px-3 py-1 bg-orange-100 text-orange-700 text-sm rounded-full\">Sacred Tooth Relic</span>\u001b[22m\n\u001b[2m      - unexpected value \"inline-block px-3 py-1 bg-orange-100 text-orange-700 text-sm rounded-full\"\u001b[22m\n\n    at E:\\Augment Code Testing\\sri-lanka-tourist-guide\\tests\\mobile-timeline.spec.ts:152:28", "location": {"file": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\tests\\mobile-timeline.spec.ts", "column": 28, "line": 152}, "snippet": "\u001b[0m \u001b[90m 150 |\u001b[39m     \n \u001b[90m 151 |\u001b[39m     \u001b[90m// Badge should have mobile-appropriate text size\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 152 |\u001b[39m     \u001b[36mawait\u001b[39m expect(eraBadge)\u001b[33m.\u001b[39mtoHaveClass(\u001b[35m/text-xs/\u001b[39m)\n \u001b[90m     |\u001b[39m                            \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 153 |\u001b[39m     \u001b[36mawait\u001b[39m expect(eraBadge)\u001b[33m.\u001b[39mtoHaveClass(\u001b[35m/md:text-sm/\u001b[39m)\n \u001b[90m 154 |\u001b[39m     \n \u001b[90m 155 |\u001b[39m     \u001b[90m// Test desktop\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\tests\\mobile-timeline.spec.ts", "column": 28, "line": 152}, "message": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoHaveClass\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nLocator: locator('.rounded-full').first()\nExpected pattern: \u001b[32m/text-xs/\u001b[39m\nReceived string:  \u001b[31m\"inline-block px-3 py-1 bg-orange-100 text-orange-700 text-sm rounded-full\"\u001b[39m\nCall log:\n\u001b[2m  - Expect \"toHaveClass\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('.rounded-full').first()\u001b[22m\n\u001b[2m    8 × locator resolved to <span class=\"inline-block px-3 py-1 bg-orange-100 text-orange-700 text-sm rounded-full\">Sacred Tooth Relic</span>\u001b[22m\n\u001b[2m      - unexpected value \"inline-block px-3 py-1 bg-orange-100 text-orange-700 text-sm rounded-full\"\u001b[22m\n\n\n  150 |     \n  151 |     // Badge should have mobile-appropriate text size\n> 152 |     await expect(eraBadge).toHaveClass(/text-xs/)\n      |                            ^\n  153 |     await expect(eraBadge).toHaveClass(/md:text-sm/)\n  154 |     \n  155 |     // Test desktop\n    at E:\\Augment Code Testing\\sri-lanka-tourist-guide\\tests\\mobile-timeline.spec.ts:152:28"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-06-24T10:33:07.070Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\test-results\\mobile-timeline-Mobile-Tim-6f717-lay-era-badges-responsively-firefox\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\test-results\\mobile-timeline-Mobile-Tim-6f717-lay-era-badges-responsively-firefox\\video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\test-results\\mobile-timeline-Mobile-Tim-6f717-lay-era-badges-responsively-firefox\\error-context.md"}], "errorLocation": {"file": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\tests\\mobile-timeline.spec.ts", "column": 28, "line": 152}}], "status": "unexpected"}], "id": "5a25f56fc10133cc8503-20df82cec2bff7100f8f", "file": "mobile-timeline.spec.ts", "line": 144, "column": 3}, {"title": "should handle touch interactions on mobile", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [{"workerIndex": 14, "parallelIndex": 1, "status": "failed", "duration": 10873, "error": {"message": "Error: locator.tap: The page does not support tap. Use hasTouch context option to enable touch support.", "stack": "Error: locator.tap: The page does not support tap. Use hasTouch context option to enable touch support.\n    at E:\\Augment Code Testing\\sri-lanka-tourist-guide\\tests\\mobile-timeline.spec.ts:169:29", "location": {"file": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\tests\\mobile-timeline.spec.ts", "column": 29, "line": 169}, "snippet": "\u001b[0m \u001b[90m 167 |\u001b[39m     \n \u001b[90m 168 |\u001b[39m     \u001b[90m// Touch should not cause layout issues\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 169 |\u001b[39m     \u001b[36mawait\u001b[39m firstTimelineItem\u001b[33m.\u001b[39mtap()\n \u001b[90m     |\u001b[39m                             \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 170 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForTimeout(\u001b[35m500\u001b[39m)\n \u001b[90m 171 |\u001b[39m     \n \u001b[90m 172 |\u001b[39m     \u001b[90m// Item should still be visible and properly positioned\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\tests\\mobile-timeline.spec.ts", "column": 29, "line": 169}, "message": "Error: locator.tap: The page does not support tap. Use hasTouch context option to enable touch support.\n\n  167 |     \n  168 |     // Touch should not cause layout issues\n> 169 |     await firstTimelineItem.tap()\n      |                             ^\n  170 |     await page.waitForTimeout(500)\n  171 |     \n  172 |     // Item should still be visible and properly positioned\n    at E:\\Augment Code Testing\\sri-lanka-tourist-guide\\tests\\mobile-timeline.spec.ts:169:29"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-06-24T10:33:07.074Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\test-results\\mobile-timeline-Mobile-Tim-eeae3-ouch-interactions-on-mobile-firefox\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\test-results\\mobile-timeline-Mobile-Tim-eeae3-ouch-interactions-on-mobile-firefox\\video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\test-results\\mobile-timeline-Mobile-Tim-eeae3-ouch-interactions-on-mobile-firefox\\error-context.md"}], "errorLocation": {"file": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\tests\\mobile-timeline.spec.ts", "column": 29, "line": 169}}], "status": "unexpected"}], "id": "5a25f56fc10133cc8503-3dba1a6de6909f4c07ad", "file": "mobile-timeline.spec.ts", "line": 162, "column": 3}, {"title": "should display timeline correctly on mobile viewport", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [{"workerIndex": 15, "parallelIndex": 1, "status": "failed", "duration": 4982, "error": {"message": "Error: expect.toBeVisible: Error: strict mode violation: locator('.absolute.left-6') resolved to 14 elements:\n    1) <div class=\"absolute left-6 md:left-1/2 transform md:-translate-x-1/2 w-1 h-full\">…</div> aka locator('.absolute.left-6').first()\n    2) <div class=\"absolute left-6 md:relative md:left-auto z-10 transform -translate-x-1/2 md:transform-none\">…</div> aka locator('.absolute.left-6.md\\\\:relative').first()\n    3) <div class=\"absolute left-6 md:relative md:left-auto z-10 transform -translate-x-1/2 md:transform-none\">…</div> aka locator('.flex.items-center.flex-row.md\\\\:flex-row-reverse > .absolute.left-6').first()\n    4) <div class=\"absolute left-6 md:relative md:left-auto z-10 transform -translate-x-1/2 md:transform-none\">…</div> aka locator('div:nth-child(3) > .absolute.left-6')\n    5) <div class=\"absolute left-6 md:relative md:left-auto z-10 transform -translate-x-1/2 md:transform-none\">…</div> aka locator('div:nth-child(4) > .absolute.left-6')\n    6) <div class=\"absolute left-6 md:relative md:left-auto z-10 transform -translate-x-1/2 md:transform-none\">…</div> aka locator('div:nth-child(5) > .absolute.left-6')\n    7) <div class=\"absolute left-6 md:relative md:left-auto z-10 transform -translate-x-1/2 md:transform-none\">…</div> aka locator('div:nth-child(6) > .absolute.left-6')\n    8) <div class=\"absolute left-6 md:relative md:left-auto z-10 transform -translate-x-1/2 md:transform-none\">…</div> aka locator('div:nth-child(7) > .absolute.left-6')\n    9) <div class=\"absolute left-6 md:relative md:left-auto z-10 transform -translate-x-1/2 md:transform-none\">…</div> aka locator('div:nth-child(8) > .absolute.left-6')\n    10) <div class=\"absolute left-6 md:relative md:left-auto z-10 transform -translate-x-1/2 md:transform-none\">…</div> aka locator('div:nth-child(9) > .absolute.left-6')\n    ...\n\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('.absolute.left-6')\u001b[22m\n", "stack": "Error: expect.toBeVisible: Error: strict mode violation: locator('.absolute.left-6') resolved to 14 elements:\n    1) <div class=\"absolute left-6 md:left-1/2 transform md:-translate-x-1/2 w-1 h-full\">…</div> aka locator('.absolute.left-6').first()\n    2) <div class=\"absolute left-6 md:relative md:left-auto z-10 transform -translate-x-1/2 md:transform-none\">…</div> aka locator('.absolute.left-6.md\\\\:relative').first()\n    3) <div class=\"absolute left-6 md:relative md:left-auto z-10 transform -translate-x-1/2 md:transform-none\">…</div> aka locator('.flex.items-center.flex-row.md\\\\:flex-row-reverse > .absolute.left-6').first()\n    4) <div class=\"absolute left-6 md:relative md:left-auto z-10 transform -translate-x-1/2 md:transform-none\">…</div> aka locator('div:nth-child(3) > .absolute.left-6')\n    5) <div class=\"absolute left-6 md:relative md:left-auto z-10 transform -translate-x-1/2 md:transform-none\">…</div> aka locator('div:nth-child(4) > .absolute.left-6')\n    6) <div class=\"absolute left-6 md:relative md:left-auto z-10 transform -translate-x-1/2 md:transform-none\">…</div> aka locator('div:nth-child(5) > .absolute.left-6')\n    7) <div class=\"absolute left-6 md:relative md:left-auto z-10 transform -translate-x-1/2 md:transform-none\">…</div> aka locator('div:nth-child(6) > .absolute.left-6')\n    8) <div class=\"absolute left-6 md:relative md:left-auto z-10 transform -translate-x-1/2 md:transform-none\">…</div> aka locator('div:nth-child(7) > .absolute.left-6')\n    9) <div class=\"absolute left-6 md:relative md:left-auto z-10 transform -translate-x-1/2 md:transform-none\">…</div> aka locator('div:nth-child(8) > .absolute.left-6')\n    10) <div class=\"absolute left-6 md:relative md:left-auto z-10 transform -translate-x-1/2 md:transform-none\">…</div> aka locator('div:nth-child(9) > .absolute.left-6')\n    ...\n\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('.absolute.left-6')\u001b[22m\n\n    at E:\\Augment Code Testing\\sri-lanka-tourist-guide\\tests\\mobile-timeline.spec.ts:18:32", "location": {"file": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\tests\\mobile-timeline.spec.ts", "column": 32, "line": 18}, "snippet": "\u001b[0m \u001b[90m 16 |\u001b[39m     \u001b[90m// Check that timeline line is positioned on the left for mobile\u001b[39m\n \u001b[90m 17 |\u001b[39m     \u001b[36mconst\u001b[39m timelineLine \u001b[33m=\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m'.absolute.left-6'\u001b[39m)\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 18 |\u001b[39m     \u001b[36mawait\u001b[39m expect(timelineLine)\u001b[33m.\u001b[39mtoBeVisible()\n \u001b[90m    |\u001b[39m                                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 19 |\u001b[39m     \n \u001b[90m 20 |\u001b[39m     \u001b[90m// Check that timeline items are in single column layout\u001b[39m\n \u001b[90m 21 |\u001b[39m     \u001b[36mconst\u001b[39m timelineItems \u001b[33m=\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m'.flex.items-center.flex-row'\u001b[39m)\u001b[0m"}, "errors": [{"location": {"file": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\tests\\mobile-timeline.spec.ts", "column": 32, "line": 18}, "message": "Error: expect.toBeVisible: Error: strict mode violation: locator('.absolute.left-6') resolved to 14 elements:\n    1) <div class=\"absolute left-6 md:left-1/2 transform md:-translate-x-1/2 w-1 h-full\">…</div> aka locator('.absolute.left-6').first()\n    2) <div class=\"absolute left-6 md:relative md:left-auto z-10 transform -translate-x-1/2 md:transform-none\">…</div> aka locator('.absolute.left-6.md\\\\:relative').first()\n    3) <div class=\"absolute left-6 md:relative md:left-auto z-10 transform -translate-x-1/2 md:transform-none\">…</div> aka locator('.flex.items-center.flex-row.md\\\\:flex-row-reverse > .absolute.left-6').first()\n    4) <div class=\"absolute left-6 md:relative md:left-auto z-10 transform -translate-x-1/2 md:transform-none\">…</div> aka locator('div:nth-child(3) > .absolute.left-6')\n    5) <div class=\"absolute left-6 md:relative md:left-auto z-10 transform -translate-x-1/2 md:transform-none\">…</div> aka locator('div:nth-child(4) > .absolute.left-6')\n    6) <div class=\"absolute left-6 md:relative md:left-auto z-10 transform -translate-x-1/2 md:transform-none\">…</div> aka locator('div:nth-child(5) > .absolute.left-6')\n    7) <div class=\"absolute left-6 md:relative md:left-auto z-10 transform -translate-x-1/2 md:transform-none\">…</div> aka locator('div:nth-child(6) > .absolute.left-6')\n    8) <div class=\"absolute left-6 md:relative md:left-auto z-10 transform -translate-x-1/2 md:transform-none\">…</div> aka locator('div:nth-child(7) > .absolute.left-6')\n    9) <div class=\"absolute left-6 md:relative md:left-auto z-10 transform -translate-x-1/2 md:transform-none\">…</div> aka locator('div:nth-child(8) > .absolute.left-6')\n    10) <div class=\"absolute left-6 md:relative md:left-auto z-10 transform -translate-x-1/2 md:transform-none\">…</div> aka locator('div:nth-child(9) > .absolute.left-6')\n    ...\n\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('.absolute.left-6')\u001b[22m\n\n\n  16 |     // Check that timeline line is positioned on the left for mobile\n  17 |     const timelineLine = page.locator('.absolute.left-6')\n> 18 |     await expect(timelineLine).toBeVisible()\n     |                                ^\n  19 |     \n  20 |     // Check that timeline items are in single column layout\n  21 |     const timelineItems = page.locator('.flex.items-center.flex-row')\n    at E:\\Augment Code Testing\\sri-lanka-tourist-guide\\tests\\mobile-timeline.spec.ts:18:32"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-06-24T10:33:26.371Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\test-results\\mobile-timeline-Mobile-Tim-846c2-orrectly-on-mobile-viewport-webkit\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\test-results\\mobile-timeline-Mobile-Tim-846c2-orrectly-on-mobile-viewport-webkit\\video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\test-results\\mobile-timeline-Mobile-Tim-846c2-orrectly-on-mobile-viewport-webkit\\error-context.md"}], "errorLocation": {"file": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\tests\\mobile-timeline.spec.ts", "column": 32, "line": 18}}], "status": "unexpected"}], "id": "5a25f56fc10133cc8503-592540d74bc0dc28dc26", "file": "mobile-timeline.spec.ts", "line": 9, "column": 3}, {"title": "should display timeline correctly on desktop viewport", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [{"workerIndex": 16, "parallelIndex": 0, "status": "failed", "duration": 11111, "error": {"message": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoHaveClass\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nLocator: locator('.flex.items-center').first()\nExpected pattern: \u001b[32m/md:flex-row/\u001b[39m\nReceived string:  \u001b[31m\"flex justify-between items-center py-4\"\u001b[39m\nCall log:\n\u001b[2m  - Expect \"toHaveClass\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('.flex.items-center').first()\u001b[22m\n\u001b[2m    8 × locator resolved to <div class=\"flex justify-between items-center py-4\">…</div>\u001b[22m\n\u001b[2m      - unexpected value \"flex justify-between items-center py-4\"\u001b[22m\n", "stack": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoHaveClass\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nLocator: locator('.flex.items-center').first()\nExpected pattern: \u001b[32m/md:flex-row/\u001b[39m\nReceived string:  \u001b[31m\"flex justify-between items-center py-4\"\u001b[39m\nCall log:\n\u001b[2m  - Expect \"toHaveClass\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('.flex.items-center').first()\u001b[22m\n\u001b[2m    8 × locator resolved to <div class=\"flex justify-between items-center py-4\">…</div>\u001b[22m\n\u001b[2m      - unexpected value \"flex justify-between items-center py-4\"\u001b[22m\n\n    at E:\\Augment Code Testing\\sri-lanka-tourist-guide\\tests\\mobile-timeline.spec.ts:66:29", "location": {"file": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\tests\\mobile-timeline.spec.ts", "column": 29, "line": 66}, "snippet": "\u001b[0m \u001b[90m 64 |\u001b[39m     \n \u001b[90m 65 |\u001b[39m     \u001b[90m// First item should be flex-row, second should be flex-row-reverse on desktop\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 66 |\u001b[39m     \u001b[36mawait\u001b[39m expect(firstItem)\u001b[33m.\u001b[39mtoHaveClass(\u001b[35m/md:flex-row/\u001b[39m)\n \u001b[90m    |\u001b[39m                             \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 67 |\u001b[39m     \u001b[36mawait\u001b[39m expect(secondItem)\u001b[33m.\u001b[39mtoHaveClass(\u001b[35m/md:flex-row-reverse/\u001b[39m)\n \u001b[90m 68 |\u001b[39m   })\n \u001b[90m 69 |\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\tests\\mobile-timeline.spec.ts", "column": 29, "line": 66}, "message": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoHaveClass\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nLocator: locator('.flex.items-center').first()\nExpected pattern: \u001b[32m/md:flex-row/\u001b[39m\nReceived string:  \u001b[31m\"flex justify-between items-center py-4\"\u001b[39m\nCall log:\n\u001b[2m  - Expect \"toHaveClass\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('.flex.items-center').first()\u001b[22m\n\u001b[2m    8 × locator resolved to <div class=\"flex justify-between items-center py-4\">…</div>\u001b[22m\n\u001b[2m      - unexpected value \"flex justify-between items-center py-4\"\u001b[22m\n\n\n  64 |     \n  65 |     // First item should be flex-row, second should be flex-row-reverse on desktop\n> 66 |     await expect(firstItem).toHaveClass(/md:flex-row/)\n     |                             ^\n  67 |     await expect(secondItem).toHaveClass(/md:flex-row-reverse/)\n  68 |   })\n  69 |\n    at E:\\Augment Code Testing\\sri-lanka-tourist-guide\\tests\\mobile-timeline.spec.ts:66:29"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-06-24T10:33:32.864Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\test-results\\mobile-timeline-Mobile-Tim-ddc63-rrectly-on-desktop-viewport-webkit\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\test-results\\mobile-timeline-Mobile-Tim-ddc63-rrectly-on-desktop-viewport-webkit\\video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\test-results\\mobile-timeline-Mobile-Tim-ddc63-rrectly-on-desktop-viewport-webkit\\error-context.md"}], "errorLocation": {"file": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\tests\\mobile-timeline.spec.ts", "column": 29, "line": 66}}], "status": "unexpected"}], "id": "5a25f56fc10133cc8503-b14abf1dffe7eb0c1f33", "file": "mobile-timeline.spec.ts", "line": 40, "column": 3}, {"title": "should have responsive text sizes", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [{"workerIndex": 17, "parallelIndex": 1, "status": "failed", "duration": 8577, "error": {"message": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoHaveClass\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nLocator: locator('h3').first()\nExpected pattern: \u001b[32m/text-lg/\u001b[39m\nReceived string:  \u001b[31m\"text-xl font-semibold mb-3 text-gray-900\"\u001b[39m\nCall log:\n\u001b[2m  - Expect \"toHaveClass\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('h3').first()\u001b[22m\n\u001b[2m    4 × locator resolved to <h3 class=\"text-2xl font-bold text-gradient mb-4\">Sri Lanka Guide</h3>\u001b[22m\n\u001b[2m      - unexpected value \"text-2xl font-bold text-gradient mb-4\"\u001b[22m\n\u001b[2m    4 × locator resolved to <h3 class=\"text-xl font-semibold mb-3 text-gray-900\">Buddhism & Spirituality</h3>\u001b[22m\n\u001b[2m      - unexpected value \"text-xl font-semibold mb-3 text-gray-900\"\u001b[22m\n", "stack": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoHaveClass\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nLocator: locator('h3').first()\nExpected pattern: \u001b[32m/text-lg/\u001b[39m\nReceived string:  \u001b[31m\"text-xl font-semibold mb-3 text-gray-900\"\u001b[39m\nCall log:\n\u001b[2m  - Expect \"toHaveClass\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('h3').first()\u001b[22m\n\u001b[2m    4 × locator resolved to <h3 class=\"text-2xl font-bold text-gradient mb-4\">Sri Lanka Guide</h3>\u001b[22m\n\u001b[2m      - unexpected value \"text-2xl font-bold text-gradient mb-4\"\u001b[22m\n\u001b[2m    4 × locator resolved to <h3 class=\"text-xl font-semibold mb-3 text-gray-900\">Buddhism & Spirituality</h3>\u001b[22m\n\u001b[2m      - unexpected value \"text-xl font-semibold mb-3 text-gray-900\"\u001b[22m\n\n    at E:\\Augment Code Testing\\sri-lanka-tourist-guide\\tests\\mobile-timeline.spec.ts:75:25", "location": {"file": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\tests\\mobile-timeline.spec.ts", "column": 25, "line": 75}, "snippet": "\u001b[0m \u001b[90m 73 |\u001b[39m     \n \u001b[90m 74 |\u001b[39m     \u001b[36mconst\u001b[39m title \u001b[33m=\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m'h3'\u001b[39m)\u001b[33m.\u001b[39mfirst()\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 75 |\u001b[39m     \u001b[36mawait\u001b[39m expect(title)\u001b[33m.\u001b[39mtoHaveClass(\u001b[35m/text-lg/\u001b[39m)\n \u001b[90m    |\u001b[39m                         \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 76 |\u001b[39m     \u001b[36mawait\u001b[39m expect(title)\u001b[33m.\u001b[39mtoHaveClass(\u001b[35m/md:text-2xl/\u001b[39m)\n \u001b[90m 77 |\u001b[39m     \n \u001b[90m 78 |\u001b[39m     \u001b[36mconst\u001b[39m description \u001b[33m=\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m'.timeline-content p'\u001b[39m)\u001b[33m.\u001b[39mfirst()\u001b[0m"}, "errors": [{"location": {"file": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\tests\\mobile-timeline.spec.ts", "column": 25, "line": 75}, "message": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoHaveClass\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nLocator: locator('h3').first()\nExpected pattern: \u001b[32m/text-lg/\u001b[39m\nReceived string:  \u001b[31m\"text-xl font-semibold mb-3 text-gray-900\"\u001b[39m\nCall log:\n\u001b[2m  - Expect \"toHaveClass\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('h3').first()\u001b[22m\n\u001b[2m    4 × locator resolved to <h3 class=\"text-2xl font-bold text-gradient mb-4\">Sri Lanka Guide</h3>\u001b[22m\n\u001b[2m      - unexpected value \"text-2xl font-bold text-gradient mb-4\"\u001b[22m\n\u001b[2m    4 × locator resolved to <h3 class=\"text-xl font-semibold mb-3 text-gray-900\">Buddhism & Spirituality</h3>\u001b[22m\n\u001b[2m      - unexpected value \"text-xl font-semibold mb-3 text-gray-900\"\u001b[22m\n\n\n  73 |     \n  74 |     const title = page.locator('h3').first()\n> 75 |     await expect(title).toHaveClass(/text-lg/)\n     |                         ^\n  76 |     await expect(title).toHaveClass(/md:text-2xl/)\n  77 |     \n  78 |     const description = page.locator('.timeline-content p').first()\n    at E:\\Augment Code Testing\\sri-lanka-tourist-guide\\tests\\mobile-timeline.spec.ts:75:25"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-06-24T10:33:33.554Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\test-results\\mobile-timeline-Mobile-Tim-07475--have-responsive-text-sizes-webkit\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\test-results\\mobile-timeline-Mobile-Tim-07475--have-responsive-text-sizes-webkit\\video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\test-results\\mobile-timeline-Mobile-Tim-07475--have-responsive-text-sizes-webkit\\error-context.md"}], "errorLocation": {"file": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\tests\\mobile-timeline.spec.ts", "column": 25, "line": 75}}], "status": "unexpected"}], "id": "5a25f56fc10133cc8503-cefadee0fe99b36322e9", "file": "mobile-timeline.spec.ts", "line": 70, "column": 3}, {"title": "should handle scroll-triggered animations on mobile", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [{"workerIndex": 18, "parallelIndex": 1, "status": "passed", "duration": 4846, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-06-24T10:33:43.590Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "5a25f56fc10133cc8503-bca644a7d38c55541167", "file": "mobile-timeline.spec.ts", "line": 83, "column": 3}, {"title": "should maintain accessibility on mobile", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [{"workerIndex": 19, "parallelIndex": 0, "status": "failed", "duration": 4390, "error": {"message": "Error: \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBeGreaterThanOrEqual\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nExpected: >= \u001b[32m16\u001b[39m\nReceived:    \u001b[31m14.39999771118164\u001b[39m", "stack": "Error: \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBeGreaterThanOrEqual\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nExpected: >= \u001b[32m16\u001b[39m\nReceived:    \u001b[31m14.39999771118164\u001b[39m\n    at E:\\Augment Code Testing\\sri-lanka-tourist-guide\\tests\\mobile-timeline.spec.ts:121:33", "location": {"file": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\tests\\mobile-timeline.spec.ts", "column": 33, "line": 121}, "snippet": "\u001b[0m \u001b[90m 119 |\u001b[39m     \u001b[36mif\u001b[39m (boundingBox) {\n \u001b[90m 120 |\u001b[39m       \u001b[90m// On mobile, dots should be at least 16px (w-4 h-4)\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 121 |\u001b[39m       expect(boundingBox\u001b[33m.\u001b[39mwidth)\u001b[33m.\u001b[39mtoBeGreaterThanOrEqual(\u001b[35m16\u001b[39m)\n \u001b[90m     |\u001b[39m                                 \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 122 |\u001b[39m       expect(boundingBox\u001b[33m.\u001b[39mheight)\u001b[33m.\u001b[39mtoBeGreaterThanOrEqual(\u001b[35m16\u001b[39m)\n \u001b[90m 123 |\u001b[39m     }\n \u001b[90m 124 |\u001b[39m   })\u001b[0m"}, "errors": [{"location": {"file": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\tests\\mobile-timeline.spec.ts", "column": 33, "line": 121}, "message": "Error: \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBeGreaterThanOrEqual\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nExpected: >= \u001b[32m16\u001b[39m\nReceived:    \u001b[31m14.39999771118164\u001b[39m\n\n  119 |     if (boundingBox) {\n  120 |       // On mobile, dots should be at least 16px (w-4 h-4)\n> 121 |       expect(boundingBox.width).toBeGreaterThanOrEqual(16)\n      |                                 ^\n  122 |       expect(boundingBox.height).toBeGreaterThanOrEqual(16)\n  123 |     }\n  124 |   })\n    at E:\\Augment Code Testing\\sri-lanka-tourist-guide\\tests\\mobile-timeline.spec.ts:121:33"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-06-24T10:33:46.404Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\test-results\\mobile-timeline-Mobile-Tim-6fecf-ain-accessibility-on-mobile-webkit\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\test-results\\mobile-timeline-Mobile-Tim-6fecf-ain-accessibility-on-mobile-webkit\\video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\test-results\\mobile-timeline-Mobile-Tim-6fecf-ain-accessibility-on-mobile-webkit\\error-context.md"}], "errorLocation": {"file": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\tests\\mobile-timeline.spec.ts", "column": 33, "line": 121}}], "status": "unexpected"}], "id": "5a25f56fc10133cc8503-2da3f3432057fce203ca", "file": "mobile-timeline.spec.ts", "line": 101, "column": 3}, {"title": "should handle content overflow properly on mobile", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [{"workerIndex": 18, "parallelIndex": 1, "status": "failed", "duration": 9129, "error": {"message": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoHaveCSS\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nLocator: locator('.timeline-content p').first()\nExpected string: \u001b[32m\"break-word\"\u001b[39m\nReceived string: \u001b[31m\"normal\"\u001b[39m\nCall log:\n\u001b[2m  - Expect \"toHaveCSS\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('.timeline-content p').first()\u001b[22m\n\u001b[2m    8 × locator resolved to <p class=\"text-sm md:text-base text-gray-600 mb-4 md:mb-6 leading-relaxed\">The earliest human settlements in Sri Lanka, span…</p>\u001b[22m\n\u001b[2m      - unexpected value \"normal\"\u001b[22m\n", "stack": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoHaveCSS\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nLocator: locator('.timeline-content p').first()\nExpected string: \u001b[32m\"break-word\"\u001b[39m\nReceived string: \u001b[31m\"normal\"\u001b[39m\nCall log:\n\u001b[2m  - Expect \"toHaveCSS\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('.timeline-content p').first()\u001b[22m\n\u001b[2m    8 × locator resolved to <p class=\"text-sm md:text-base text-gray-600 mb-4 md:mb-6 leading-relaxed\">The earliest human settlements in Sri Lanka, span…</p>\u001b[22m\n\u001b[2m      - unexpected value \"normal\"\u001b[22m\n\n    at E:\\Augment Code Testing\\sri-lanka-tourist-guide\\tests\\mobile-timeline.spec.ts:141:29", "location": {"file": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\tests\\mobile-timeline.spec.ts", "column": 29, "line": 141}, "snippet": "\u001b[0m \u001b[90m 139 |\u001b[39m     \u001b[36mconst\u001b[39m longTexts \u001b[33m=\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m'.timeline-content p'\u001b[39m)\n \u001b[90m 140 |\u001b[39m     \u001b[36mconst\u001b[39m firstText \u001b[33m=\u001b[39m longTexts\u001b[33m.\u001b[39mfirst()\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 141 |\u001b[39m     \u001b[36mawait\u001b[39m expect(firstText)\u001b[33m.\u001b[39mtoHaveCSS(\u001b[32m'word-wrap'\u001b[39m\u001b[33m,\u001b[39m \u001b[32m'break-word'\u001b[39m)\n \u001b[90m     |\u001b[39m                             \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 142 |\u001b[39m   })\n \u001b[90m 143 |\u001b[39m\n \u001b[90m 144 |\u001b[39m   test(\u001b[32m'should display era badges responsively'\u001b[39m\u001b[33m,\u001b[39m \u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\u001b[0m"}, "errors": [{"location": {"file": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\tests\\mobile-timeline.spec.ts", "column": 29, "line": 141}, "message": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoHaveCSS\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nLocator: locator('.timeline-content p').first()\nExpected string: \u001b[32m\"break-word\"\u001b[39m\nReceived string: \u001b[31m\"normal\"\u001b[39m\nCall log:\n\u001b[2m  - Expect \"toHaveCSS\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('.timeline-content p').first()\u001b[22m\n\u001b[2m    8 × locator resolved to <p class=\"text-sm md:text-base text-gray-600 mb-4 md:mb-6 leading-relaxed\">The earliest human settlements in Sri Lanka, span…</p>\u001b[22m\n\u001b[2m      - unexpected value \"normal\"\u001b[22m\n\n\n  139 |     const longTexts = page.locator('.timeline-content p')\n  140 |     const firstText = longTexts.first()\n> 141 |     await expect(firstText).toHaveCSS('word-wrap', 'break-word')\n      |                             ^\n  142 |   })\n  143 |\n  144 |   test('should display era badges responsively', async ({ page }) => {\n    at E:\\Augment Code Testing\\sri-lanka-tourist-guide\\tests\\mobile-timeline.spec.ts:141:29"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-06-24T10:33:48.634Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\test-results\\mobile-timeline-Mobile-Tim-86076-overflow-properly-on-mobile-webkit\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\test-results\\mobile-timeline-Mobile-Tim-86076-overflow-properly-on-mobile-webkit\\video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\test-results\\mobile-timeline-Mobile-Tim-86076-overflow-properly-on-mobile-webkit\\error-context.md"}], "errorLocation": {"file": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\tests\\mobile-timeline.spec.ts", "column": 29, "line": 141}}], "status": "unexpected"}], "id": "5a25f56fc10133cc8503-7d2cd2c9d66f2d7819c3", "file": "mobile-timeline.spec.ts", "line": 126, "column": 3}, {"title": "should display era badges responsively", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [{"workerIndex": 20, "parallelIndex": 0, "status": "failed", "duration": 9257, "error": {"message": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoHaveClass\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nLocator: locator('.rounded-full').first()\nExpected pattern: \u001b[32m/text-xs/\u001b[39m\nReceived string:  \u001b[31m\"inline-block px-3 py-1 bg-orange-100 text-orange-700 text-sm rounded-full\"\u001b[39m\nCall log:\n\u001b[2m  - Expect \"toHaveClass\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('.rounded-full').first()\u001b[22m\n\u001b[2m    8 × locator resolved to <span class=\"inline-block px-3 py-1 bg-orange-100 text-orange-700 text-sm rounded-full\">Sacred Tooth Relic</span>\u001b[22m\n\u001b[2m      - unexpected value \"inline-block px-3 py-1 bg-orange-100 text-orange-700 text-sm rounded-full\"\u001b[22m\n", "stack": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoHaveClass\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nLocator: locator('.rounded-full').first()\nExpected pattern: \u001b[32m/text-xs/\u001b[39m\nReceived string:  \u001b[31m\"inline-block px-3 py-1 bg-orange-100 text-orange-700 text-sm rounded-full\"\u001b[39m\nCall log:\n\u001b[2m  - Expect \"toHaveClass\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('.rounded-full').first()\u001b[22m\n\u001b[2m    8 × locator resolved to <span class=\"inline-block px-3 py-1 bg-orange-100 text-orange-700 text-sm rounded-full\">Sacred Tooth Relic</span>\u001b[22m\n\u001b[2m      - unexpected value \"inline-block px-3 py-1 bg-orange-100 text-orange-700 text-sm rounded-full\"\u001b[22m\n\n    at E:\\Augment Code Testing\\sri-lanka-tourist-guide\\tests\\mobile-timeline.spec.ts:152:28", "location": {"file": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\tests\\mobile-timeline.spec.ts", "column": 28, "line": 152}, "snippet": "\u001b[0m \u001b[90m 150 |\u001b[39m     \n \u001b[90m 151 |\u001b[39m     \u001b[90m// Badge should have mobile-appropriate text size\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 152 |\u001b[39m     \u001b[36mawait\u001b[39m expect(eraBadge)\u001b[33m.\u001b[39mtoHaveClass(\u001b[35m/text-xs/\u001b[39m)\n \u001b[90m     |\u001b[39m                            \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 153 |\u001b[39m     \u001b[36mawait\u001b[39m expect(eraBadge)\u001b[33m.\u001b[39mtoHaveClass(\u001b[35m/md:text-sm/\u001b[39m)\n \u001b[90m 154 |\u001b[39m     \n \u001b[90m 155 |\u001b[39m     \u001b[90m// Test desktop\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\tests\\mobile-timeline.spec.ts", "column": 28, "line": 152}, "message": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoHaveClass\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nLocator: locator('.rounded-full').first()\nExpected pattern: \u001b[32m/text-xs/\u001b[39m\nReceived string:  \u001b[31m\"inline-block px-3 py-1 bg-orange-100 text-orange-700 text-sm rounded-full\"\u001b[39m\nCall log:\n\u001b[2m  - Expect \"toHaveClass\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('.rounded-full').first()\u001b[22m\n\u001b[2m    8 × locator resolved to <span class=\"inline-block px-3 py-1 bg-orange-100 text-orange-700 text-sm rounded-full\">Sacred Tooth Relic</span>\u001b[22m\n\u001b[2m      - unexpected value \"inline-block px-3 py-1 bg-orange-100 text-orange-700 text-sm rounded-full\"\u001b[22m\n\n\n  150 |     \n  151 |     // Badge should have mobile-appropriate text size\n> 152 |     await expect(eraBadge).toHaveClass(/text-xs/)\n      |                            ^\n  153 |     await expect(eraBadge).toHaveClass(/md:text-sm/)\n  154 |     \n  155 |     // Test desktop\n    at E:\\Augment Code Testing\\sri-lanka-tourist-guide\\tests\\mobile-timeline.spec.ts:152:28"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-06-24T10:33:54.612Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\test-results\\mobile-timeline-Mobile-Tim-6f717-lay-era-badges-responsively-webkit\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\test-results\\mobile-timeline-Mobile-Tim-6f717-lay-era-badges-responsively-webkit\\video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\test-results\\mobile-timeline-Mobile-Tim-6f717-lay-era-badges-responsively-webkit\\error-context.md"}], "errorLocation": {"file": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\tests\\mobile-timeline.spec.ts", "column": 28, "line": 152}}], "status": "unexpected"}], "id": "5a25f56fc10133cc8503-6202a54e2f710396b268", "file": "mobile-timeline.spec.ts", "line": 144, "column": 3}, {"title": "should handle touch interactions on mobile", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [{"workerIndex": 21, "parallelIndex": 1, "status": "failed", "duration": 2858, "error": {"message": "Error: locator.tap: The page does not support tap. Use hasTouch context option to enable touch support.", "stack": "Error: locator.tap: The page does not support tap. Use hasTouch context option to enable touch support.\n    at E:\\Augment Code Testing\\sri-lanka-tourist-guide\\tests\\mobile-timeline.spec.ts:169:29", "location": {"file": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\tests\\mobile-timeline.spec.ts", "column": 29, "line": 169}, "snippet": "\u001b[0m \u001b[90m 167 |\u001b[39m     \n \u001b[90m 168 |\u001b[39m     \u001b[90m// Touch should not cause layout issues\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 169 |\u001b[39m     \u001b[36mawait\u001b[39m firstTimelineItem\u001b[33m.\u001b[39mtap()\n \u001b[90m     |\u001b[39m                             \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 170 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForTimeout(\u001b[35m500\u001b[39m)\n \u001b[90m 171 |\u001b[39m     \n \u001b[90m 172 |\u001b[39m     \u001b[90m// Item should still be visible and properly positioned\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\tests\\mobile-timeline.spec.ts", "column": 29, "line": 169}, "message": "Error: locator.tap: The page does not support tap. Use hasTouch context option to enable touch support.\n\n  167 |     \n  168 |     // Touch should not cause layout issues\n> 169 |     await firstTimelineItem.tap()\n      |                             ^\n  170 |     await page.waitForTimeout(500)\n  171 |     \n  172 |     // Item should still be visible and properly positioned\n    at E:\\Augment Code Testing\\sri-lanka-tourist-guide\\tests\\mobile-timeline.spec.ts:169:29"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-06-24T10:33:59.196Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\test-results\\mobile-timeline-Mobile-Tim-eeae3-ouch-interactions-on-mobile-webkit\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\test-results\\mobile-timeline-Mobile-Tim-eeae3-ouch-interactions-on-mobile-webkit\\video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\test-results\\mobile-timeline-Mobile-Tim-eeae3-ouch-interactions-on-mobile-webkit\\error-context.md"}], "errorLocation": {"file": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\tests\\mobile-timeline.spec.ts", "column": 29, "line": 169}}], "status": "unexpected"}], "id": "5a25f56fc10133cc8503-6a85d9b8bbf6620f9cc5", "file": "mobile-timeline.spec.ts", "line": 162, "column": 3}, {"title": "should display timeline correctly on mobile viewport", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [{"workerIndex": 22, "parallelIndex": 1, "status": "failed", "duration": 5224, "error": {"message": "Error: expect.toBeVisible: Error: strict mode violation: locator('.absolute.left-6') resolved to 14 elements:\n    1) <div class=\"absolute left-6 md:left-1/2 transform md:-translate-x-1/2 w-1 h-full\">…</div> aka locator('.absolute.left-6').first()\n    2) <div class=\"absolute left-6 md:relative md:left-auto z-10 transform -translate-x-1/2 md:transform-none\">…</div> aka locator('.absolute.left-6.md\\\\:relative').first()\n    3) <div class=\"absolute left-6 md:relative md:left-auto z-10 transform -translate-x-1/2 md:transform-none\">…</div> aka locator('.flex.items-center.flex-row.md\\\\:flex-row-reverse > .absolute.left-6').first()\n    4) <div class=\"absolute left-6 md:relative md:left-auto z-10 transform -translate-x-1/2 md:transform-none\">…</div> aka locator('div:nth-child(3) > .absolute.left-6')\n    5) <div class=\"absolute left-6 md:relative md:left-auto z-10 transform -translate-x-1/2 md:transform-none\">…</div> aka locator('div:nth-child(4) > .absolute.left-6')\n    6) <div class=\"absolute left-6 md:relative md:left-auto z-10 transform -translate-x-1/2 md:transform-none\">…</div> aka locator('div:nth-child(5) > .absolute.left-6')\n    7) <div class=\"absolute left-6 md:relative md:left-auto z-10 transform -translate-x-1/2 md:transform-none\">…</div> aka locator('div:nth-child(6) > .absolute.left-6')\n    8) <div class=\"absolute left-6 md:relative md:left-auto z-10 transform -translate-x-1/2 md:transform-none\">…</div> aka locator('div:nth-child(7) > .absolute.left-6')\n    9) <div class=\"absolute left-6 md:relative md:left-auto z-10 transform -translate-x-1/2 md:transform-none\">…</div> aka locator('div:nth-child(8) > .absolute.left-6')\n    10) <div class=\"absolute left-6 md:relative md:left-auto z-10 transform -translate-x-1/2 md:transform-none\">…</div> aka locator('div:nth-child(9) > .absolute.left-6')\n    ...\n\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('.absolute.left-6')\u001b[22m\n", "stack": "Error: expect.toBeVisible: Error: strict mode violation: locator('.absolute.left-6') resolved to 14 elements:\n    1) <div class=\"absolute left-6 md:left-1/2 transform md:-translate-x-1/2 w-1 h-full\">…</div> aka locator('.absolute.left-6').first()\n    2) <div class=\"absolute left-6 md:relative md:left-auto z-10 transform -translate-x-1/2 md:transform-none\">…</div> aka locator('.absolute.left-6.md\\\\:relative').first()\n    3) <div class=\"absolute left-6 md:relative md:left-auto z-10 transform -translate-x-1/2 md:transform-none\">…</div> aka locator('.flex.items-center.flex-row.md\\\\:flex-row-reverse > .absolute.left-6').first()\n    4) <div class=\"absolute left-6 md:relative md:left-auto z-10 transform -translate-x-1/2 md:transform-none\">…</div> aka locator('div:nth-child(3) > .absolute.left-6')\n    5) <div class=\"absolute left-6 md:relative md:left-auto z-10 transform -translate-x-1/2 md:transform-none\">…</div> aka locator('div:nth-child(4) > .absolute.left-6')\n    6) <div class=\"absolute left-6 md:relative md:left-auto z-10 transform -translate-x-1/2 md:transform-none\">…</div> aka locator('div:nth-child(5) > .absolute.left-6')\n    7) <div class=\"absolute left-6 md:relative md:left-auto z-10 transform -translate-x-1/2 md:transform-none\">…</div> aka locator('div:nth-child(6) > .absolute.left-6')\n    8) <div class=\"absolute left-6 md:relative md:left-auto z-10 transform -translate-x-1/2 md:transform-none\">…</div> aka locator('div:nth-child(7) > .absolute.left-6')\n    9) <div class=\"absolute left-6 md:relative md:left-auto z-10 transform -translate-x-1/2 md:transform-none\">…</div> aka locator('div:nth-child(8) > .absolute.left-6')\n    10) <div class=\"absolute left-6 md:relative md:left-auto z-10 transform -translate-x-1/2 md:transform-none\">…</div> aka locator('div:nth-child(9) > .absolute.left-6')\n    ...\n\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('.absolute.left-6')\u001b[22m\n\n    at E:\\Augment Code Testing\\sri-lanka-tourist-guide\\tests\\mobile-timeline.spec.ts:18:32", "location": {"file": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\tests\\mobile-timeline.spec.ts", "column": 32, "line": 18}, "snippet": "\u001b[0m \u001b[90m 16 |\u001b[39m     \u001b[90m// Check that timeline line is positioned on the left for mobile\u001b[39m\n \u001b[90m 17 |\u001b[39m     \u001b[36mconst\u001b[39m timelineLine \u001b[33m=\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m'.absolute.left-6'\u001b[39m)\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 18 |\u001b[39m     \u001b[36mawait\u001b[39m expect(timelineLine)\u001b[33m.\u001b[39mtoBeVisible()\n \u001b[90m    |\u001b[39m                                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 19 |\u001b[39m     \n \u001b[90m 20 |\u001b[39m     \u001b[90m// Check that timeline items are in single column layout\u001b[39m\n \u001b[90m 21 |\u001b[39m     \u001b[36mconst\u001b[39m timelineItems \u001b[33m=\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m'.flex.items-center.flex-row'\u001b[39m)\u001b[0m"}, "errors": [{"location": {"file": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\tests\\mobile-timeline.spec.ts", "column": 32, "line": 18}, "message": "Error: expect.toBeVisible: Error: strict mode violation: locator('.absolute.left-6') resolved to 14 elements:\n    1) <div class=\"absolute left-6 md:left-1/2 transform md:-translate-x-1/2 w-1 h-full\">…</div> aka locator('.absolute.left-6').first()\n    2) <div class=\"absolute left-6 md:relative md:left-auto z-10 transform -translate-x-1/2 md:transform-none\">…</div> aka locator('.absolute.left-6.md\\\\:relative').first()\n    3) <div class=\"absolute left-6 md:relative md:left-auto z-10 transform -translate-x-1/2 md:transform-none\">…</div> aka locator('.flex.items-center.flex-row.md\\\\:flex-row-reverse > .absolute.left-6').first()\n    4) <div class=\"absolute left-6 md:relative md:left-auto z-10 transform -translate-x-1/2 md:transform-none\">…</div> aka locator('div:nth-child(3) > .absolute.left-6')\n    5) <div class=\"absolute left-6 md:relative md:left-auto z-10 transform -translate-x-1/2 md:transform-none\">…</div> aka locator('div:nth-child(4) > .absolute.left-6')\n    6) <div class=\"absolute left-6 md:relative md:left-auto z-10 transform -translate-x-1/2 md:transform-none\">…</div> aka locator('div:nth-child(5) > .absolute.left-6')\n    7) <div class=\"absolute left-6 md:relative md:left-auto z-10 transform -translate-x-1/2 md:transform-none\">…</div> aka locator('div:nth-child(6) > .absolute.left-6')\n    8) <div class=\"absolute left-6 md:relative md:left-auto z-10 transform -translate-x-1/2 md:transform-none\">…</div> aka locator('div:nth-child(7) > .absolute.left-6')\n    9) <div class=\"absolute left-6 md:relative md:left-auto z-10 transform -translate-x-1/2 md:transform-none\">…</div> aka locator('div:nth-child(8) > .absolute.left-6')\n    10) <div class=\"absolute left-6 md:relative md:left-auto z-10 transform -translate-x-1/2 md:transform-none\">…</div> aka locator('div:nth-child(9) > .absolute.left-6')\n    ...\n\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('.absolute.left-6')\u001b[22m\n\n\n  16 |     // Check that timeline line is positioned on the left for mobile\n  17 |     const timelineLine = page.locator('.absolute.left-6')\n> 18 |     await expect(timelineLine).toBeVisible()\n     |                                ^\n  19 |     \n  20 |     // Check that timeline items are in single column layout\n  21 |     const timelineItems = page.locator('.flex.items-center.flex-row')\n    at E:\\Augment Code Testing\\sri-lanka-tourist-guide\\tests\\mobile-timeline.spec.ts:18:32"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-06-24T10:34:05.860Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\test-results\\mobile-timeline-Mobile-Tim-846c2-orrectly-on-mobile-viewport-Mobile-Chrome\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\test-results\\mobile-timeline-Mobile-Tim-846c2-orrectly-on-mobile-viewport-Mobile-Chrome\\video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\test-results\\mobile-timeline-Mobile-Tim-846c2-orrectly-on-mobile-viewport-Mobile-Chrome\\error-context.md"}], "errorLocation": {"file": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\tests\\mobile-timeline.spec.ts", "column": 32, "line": 18}}], "status": "unexpected"}], "id": "5a25f56fc10133cc8503-518e4df1b5187ef116b1", "file": "mobile-timeline.spec.ts", "line": 9, "column": 3}, {"title": "should display timeline correctly on desktop viewport", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [{"workerIndex": 23, "parallelIndex": 0, "status": "failed", "duration": 9501, "error": {"message": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoHaveClass\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nLocator: locator('.flex.items-center').first()\nExpected pattern: \u001b[32m/md:flex-row/\u001b[39m\nReceived string:  \u001b[31m\"flex justify-between items-center py-4\"\u001b[39m\nCall log:\n\u001b[2m  - Expect \"toHaveClass\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('.flex.items-center').first()\u001b[22m\n\u001b[2m    9 × locator resolved to <div class=\"flex justify-between items-center py-4\">…</div>\u001b[22m\n\u001b[2m      - unexpected value \"flex justify-between items-center py-4\"\u001b[22m\n", "stack": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoHaveClass\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nLocator: locator('.flex.items-center').first()\nExpected pattern: \u001b[32m/md:flex-row/\u001b[39m\nReceived string:  \u001b[31m\"flex justify-between items-center py-4\"\u001b[39m\nCall log:\n\u001b[2m  - Expect \"toHaveClass\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('.flex.items-center').first()\u001b[22m\n\u001b[2m    9 × locator resolved to <div class=\"flex justify-between items-center py-4\">…</div>\u001b[22m\n\u001b[2m      - unexpected value \"flex justify-between items-center py-4\"\u001b[22m\n\n    at E:\\Augment Code Testing\\sri-lanka-tourist-guide\\tests\\mobile-timeline.spec.ts:66:29", "location": {"file": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\tests\\mobile-timeline.spec.ts", "column": 29, "line": 66}, "snippet": "\u001b[0m \u001b[90m 64 |\u001b[39m     \n \u001b[90m 65 |\u001b[39m     \u001b[90m// First item should be flex-row, second should be flex-row-reverse on desktop\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 66 |\u001b[39m     \u001b[36mawait\u001b[39m expect(firstItem)\u001b[33m.\u001b[39mtoHaveClass(\u001b[35m/md:flex-row/\u001b[39m)\n \u001b[90m    |\u001b[39m                             \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 67 |\u001b[39m     \u001b[36mawait\u001b[39m expect(secondItem)\u001b[33m.\u001b[39mtoHaveClass(\u001b[35m/md:flex-row-reverse/\u001b[39m)\n \u001b[90m 68 |\u001b[39m   })\n \u001b[90m 69 |\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\tests\\mobile-timeline.spec.ts", "column": 29, "line": 66}, "message": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoHaveClass\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nLocator: locator('.flex.items-center').first()\nExpected pattern: \u001b[32m/md:flex-row/\u001b[39m\nReceived string:  \u001b[31m\"flex justify-between items-center py-4\"\u001b[39m\nCall log:\n\u001b[2m  - Expect \"toHaveClass\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('.flex.items-center').first()\u001b[22m\n\u001b[2m    9 × locator resolved to <div class=\"flex justify-between items-center py-4\">…</div>\u001b[22m\n\u001b[2m      - unexpected value \"flex justify-between items-center py-4\"\u001b[22m\n\n\n  64 |     \n  65 |     // First item should be flex-row, second should be flex-row-reverse on desktop\n> 66 |     await expect(firstItem).toHaveClass(/md:flex-row/)\n     |                             ^\n  67 |     await expect(secondItem).toHaveClass(/md:flex-row-reverse/)\n  68 |   })\n  69 |\n    at E:\\Augment Code Testing\\sri-lanka-tourist-guide\\tests\\mobile-timeline.spec.ts:66:29"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-06-24T10:34:08.807Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\test-results\\mobile-timeline-Mobile-Tim-ddc63-rrectly-on-desktop-viewport-Mobile-Chrome\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\test-results\\mobile-timeline-Mobile-Tim-ddc63-rrectly-on-desktop-viewport-Mobile-Chrome\\video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\test-results\\mobile-timeline-Mobile-Tim-ddc63-rrectly-on-desktop-viewport-Mobile-Chrome\\error-context.md"}], "errorLocation": {"file": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\tests\\mobile-timeline.spec.ts", "column": 29, "line": 66}}], "status": "unexpected"}], "id": "5a25f56fc10133cc8503-b35ceb5d293641ff3d2c", "file": "mobile-timeline.spec.ts", "line": 40, "column": 3}, {"title": "should have responsive text sizes", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [{"workerIndex": 24, "parallelIndex": 1, "status": "failed", "duration": 9925, "error": {"message": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoHaveClass\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nLocator: locator('h3').first()\nExpected pattern: \u001b[32m/text-lg/\u001b[39m\nReceived string:  \u001b[31m\"text-xl font-semibold mb-3 text-gray-900\"\u001b[39m\nCall log:\n\u001b[2m  - Expect \"toHaveClass\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('h3').first()\u001b[22m\n\u001b[2m    8 × locator resolved to <h3 class=\"text-xl font-semibold mb-3 text-gray-900\">Buddhism & Spirituality</h3>\u001b[22m\n\u001b[2m      - unexpected value \"text-xl font-semibold mb-3 text-gray-900\"\u001b[22m\n", "stack": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoHaveClass\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nLocator: locator('h3').first()\nExpected pattern: \u001b[32m/text-lg/\u001b[39m\nReceived string:  \u001b[31m\"text-xl font-semibold mb-3 text-gray-900\"\u001b[39m\nCall log:\n\u001b[2m  - Expect \"toHaveClass\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('h3').first()\u001b[22m\n\u001b[2m    8 × locator resolved to <h3 class=\"text-xl font-semibold mb-3 text-gray-900\">Buddhism & Spirituality</h3>\u001b[22m\n\u001b[2m      - unexpected value \"text-xl font-semibold mb-3 text-gray-900\"\u001b[22m\n\n    at E:\\Augment Code Testing\\sri-lanka-tourist-guide\\tests\\mobile-timeline.spec.ts:75:25", "location": {"file": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\tests\\mobile-timeline.spec.ts", "column": 25, "line": 75}, "snippet": "\u001b[0m \u001b[90m 73 |\u001b[39m     \n \u001b[90m 74 |\u001b[39m     \u001b[36mconst\u001b[39m title \u001b[33m=\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m'h3'\u001b[39m)\u001b[33m.\u001b[39mfirst()\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 75 |\u001b[39m     \u001b[36mawait\u001b[39m expect(title)\u001b[33m.\u001b[39mtoHaveClass(\u001b[35m/text-lg/\u001b[39m)\n \u001b[90m    |\u001b[39m                         \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 76 |\u001b[39m     \u001b[36mawait\u001b[39m expect(title)\u001b[33m.\u001b[39mtoHaveClass(\u001b[35m/md:text-2xl/\u001b[39m)\n \u001b[90m 77 |\u001b[39m     \n \u001b[90m 78 |\u001b[39m     \u001b[36mconst\u001b[39m description \u001b[33m=\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m'.timeline-content p'\u001b[39m)\u001b[33m.\u001b[39mfirst()\u001b[0m"}, "errors": [{"location": {"file": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\tests\\mobile-timeline.spec.ts", "column": 25, "line": 75}, "message": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoHaveClass\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nLocator: locator('h3').first()\nExpected pattern: \u001b[32m/text-lg/\u001b[39m\nReceived string:  \u001b[31m\"text-xl font-semibold mb-3 text-gray-900\"\u001b[39m\nCall log:\n\u001b[2m  - Expect \"toHaveClass\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('h3').first()\u001b[22m\n\u001b[2m    8 × locator resolved to <h3 class=\"text-xl font-semibold mb-3 text-gray-900\">Buddhism & Spirituality</h3>\u001b[22m\n\u001b[2m      - unexpected value \"text-xl font-semibold mb-3 text-gray-900\"\u001b[22m\n\n\n  73 |     \n  74 |     const title = page.locator('h3').first()\n> 75 |     await expect(title).toHaveClass(/text-lg/)\n     |                         ^\n  76 |     await expect(title).toHaveClass(/md:text-2xl/)\n  77 |     \n  78 |     const description = page.locator('.timeline-content p').first()\n    at E:\\Augment Code Testing\\sri-lanka-tourist-guide\\tests\\mobile-timeline.spec.ts:75:25"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-06-24T10:34:14.092Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\test-results\\mobile-timeline-Mobile-Tim-07475--have-responsive-text-sizes-Mobile-Chrome\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\test-results\\mobile-timeline-Mobile-Tim-07475--have-responsive-text-sizes-Mobile-Chrome\\video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\test-results\\mobile-timeline-Mobile-Tim-07475--have-responsive-text-sizes-Mobile-Chrome\\error-context.md"}], "errorLocation": {"file": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\tests\\mobile-timeline.spec.ts", "column": 25, "line": 75}}], "status": "unexpected"}], "id": "5a25f56fc10133cc8503-e5b559d9b5d24b8047be", "file": "mobile-timeline.spec.ts", "line": 70, "column": 3}, {"title": "should handle scroll-triggered animations on mobile", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [{"workerIndex": 25, "parallelIndex": 0, "status": "passed", "duration": 4122, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-06-24T10:34:20.862Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "5a25f56fc10133cc8503-8bbfbff8850112faccd5", "file": "mobile-timeline.spec.ts", "line": 83, "column": 3}, {"title": "should maintain accessibility on mobile", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [{"workerIndex": 26, "parallelIndex": 1, "status": "failed", "duration": 3489, "error": {"message": "Error: \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBeGreaterThanOrEqual\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nExpected: >= \u001b[32m16\u001b[39m\nReceived:    \u001b[31m14.39999771118164\u001b[39m", "stack": "Error: \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBeGreaterThanOrEqual\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nExpected: >= \u001b[32m16\u001b[39m\nReceived:    \u001b[31m14.39999771118164\u001b[39m\n    at E:\\Augment Code Testing\\sri-lanka-tourist-guide\\tests\\mobile-timeline.spec.ts:121:33", "location": {"file": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\tests\\mobile-timeline.spec.ts", "column": 33, "line": 121}, "snippet": "\u001b[0m \u001b[90m 119 |\u001b[39m     \u001b[36mif\u001b[39m (boundingBox) {\n \u001b[90m 120 |\u001b[39m       \u001b[90m// On mobile, dots should be at least 16px (w-4 h-4)\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 121 |\u001b[39m       expect(boundingBox\u001b[33m.\u001b[39mwidth)\u001b[33m.\u001b[39mtoBeGreaterThanOrEqual(\u001b[35m16\u001b[39m)\n \u001b[90m     |\u001b[39m                                 \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 122 |\u001b[39m       expect(boundingBox\u001b[33m.\u001b[39mheight)\u001b[33m.\u001b[39mtoBeGreaterThanOrEqual(\u001b[35m16\u001b[39m)\n \u001b[90m 123 |\u001b[39m     }\n \u001b[90m 124 |\u001b[39m   })\u001b[0m"}, "errors": [{"location": {"file": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\tests\\mobile-timeline.spec.ts", "column": 33, "line": 121}, "message": "Error: \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBeGreaterThanOrEqual\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nExpected: >= \u001b[32m16\u001b[39m\nReceived:    \u001b[31m14.39999771118164\u001b[39m\n\n  119 |     if (boundingBox) {\n  120 |       // On mobile, dots should be at least 16px (w-4 h-4)\n> 121 |       expect(boundingBox.width).toBeGreaterThanOrEqual(16)\n      |                                 ^\n  122 |       expect(boundingBox.height).toBeGreaterThanOrEqual(16)\n  123 |     }\n  124 |   })\n    at E:\\Augment Code Testing\\sri-lanka-tourist-guide\\tests\\mobile-timeline.spec.ts:121:33"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-06-24T10:34:27.713Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\test-results\\mobile-timeline-Mobile-Tim-6fecf-ain-accessibility-on-mobile-Mobile-Chrome\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\test-results\\mobile-timeline-Mobile-Tim-6fecf-ain-accessibility-on-mobile-Mobile-Chrome\\video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\test-results\\mobile-timeline-Mobile-Tim-6fecf-ain-accessibility-on-mobile-Mobile-Chrome\\error-context.md"}], "errorLocation": {"file": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\tests\\mobile-timeline.spec.ts", "column": 33, "line": 121}}], "status": "unexpected"}], "id": "5a25f56fc10133cc8503-09283023456f10a5e722", "file": "mobile-timeline.spec.ts", "line": 101, "column": 3}, {"title": "should handle content overflow properly on mobile", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [{"workerIndex": 25, "parallelIndex": 0, "status": "failed", "duration": 8295, "error": {"message": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoHaveCSS\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nLocator: locator('.timeline-content p').first()\nExpected string: \u001b[32m\"break-word\"\u001b[39m\nReceived string: \u001b[31m\"normal\"\u001b[39m\nCall log:\n\u001b[2m  - Expect \"toHaveCSS\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('.timeline-content p').first()\u001b[22m\n\u001b[2m    8 × locator resolved to <p class=\"text-sm md:text-base text-gray-600 mb-4 md:mb-6 leading-relaxed\">The earliest human settlements in Sri Lanka, span…</p>\u001b[22m\n\u001b[2m      - unexpected value \"normal\"\u001b[22m\n", "stack": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoHaveCSS\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nLocator: locator('.timeline-content p').first()\nExpected string: \u001b[32m\"break-word\"\u001b[39m\nReceived string: \u001b[31m\"normal\"\u001b[39m\nCall log:\n\u001b[2m  - Expect \"toHaveCSS\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('.timeline-content p').first()\u001b[22m\n\u001b[2m    8 × locator resolved to <p class=\"text-sm md:text-base text-gray-600 mb-4 md:mb-6 leading-relaxed\">The earliest human settlements in Sri Lanka, span…</p>\u001b[22m\n\u001b[2m      - unexpected value \"normal\"\u001b[22m\n\n    at E:\\Augment Code Testing\\sri-lanka-tourist-guide\\tests\\mobile-timeline.spec.ts:141:29", "location": {"file": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\tests\\mobile-timeline.spec.ts", "column": 29, "line": 141}, "snippet": "\u001b[0m \u001b[90m 139 |\u001b[39m     \u001b[36mconst\u001b[39m longTexts \u001b[33m=\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m'.timeline-content p'\u001b[39m)\n \u001b[90m 140 |\u001b[39m     \u001b[36mconst\u001b[39m firstText \u001b[33m=\u001b[39m longTexts\u001b[33m.\u001b[39mfirst()\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 141 |\u001b[39m     \u001b[36mawait\u001b[39m expect(firstText)\u001b[33m.\u001b[39mtoHaveCSS(\u001b[32m'word-wrap'\u001b[39m\u001b[33m,\u001b[39m \u001b[32m'break-word'\u001b[39m)\n \u001b[90m     |\u001b[39m                             \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 142 |\u001b[39m   })\n \u001b[90m 143 |\u001b[39m\n \u001b[90m 144 |\u001b[39m   test(\u001b[32m'should display era badges responsively'\u001b[39m\u001b[33m,\u001b[39m \u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\u001b[0m"}, "errors": [{"location": {"file": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\tests\\mobile-timeline.spec.ts", "column": 29, "line": 141}, "message": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoHaveCSS\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nLocator: locator('.timeline-content p').first()\nExpected string: \u001b[32m\"break-word\"\u001b[39m\nReceived string: \u001b[31m\"normal\"\u001b[39m\nCall log:\n\u001b[2m  - Expect \"toHaveCSS\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('.timeline-content p').first()\u001b[22m\n\u001b[2m    8 × locator resolved to <p class=\"text-sm md:text-base text-gray-600 mb-4 md:mb-6 leading-relaxed\">The earliest human settlements in Sri Lanka, span…</p>\u001b[22m\n\u001b[2m      - unexpected value \"normal\"\u001b[22m\n\n\n  139 |     const longTexts = page.locator('.timeline-content p')\n  140 |     const firstText = longTexts.first()\n> 141 |     await expect(firstText).toHaveCSS('word-wrap', 'break-word')\n      |                             ^\n  142 |   })\n  143 |\n  144 |   test('should display era badges responsively', async ({ page }) => {\n    at E:\\Augment Code Testing\\sri-lanka-tourist-guide\\tests\\mobile-timeline.spec.ts:141:29"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-06-24T10:34:25.284Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\test-results\\mobile-timeline-Mobile-Tim-86076-overflow-properly-on-mobile-Mobile-Chrome\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\test-results\\mobile-timeline-Mobile-Tim-86076-overflow-properly-on-mobile-Mobile-Chrome\\video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\test-results\\mobile-timeline-Mobile-Tim-86076-overflow-properly-on-mobile-Mobile-Chrome\\error-context.md"}], "errorLocation": {"file": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\tests\\mobile-timeline.spec.ts", "column": 29, "line": 141}}], "status": "unexpected"}], "id": "5a25f56fc10133cc8503-7399d6783ad077fcc660", "file": "mobile-timeline.spec.ts", "line": 126, "column": 3}, {"title": "should display era badges responsively", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [{"workerIndex": 27, "parallelIndex": 1, "status": "failed", "duration": 10375, "error": {"message": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoHaveClass\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nLocator: locator('.rounded-full').first()\nExpected pattern: \u001b[32m/text-xs/\u001b[39m\nReceived string:  \u001b[31m\"inline-block px-3 py-1 bg-orange-100 text-orange-700 text-sm rounded-full\"\u001b[39m\nCall log:\n\u001b[2m  - Expect \"toHaveClass\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('.rounded-full').first()\u001b[22m\n\u001b[2m    8 × locator resolved to <span class=\"inline-block px-3 py-1 bg-orange-100 text-orange-700 text-sm rounded-full\">Sacred Tooth Relic</span>\u001b[22m\n\u001b[2m      - unexpected value \"inline-block px-3 py-1 bg-orange-100 text-orange-700 text-sm rounded-full\"\u001b[22m\n", "stack": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoHaveClass\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nLocator: locator('.rounded-full').first()\nExpected pattern: \u001b[32m/text-xs/\u001b[39m\nReceived string:  \u001b[31m\"inline-block px-3 py-1 bg-orange-100 text-orange-700 text-sm rounded-full\"\u001b[39m\nCall log:\n\u001b[2m  - Expect \"toHaveClass\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('.rounded-full').first()\u001b[22m\n\u001b[2m    8 × locator resolved to <span class=\"inline-block px-3 py-1 bg-orange-100 text-orange-700 text-sm rounded-full\">Sacred Tooth Relic</span>\u001b[22m\n\u001b[2m      - unexpected value \"inline-block px-3 py-1 bg-orange-100 text-orange-700 text-sm rounded-full\"\u001b[22m\n\n    at E:\\Augment Code Testing\\sri-lanka-tourist-guide\\tests\\mobile-timeline.spec.ts:152:28", "location": {"file": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\tests\\mobile-timeline.spec.ts", "column": 28, "line": 152}, "snippet": "\u001b[0m \u001b[90m 150 |\u001b[39m     \n \u001b[90m 151 |\u001b[39m     \u001b[90m// Badge should have mobile-appropriate text size\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 152 |\u001b[39m     \u001b[36mawait\u001b[39m expect(eraBadge)\u001b[33m.\u001b[39mtoHaveClass(\u001b[35m/text-xs/\u001b[39m)\n \u001b[90m     |\u001b[39m                            \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 153 |\u001b[39m     \u001b[36mawait\u001b[39m expect(eraBadge)\u001b[33m.\u001b[39mtoHaveClass(\u001b[35m/md:text-sm/\u001b[39m)\n \u001b[90m 154 |\u001b[39m     \n \u001b[90m 155 |\u001b[39m     \u001b[90m// Test desktop\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\tests\\mobile-timeline.spec.ts", "column": 28, "line": 152}, "message": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoHaveClass\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nLocator: locator('.rounded-full').first()\nExpected pattern: \u001b[32m/text-xs/\u001b[39m\nReceived string:  \u001b[31m\"inline-block px-3 py-1 bg-orange-100 text-orange-700 text-sm rounded-full\"\u001b[39m\nCall log:\n\u001b[2m  - Expect \"toHaveClass\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('.rounded-full').first()\u001b[22m\n\u001b[2m    8 × locator resolved to <span class=\"inline-block px-3 py-1 bg-orange-100 text-orange-700 text-sm rounded-full\">Sacred Tooth Relic</span>\u001b[22m\n\u001b[2m      - unexpected value \"inline-block px-3 py-1 bg-orange-100 text-orange-700 text-sm rounded-full\"\u001b[22m\n\n\n  150 |     \n  151 |     // Badge should have mobile-appropriate text size\n> 152 |     await expect(eraBadge).toHaveClass(/text-xs/)\n      |                            ^\n  153 |     await expect(eraBadge).toHaveClass(/md:text-sm/)\n  154 |     \n  155 |     // Test desktop\n    at E:\\Augment Code Testing\\sri-lanka-tourist-guide\\tests\\mobile-timeline.spec.ts:152:28"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-06-24T10:34:33.755Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\test-results\\mobile-timeline-Mobile-Tim-6f717-lay-era-badges-responsively-Mobile-Chrome\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\test-results\\mobile-timeline-Mobile-Tim-6f717-lay-era-badges-responsively-Mobile-Chrome\\video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\test-results\\mobile-timeline-Mobile-Tim-6f717-lay-era-badges-responsively-Mobile-Chrome\\error-context.md"}], "errorLocation": {"file": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\tests\\mobile-timeline.spec.ts", "column": 28, "line": 152}}], "status": "unexpected"}], "id": "5a25f56fc10133cc8503-f7128f215febffe909db", "file": "mobile-timeline.spec.ts", "line": 144, "column": 3}, {"title": "should handle touch interactions on mobile", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [{"workerIndex": 28, "parallelIndex": 0, "status": "passed", "duration": 5340, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-06-24T10:34:39.040Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "5a25f56fc10133cc8503-a2833ef3ae61dca69333", "file": "mobile-timeline.spec.ts", "line": 162, "column": 3}, {"title": "should display timeline correctly on mobile viewport", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [{"workerIndex": 30, "parallelIndex": 0, "status": "failed", "duration": 4442, "error": {"message": "Error: expect.toBeVisible: Error: strict mode violation: locator('.absolute.left-6') resolved to 14 elements:\n    1) <div class=\"absolute left-6 md:left-1/2 transform md:-translate-x-1/2 w-1 h-full\">…</div> aka locator('.absolute.left-6').first()\n    2) <div class=\"absolute left-6 md:relative md:left-auto z-10 transform -translate-x-1/2 md:transform-none\">…</div> aka locator('.absolute.left-6.md\\\\:relative').first()\n    3) <div class=\"absolute left-6 md:relative md:left-auto z-10 transform -translate-x-1/2 md:transform-none\">…</div> aka locator('.flex.items-center.flex-row.md\\\\:flex-row-reverse > .absolute.left-6').first()\n    4) <div class=\"absolute left-6 md:relative md:left-auto z-10 transform -translate-x-1/2 md:transform-none\">…</div> aka locator('div:nth-child(3) > .absolute.left-6')\n    5) <div class=\"absolute left-6 md:relative md:left-auto z-10 transform -translate-x-1/2 md:transform-none\">…</div> aka locator('div:nth-child(4) > .absolute.left-6')\n    6) <div class=\"absolute left-6 md:relative md:left-auto z-10 transform -translate-x-1/2 md:transform-none\">…</div> aka locator('div:nth-child(5) > .absolute.left-6')\n    7) <div class=\"absolute left-6 md:relative md:left-auto z-10 transform -translate-x-1/2 md:transform-none\">…</div> aka locator('div:nth-child(6) > .absolute.left-6')\n    8) <div class=\"absolute left-6 md:relative md:left-auto z-10 transform -translate-x-1/2 md:transform-none\">…</div> aka locator('div:nth-child(7) > .absolute.left-6')\n    9) <div class=\"absolute left-6 md:relative md:left-auto z-10 transform -translate-x-1/2 md:transform-none\">…</div> aka locator('div:nth-child(8) > .absolute.left-6')\n    10) <div class=\"absolute left-6 md:relative md:left-auto z-10 transform -translate-x-1/2 md:transform-none\">…</div> aka locator('div:nth-child(9) > .absolute.left-6')\n    ...\n\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('.absolute.left-6')\u001b[22m\n", "stack": "Error: expect.toBeVisible: Error: strict mode violation: locator('.absolute.left-6') resolved to 14 elements:\n    1) <div class=\"absolute left-6 md:left-1/2 transform md:-translate-x-1/2 w-1 h-full\">…</div> aka locator('.absolute.left-6').first()\n    2) <div class=\"absolute left-6 md:relative md:left-auto z-10 transform -translate-x-1/2 md:transform-none\">…</div> aka locator('.absolute.left-6.md\\\\:relative').first()\n    3) <div class=\"absolute left-6 md:relative md:left-auto z-10 transform -translate-x-1/2 md:transform-none\">…</div> aka locator('.flex.items-center.flex-row.md\\\\:flex-row-reverse > .absolute.left-6').first()\n    4) <div class=\"absolute left-6 md:relative md:left-auto z-10 transform -translate-x-1/2 md:transform-none\">…</div> aka locator('div:nth-child(3) > .absolute.left-6')\n    5) <div class=\"absolute left-6 md:relative md:left-auto z-10 transform -translate-x-1/2 md:transform-none\">…</div> aka locator('div:nth-child(4) > .absolute.left-6')\n    6) <div class=\"absolute left-6 md:relative md:left-auto z-10 transform -translate-x-1/2 md:transform-none\">…</div> aka locator('div:nth-child(5) > .absolute.left-6')\n    7) <div class=\"absolute left-6 md:relative md:left-auto z-10 transform -translate-x-1/2 md:transform-none\">…</div> aka locator('div:nth-child(6) > .absolute.left-6')\n    8) <div class=\"absolute left-6 md:relative md:left-auto z-10 transform -translate-x-1/2 md:transform-none\">…</div> aka locator('div:nth-child(7) > .absolute.left-6')\n    9) <div class=\"absolute left-6 md:relative md:left-auto z-10 transform -translate-x-1/2 md:transform-none\">…</div> aka locator('div:nth-child(8) > .absolute.left-6')\n    10) <div class=\"absolute left-6 md:relative md:left-auto z-10 transform -translate-x-1/2 md:transform-none\">…</div> aka locator('div:nth-child(9) > .absolute.left-6')\n    ...\n\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('.absolute.left-6')\u001b[22m\n\n    at E:\\Augment Code Testing\\sri-lanka-tourist-guide\\tests\\mobile-timeline.spec.ts:18:32", "location": {"file": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\tests\\mobile-timeline.spec.ts", "column": 32, "line": 18}, "snippet": "\u001b[0m \u001b[90m 16 |\u001b[39m     \u001b[90m// Check that timeline line is positioned on the left for mobile\u001b[39m\n \u001b[90m 17 |\u001b[39m     \u001b[36mconst\u001b[39m timelineLine \u001b[33m=\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m'.absolute.left-6'\u001b[39m)\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 18 |\u001b[39m     \u001b[36mawait\u001b[39m expect(timelineLine)\u001b[33m.\u001b[39mtoBeVisible()\n \u001b[90m    |\u001b[39m                                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 19 |\u001b[39m     \n \u001b[90m 20 |\u001b[39m     \u001b[90m// Check that timeline items are in single column layout\u001b[39m\n \u001b[90m 21 |\u001b[39m     \u001b[36mconst\u001b[39m timelineItems \u001b[33m=\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m'.flex.items-center.flex-row'\u001b[39m)\u001b[0m"}, "errors": [{"location": {"file": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\tests\\mobile-timeline.spec.ts", "column": 32, "line": 18}, "message": "Error: expect.toBeVisible: Error: strict mode violation: locator('.absolute.left-6') resolved to 14 elements:\n    1) <div class=\"absolute left-6 md:left-1/2 transform md:-translate-x-1/2 w-1 h-full\">…</div> aka locator('.absolute.left-6').first()\n    2) <div class=\"absolute left-6 md:relative md:left-auto z-10 transform -translate-x-1/2 md:transform-none\">…</div> aka locator('.absolute.left-6.md\\\\:relative').first()\n    3) <div class=\"absolute left-6 md:relative md:left-auto z-10 transform -translate-x-1/2 md:transform-none\">…</div> aka locator('.flex.items-center.flex-row.md\\\\:flex-row-reverse > .absolute.left-6').first()\n    4) <div class=\"absolute left-6 md:relative md:left-auto z-10 transform -translate-x-1/2 md:transform-none\">…</div> aka locator('div:nth-child(3) > .absolute.left-6')\n    5) <div class=\"absolute left-6 md:relative md:left-auto z-10 transform -translate-x-1/2 md:transform-none\">…</div> aka locator('div:nth-child(4) > .absolute.left-6')\n    6) <div class=\"absolute left-6 md:relative md:left-auto z-10 transform -translate-x-1/2 md:transform-none\">…</div> aka locator('div:nth-child(5) > .absolute.left-6')\n    7) <div class=\"absolute left-6 md:relative md:left-auto z-10 transform -translate-x-1/2 md:transform-none\">…</div> aka locator('div:nth-child(6) > .absolute.left-6')\n    8) <div class=\"absolute left-6 md:relative md:left-auto z-10 transform -translate-x-1/2 md:transform-none\">…</div> aka locator('div:nth-child(7) > .absolute.left-6')\n    9) <div class=\"absolute left-6 md:relative md:left-auto z-10 transform -translate-x-1/2 md:transform-none\">…</div> aka locator('div:nth-child(8) > .absolute.left-6')\n    10) <div class=\"absolute left-6 md:relative md:left-auto z-10 transform -translate-x-1/2 md:transform-none\">…</div> aka locator('div:nth-child(9) > .absolute.left-6')\n    ...\n\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('.absolute.left-6')\u001b[22m\n\n\n  16 |     // Check that timeline line is positioned on the left for mobile\n  17 |     const timelineLine = page.locator('.absolute.left-6')\n> 18 |     await expect(timelineLine).toBeVisible()\n     |                                ^\n  19 |     \n  20 |     // Check that timeline items are in single column layout\n  21 |     const timelineItems = page.locator('.flex.items-center.flex-row')\n    at E:\\Augment Code Testing\\sri-lanka-tourist-guide\\tests\\mobile-timeline.spec.ts:18:32"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-06-24T10:34:47.111Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\test-results\\mobile-timeline-Mobile-Tim-846c2-orrectly-on-mobile-viewport-Mobile-Safari\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\test-results\\mobile-timeline-Mobile-Tim-846c2-orrectly-on-mobile-viewport-Mobile-Safari\\video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\test-results\\mobile-timeline-Mobile-Tim-846c2-orrectly-on-mobile-viewport-Mobile-Safari\\error-context.md"}], "errorLocation": {"file": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\tests\\mobile-timeline.spec.ts", "column": 32, "line": 18}}], "status": "unexpected"}], "id": "5a25f56fc10133cc8503-759ee08d3921ebdc79db", "file": "mobile-timeline.spec.ts", "line": 9, "column": 3}, {"title": "should display timeline correctly on desktop viewport", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [{"workerIndex": 29, "parallelIndex": 1, "status": "failed", "duration": 10023, "error": {"message": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoHaveClass\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nLocator: locator('.flex.items-center').first()\nExpected pattern: \u001b[32m/md:flex-row/\u001b[39m\nReceived string:  \u001b[31m\"flex justify-between items-center py-4\"\u001b[39m\nCall log:\n\u001b[2m  - Expect \"toHaveClass\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('.flex.items-center').first()\u001b[22m\n\u001b[2m    9 × locator resolved to <div class=\"flex justify-between items-center py-4\">…</div>\u001b[22m\n\u001b[2m      - unexpected value \"flex justify-between items-center py-4\"\u001b[22m\n", "stack": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoHaveClass\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nLocator: locator('.flex.items-center').first()\nExpected pattern: \u001b[32m/md:flex-row/\u001b[39m\nReceived string:  \u001b[31m\"flex justify-between items-center py-4\"\u001b[39m\nCall log:\n\u001b[2m  - Expect \"toHaveClass\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('.flex.items-center').first()\u001b[22m\n\u001b[2m    9 × locator resolved to <div class=\"flex justify-between items-center py-4\">…</div>\u001b[22m\n\u001b[2m      - unexpected value \"flex justify-between items-center py-4\"\u001b[22m\n\n    at E:\\Augment Code Testing\\sri-lanka-tourist-guide\\tests\\mobile-timeline.spec.ts:66:29", "location": {"file": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\tests\\mobile-timeline.spec.ts", "column": 29, "line": 66}, "snippet": "\u001b[0m \u001b[90m 64 |\u001b[39m     \n \u001b[90m 65 |\u001b[39m     \u001b[90m// First item should be flex-row, second should be flex-row-reverse on desktop\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 66 |\u001b[39m     \u001b[36mawait\u001b[39m expect(firstItem)\u001b[33m.\u001b[39mtoHaveClass(\u001b[35m/md:flex-row/\u001b[39m)\n \u001b[90m    |\u001b[39m                             \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 67 |\u001b[39m     \u001b[36mawait\u001b[39m expect(secondItem)\u001b[33m.\u001b[39mtoHaveClass(\u001b[35m/md:flex-row-reverse/\u001b[39m)\n \u001b[90m 68 |\u001b[39m   })\n \u001b[90m 69 |\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\tests\\mobile-timeline.spec.ts", "column": 29, "line": 66}, "message": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoHaveClass\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nLocator: locator('.flex.items-center').first()\nExpected pattern: \u001b[32m/md:flex-row/\u001b[39m\nReceived string:  \u001b[31m\"flex justify-between items-center py-4\"\u001b[39m\nCall log:\n\u001b[2m  - Expect \"toHaveClass\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('.flex.items-center').first()\u001b[22m\n\u001b[2m    9 × locator resolved to <div class=\"flex justify-between items-center py-4\">…</div>\u001b[22m\n\u001b[2m      - unexpected value \"flex justify-between items-center py-4\"\u001b[22m\n\n\n  64 |     \n  65 |     // First item should be flex-row, second should be flex-row-reverse on desktop\n> 66 |     await expect(firstItem).toHaveClass(/md:flex-row/)\n     |                             ^\n  67 |     await expect(secondItem).toHaveClass(/md:flex-row-reverse/)\n  68 |   })\n  69 |\n    at E:\\Augment Code Testing\\sri-lanka-tourist-guide\\tests\\mobile-timeline.spec.ts:66:29"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-06-24T10:34:47.074Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\test-results\\mobile-timeline-Mobile-Tim-ddc63-rrectly-on-desktop-viewport-Mobile-Safari\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\test-results\\mobile-timeline-Mobile-Tim-ddc63-rrectly-on-desktop-viewport-Mobile-Safari\\video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\test-results\\mobile-timeline-Mobile-Tim-ddc63-rrectly-on-desktop-viewport-Mobile-Safari\\error-context.md"}], "errorLocation": {"file": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\tests\\mobile-timeline.spec.ts", "column": 29, "line": 66}}], "status": "unexpected"}], "id": "5a25f56fc10133cc8503-0dd788602dd343d13f0f", "file": "mobile-timeline.spec.ts", "line": 40, "column": 3}, {"title": "should have responsive text sizes", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [{"workerIndex": 31, "parallelIndex": 0, "status": "failed", "duration": 8860, "error": {"message": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoHaveClass\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nLocator: locator('h3').first()\nExpected pattern: \u001b[32m/text-lg/\u001b[39m\nReceived string:  \u001b[31m\"text-xl font-semibold mb-3 text-gray-900\"\u001b[39m\nCall log:\n\u001b[2m  - Expect \"toHaveClass\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('h3').first()\u001b[22m\n\u001b[2m    8 × locator resolved to <h3 class=\"text-xl font-semibold mb-3 text-gray-900\">Buddhism & Spirituality</h3>\u001b[22m\n\u001b[2m      - unexpected value \"text-xl font-semibold mb-3 text-gray-900\"\u001b[22m\n", "stack": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoHaveClass\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nLocator: locator('h3').first()\nExpected pattern: \u001b[32m/text-lg/\u001b[39m\nReceived string:  \u001b[31m\"text-xl font-semibold mb-3 text-gray-900\"\u001b[39m\nCall log:\n\u001b[2m  - Expect \"toHaveClass\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('h3').first()\u001b[22m\n\u001b[2m    8 × locator resolved to <h3 class=\"text-xl font-semibold mb-3 text-gray-900\">Buddhism & Spirituality</h3>\u001b[22m\n\u001b[2m      - unexpected value \"text-xl font-semibold mb-3 text-gray-900\"\u001b[22m\n\n    at E:\\Augment Code Testing\\sri-lanka-tourist-guide\\tests\\mobile-timeline.spec.ts:75:25", "location": {"file": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\tests\\mobile-timeline.spec.ts", "column": 25, "line": 75}, "snippet": "\u001b[0m \u001b[90m 73 |\u001b[39m     \n \u001b[90m 74 |\u001b[39m     \u001b[36mconst\u001b[39m title \u001b[33m=\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m'h3'\u001b[39m)\u001b[33m.\u001b[39mfirst()\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 75 |\u001b[39m     \u001b[36mawait\u001b[39m expect(title)\u001b[33m.\u001b[39mtoHaveClass(\u001b[35m/text-lg/\u001b[39m)\n \u001b[90m    |\u001b[39m                         \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 76 |\u001b[39m     \u001b[36mawait\u001b[39m expect(title)\u001b[33m.\u001b[39mtoHaveClass(\u001b[35m/md:text-2xl/\u001b[39m)\n \u001b[90m 77 |\u001b[39m     \n \u001b[90m 78 |\u001b[39m     \u001b[36mconst\u001b[39m description \u001b[33m=\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m'.timeline-content p'\u001b[39m)\u001b[33m.\u001b[39mfirst()\u001b[0m"}, "errors": [{"location": {"file": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\tests\\mobile-timeline.spec.ts", "column": 25, "line": 75}, "message": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoHaveClass\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nLocator: locator('h3').first()\nExpected pattern: \u001b[32m/text-lg/\u001b[39m\nReceived string:  \u001b[31m\"text-xl font-semibold mb-3 text-gray-900\"\u001b[39m\nCall log:\n\u001b[2m  - Expect \"toHaveClass\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('h3').first()\u001b[22m\n\u001b[2m    8 × locator resolved to <h3 class=\"text-xl font-semibold mb-3 text-gray-900\">Buddhism & Spirituality</h3>\u001b[22m\n\u001b[2m      - unexpected value \"text-xl font-semibold mb-3 text-gray-900\"\u001b[22m\n\n\n  73 |     \n  74 |     const title = page.locator('h3').first()\n> 75 |     await expect(title).toHaveClass(/text-lg/)\n     |                         ^\n  76 |     await expect(title).toHaveClass(/md:text-2xl/)\n  77 |     \n  78 |     const description = page.locator('.timeline-content p').first()\n    at E:\\Augment Code Testing\\sri-lanka-tourist-guide\\tests\\mobile-timeline.spec.ts:75:25"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-06-24T10:34:52.997Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\test-results\\mobile-timeline-Mobile-Tim-07475--have-responsive-text-sizes-Mobile-Safari\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\test-results\\mobile-timeline-Mobile-Tim-07475--have-responsive-text-sizes-Mobile-Safari\\video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\test-results\\mobile-timeline-Mobile-Tim-07475--have-responsive-text-sizes-Mobile-Safari\\error-context.md"}], "errorLocation": {"file": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\tests\\mobile-timeline.spec.ts", "column": 25, "line": 75}}], "status": "unexpected"}], "id": "5a25f56fc10133cc8503-6e5d7100a2f2f753439f", "file": "mobile-timeline.spec.ts", "line": 70, "column": 3}, {"title": "should handle scroll-triggered animations on mobile", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [{"workerIndex": 32, "parallelIndex": 1, "status": "passed", "duration": 7916, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-06-24T10:34:59.129Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "5a25f56fc10133cc8503-2dc0154bfc76c46e8d85", "file": "mobile-timeline.spec.ts", "line": 83, "column": 3}, {"title": "should maintain accessibility on mobile", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [{"workerIndex": 33, "parallelIndex": 0, "status": "failed", "duration": 5978, "error": {"message": "Error: \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBeGreaterThanOrEqual\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nExpected: >= \u001b[32m16\u001b[39m\nReceived:    \u001b[31m14.400001525878906\u001b[39m", "stack": "Error: \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBeGreaterThanOrEqual\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nExpected: >= \u001b[32m16\u001b[39m\nReceived:    \u001b[31m14.400001525878906\u001b[39m\n    at E:\\Augment Code Testing\\sri-lanka-tourist-guide\\tests\\mobile-timeline.spec.ts:121:33", "location": {"file": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\tests\\mobile-timeline.spec.ts", "column": 33, "line": 121}, "snippet": "\u001b[0m \u001b[90m 119 |\u001b[39m     \u001b[36mif\u001b[39m (boundingBox) {\n \u001b[90m 120 |\u001b[39m       \u001b[90m// On mobile, dots should be at least 16px (w-4 h-4)\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 121 |\u001b[39m       expect(boundingBox\u001b[33m.\u001b[39mwidth)\u001b[33m.\u001b[39mtoBeGreaterThanOrEqual(\u001b[35m16\u001b[39m)\n \u001b[90m     |\u001b[39m                                 \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 122 |\u001b[39m       expect(boundingBox\u001b[33m.\u001b[39mheight)\u001b[33m.\u001b[39mtoBeGreaterThanOrEqual(\u001b[35m16\u001b[39m)\n \u001b[90m 123 |\u001b[39m     }\n \u001b[90m 124 |\u001b[39m   })\u001b[0m"}, "errors": [{"location": {"file": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\tests\\mobile-timeline.spec.ts", "column": 33, "line": 121}, "message": "Error: \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBeGreaterThanOrEqual\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nExpected: >= \u001b[32m16\u001b[39m\nReceived:    \u001b[31m14.400001525878906\u001b[39m\n\n  119 |     if (boundingBox) {\n  120 |       // On mobile, dots should be at least 16px (w-4 h-4)\n> 121 |       expect(boundingBox.width).toBeGreaterThanOrEqual(16)\n      |                                 ^\n  122 |       expect(boundingBox.height).toBeGreaterThanOrEqual(16)\n  123 |     }\n  124 |   })\n    at E:\\Augment Code Testing\\sri-lanka-tourist-guide\\tests\\mobile-timeline.spec.ts:121:33"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-06-24T10:35:08.557Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\test-results\\mobile-timeline-Mobile-Tim-6fecf-ain-accessibility-on-mobile-Mobile-Safari\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\test-results\\mobile-timeline-Mobile-Tim-6fecf-ain-accessibility-on-mobile-Mobile-Safari\\video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\test-results\\mobile-timeline-Mobile-Tim-6fecf-ain-accessibility-on-mobile-Mobile-Safari\\error-context.md"}], "errorLocation": {"file": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\tests\\mobile-timeline.spec.ts", "column": 33, "line": 121}}], "status": "unexpected"}], "id": "5a25f56fc10133cc8503-b1e563350384247ed720", "file": "mobile-timeline.spec.ts", "line": 101, "column": 3}, {"title": "should handle content overflow properly on mobile", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [{"workerIndex": 32, "parallelIndex": 1, "status": "failed", "duration": 11664, "error": {"message": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoHaveCSS\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nLocator: locator('.timeline-content p').first()\nExpected string: \u001b[32m\"break-word\"\u001b[39m\nReceived string: \u001b[31m\"normal\"\u001b[39m\nCall log:\n\u001b[2m  - Expect \"toHaveCSS\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('.timeline-content p').first()\u001b[22m\n\u001b[2m    8 × locator resolved to <p class=\"text-sm md:text-base text-gray-600 mb-4 md:mb-6 leading-relaxed\">The earliest human settlements in Sri Lanka, span…</p>\u001b[22m\n\u001b[2m      - unexpected value \"normal\"\u001b[22m\n", "stack": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoHaveCSS\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nLocator: locator('.timeline-content p').first()\nExpected string: \u001b[32m\"break-word\"\u001b[39m\nReceived string: \u001b[31m\"normal\"\u001b[39m\nCall log:\n\u001b[2m  - Expect \"toHaveCSS\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('.timeline-content p').first()\u001b[22m\n\u001b[2m    8 × locator resolved to <p class=\"text-sm md:text-base text-gray-600 mb-4 md:mb-6 leading-relaxed\">The earliest human settlements in Sri Lanka, span…</p>\u001b[22m\n\u001b[2m      - unexpected value \"normal\"\u001b[22m\n\n    at E:\\Augment Code Testing\\sri-lanka-tourist-guide\\tests\\mobile-timeline.spec.ts:141:29", "location": {"file": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\tests\\mobile-timeline.spec.ts", "column": 29, "line": 141}, "snippet": "\u001b[0m \u001b[90m 139 |\u001b[39m     \u001b[36mconst\u001b[39m longTexts \u001b[33m=\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m'.timeline-content p'\u001b[39m)\n \u001b[90m 140 |\u001b[39m     \u001b[36mconst\u001b[39m firstText \u001b[33m=\u001b[39m longTexts\u001b[33m.\u001b[39mfirst()\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 141 |\u001b[39m     \u001b[36mawait\u001b[39m expect(firstText)\u001b[33m.\u001b[39mtoHaveCSS(\u001b[32m'word-wrap'\u001b[39m\u001b[33m,\u001b[39m \u001b[32m'break-word'\u001b[39m)\n \u001b[90m     |\u001b[39m                             \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 142 |\u001b[39m   })\n \u001b[90m 143 |\u001b[39m\n \u001b[90m 144 |\u001b[39m   test(\u001b[32m'should display era badges responsively'\u001b[39m\u001b[33m,\u001b[39m \u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\u001b[0m"}, "errors": [{"location": {"file": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\tests\\mobile-timeline.spec.ts", "column": 29, "line": 141}, "message": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoHaveCSS\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nLocator: locator('.timeline-content p').first()\nExpected string: \u001b[32m\"break-word\"\u001b[39m\nReceived string: \u001b[31m\"normal\"\u001b[39m\nCall log:\n\u001b[2m  - Expect \"toHaveCSS\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('.timeline-content p').first()\u001b[22m\n\u001b[2m    8 × locator resolved to <p class=\"text-sm md:text-base text-gray-600 mb-4 md:mb-6 leading-relaxed\">The earliest human settlements in Sri Lanka, span…</p>\u001b[22m\n\u001b[2m      - unexpected value \"normal\"\u001b[22m\n\n\n  139 |     const longTexts = page.locator('.timeline-content p')\n  140 |     const firstText = longTexts.first()\n> 141 |     await expect(firstText).toHaveCSS('word-wrap', 'break-word')\n      |                             ^\n  142 |   })\n  143 |\n  144 |   test('should display era badges responsively', async ({ page }) => {\n    at E:\\Augment Code Testing\\sri-lanka-tourist-guide\\tests\\mobile-timeline.spec.ts:141:29"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-06-24T10:35:07.401Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\test-results\\mobile-timeline-Mobile-Tim-86076-overflow-properly-on-mobile-Mobile-Safari\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\test-results\\mobile-timeline-Mobile-Tim-86076-overflow-properly-on-mobile-Mobile-Safari\\video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\test-results\\mobile-timeline-Mobile-Tim-86076-overflow-properly-on-mobile-Mobile-Safari\\error-context.md"}], "errorLocation": {"file": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\tests\\mobile-timeline.spec.ts", "column": 29, "line": 141}}], "status": "unexpected"}], "id": "5a25f56fc10133cc8503-894043ccfc804f39879d", "file": "mobile-timeline.spec.ts", "line": 126, "column": 3}, {"title": "should display era badges responsively", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [{"workerIndex": 34, "parallelIndex": 0, "status": "failed", "duration": 7448, "error": {"message": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoHaveClass\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nLocator: locator('.rounded-full').first()\nExpected pattern: \u001b[32m/text-xs/\u001b[39m\nReceived string:  \u001b[31m\"inline-block px-3 py-1 bg-orange-100 text-orange-700 text-sm rounded-full\"\u001b[39m\nCall log:\n\u001b[2m  - Expect \"toHaveClass\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('.rounded-full').first()\u001b[22m\n\u001b[2m    8 × locator resolved to <span class=\"inline-block px-3 py-1 bg-orange-100 text-orange-700 text-sm rounded-full\">Sacred Tooth Relic</span>\u001b[22m\n\u001b[2m      - unexpected value \"inline-block px-3 py-1 bg-orange-100 text-orange-700 text-sm rounded-full\"\u001b[22m\n", "stack": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoHaveClass\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nLocator: locator('.rounded-full').first()\nExpected pattern: \u001b[32m/text-xs/\u001b[39m\nReceived string:  \u001b[31m\"inline-block px-3 py-1 bg-orange-100 text-orange-700 text-sm rounded-full\"\u001b[39m\nCall log:\n\u001b[2m  - Expect \"toHaveClass\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('.rounded-full').first()\u001b[22m\n\u001b[2m    8 × locator resolved to <span class=\"inline-block px-3 py-1 bg-orange-100 text-orange-700 text-sm rounded-full\">Sacred Tooth Relic</span>\u001b[22m\n\u001b[2m      - unexpected value \"inline-block px-3 py-1 bg-orange-100 text-orange-700 text-sm rounded-full\"\u001b[22m\n\n    at E:\\Augment Code Testing\\sri-lanka-tourist-guide\\tests\\mobile-timeline.spec.ts:152:28", "location": {"file": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\tests\\mobile-timeline.spec.ts", "column": 28, "line": 152}, "snippet": "\u001b[0m \u001b[90m 150 |\u001b[39m     \n \u001b[90m 151 |\u001b[39m     \u001b[90m// Badge should have mobile-appropriate text size\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 152 |\u001b[39m     \u001b[36mawait\u001b[39m expect(eraBadge)\u001b[33m.\u001b[39mtoHaveClass(\u001b[35m/text-xs/\u001b[39m)\n \u001b[90m     |\u001b[39m                            \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 153 |\u001b[39m     \u001b[36mawait\u001b[39m expect(eraBadge)\u001b[33m.\u001b[39mtoHaveClass(\u001b[35m/md:text-sm/\u001b[39m)\n \u001b[90m 154 |\u001b[39m     \n \u001b[90m 155 |\u001b[39m     \u001b[90m// Test desktop\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\tests\\mobile-timeline.spec.ts", "column": 28, "line": 152}, "message": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoHaveClass\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nLocator: locator('.rounded-full').first()\nExpected pattern: \u001b[32m/text-xs/\u001b[39m\nReceived string:  \u001b[31m\"inline-block px-3 py-1 bg-orange-100 text-orange-700 text-sm rounded-full\"\u001b[39m\nCall log:\n\u001b[2m  - Expect \"toHaveClass\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('.rounded-full').first()\u001b[22m\n\u001b[2m    8 × locator resolved to <span class=\"inline-block px-3 py-1 bg-orange-100 text-orange-700 text-sm rounded-full\">Sacred Tooth Relic</span>\u001b[22m\n\u001b[2m      - unexpected value \"inline-block px-3 py-1 bg-orange-100 text-orange-700 text-sm rounded-full\"\u001b[22m\n\n\n  150 |     \n  151 |     // Badge should have mobile-appropriate text size\n> 152 |     await expect(eraBadge).toHaveClass(/text-xs/)\n      |                            ^\n  153 |     await expect(eraBadge).toHaveClass(/md:text-sm/)\n  154 |     \n  155 |     // Test desktop\n    at E:\\Augment Code Testing\\sri-lanka-tourist-guide\\tests\\mobile-timeline.spec.ts:152:28"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-06-24T10:35:16.909Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\test-results\\mobile-timeline-Mobile-Tim-6f717-lay-era-badges-responsively-Mobile-Safari\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\test-results\\mobile-timeline-Mobile-Tim-6f717-lay-era-badges-responsively-Mobile-Safari\\video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\test-results\\mobile-timeline-Mobile-Tim-6f717-lay-era-badges-responsively-Mobile-Safari\\error-context.md"}], "errorLocation": {"file": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\tests\\mobile-timeline.spec.ts", "column": 28, "line": 152}}], "status": "unexpected"}], "id": "5a25f56fc10133cc8503-85fc6c22e10e6a47f009", "file": "mobile-timeline.spec.ts", "line": 144, "column": 3}, {"title": "should handle touch interactions on mobile", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [{"workerIndex": 35, "parallelIndex": 1, "status": "passed", "duration": 4798, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-06-24T10:35:21.825Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "5a25f56fc10133cc8503-1b7e37f40bad98343489", "file": "mobile-timeline.spec.ts", "line": 162, "column": 3}]}]}], "errors": [], "stats": {"startTime": "2025-06-24T10:31:00.876Z", "duration": 266068.153, "expected": 6, "skipped": 0, "unexpected": 34, "flaky": 0}}