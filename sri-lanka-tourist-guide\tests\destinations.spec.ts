import { test, expect } from '@playwright/test';

test.describe('Destinations Page Tests', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/destinations');
  });

  test('should display destinations page correctly', async ({ page }) => {
    await expect(page).toHaveTitle(/Destinations/);
    await expect(page.locator('h1')).toContainText('Destinations');

    // Check for search and filter functionality (main page search)
    await expect(page.locator('section').first().locator('input[placeholder*="Search destinations"]')).toBeVisible();
    await expect(page.locator('select').first()).toBeVisible(); // Category filter
    await expect(page.locator('select').nth(1)).toBeVisible(); // Province filter
  });

  test('should display interactive map', async ({ page }) => {
    // Wait for map to load
    await page.waitForSelector('.leaflet-container', { timeout: 10000 });
    
    // Check if map container is visible
    await expect(page.locator('.leaflet-container')).toBeVisible();
    
    // Check for map controls
    await expect(page.locator('text=Explore Sri Lanka on the Map')).toBeVisible();
    
    // Check for map search functionality (map section search)
    await expect(page.locator('input[placeholder*="Search destinations on map"]')).toBeVisible();
  });

  test('should display destination cards', async ({ page }) => {
    // Wait for destination cards to load
    await page.waitForSelector('[data-testid="destination-card"], .card', { timeout: 10000 });
    
    // Check if destination cards are displayed
    const destinationCards = page.locator('[data-testid="destination-card"], .card');
    await expect(destinationCards.first()).toBeVisible();
    
    // Check destination card content
    await expect(destinationCards.first()).toContainText('Sigiriya');
    await expect(destinationCards.first().locator('text=Explore Destination')).toBeVisible();
  });

  test('should filter destinations by category', async ({ page }) => {
    // Wait for destinations to load
    await page.waitForSelector('.card', { timeout: 10000 });
    
    // Select cultural category
    await page.selectOption('select >> nth=0', 'cultural');
    
    // Wait for filtering
    await page.waitForTimeout(1000);
    
    // Check that cultural destinations are shown
    const cards = page.locator('.card');
    const count = await cards.count();
    expect(count).toBeGreaterThan(0);
    
    // Verify cultural destinations are displayed
    await expect(page.locator('option[value="cultural"]')).toBeVisible();
  });

  test('should filter destinations by province', async ({ page }) => {
    // Wait for destinations to load
    await page.waitForSelector('.card', { timeout: 10000 });
    
    // Select Central province
    await page.selectOption('select >> nth=1', 'Central');
    
    // Wait for filtering
    await page.waitForTimeout(1000);
    
    // Check that Central province destinations are shown
    const cards = page.locator('.card');
    const count = await cards.count();
    expect(count).toBeGreaterThan(0);
  });

  test('should search destinations', async ({ page }) => {
    // Wait for destinations to load
    await page.waitForSelector('.card', { timeout: 10000 });

    // Search for Sigiriya (use the main page search)
    await page.fill('section input[placeholder*="Search destinations"]', 'Sigiriya');
    
    // Wait for search results
    await page.waitForTimeout(1000);
    
    // Check search results
    const cards = page.locator('.card');
    const count = await cards.count();
    expect(count).toBeGreaterThan(0);
    
    // Verify Sigiriya is shown
    await expect(page.locator('text=Sigiriya')).toBeVisible();
  });

  test('should navigate to destination detail page', async ({ page }) => {
    // Wait for destinations to load
    await page.waitForSelector('.card', { timeout: 10000 });
    
    // Click on first "Explore Destination" button
    await page.click('text=Explore Destination >> nth=0');
    
    // Should navigate to destination detail page
    await expect(page).toHaveURL(/\/destinations\/.+/);
    
    // Should display destination content
    await expect(page.locator('h1')).toBeVisible();
  });

  test('should display results count', async ({ page }) => {
    // Wait for destinations to load
    await page.waitForSelector('.card', { timeout: 10000 });
    
    // Check for results count display
    await expect(page.locator('text=/\\d+ destinations? found/')).toBeVisible();
  });
});

test.describe('Interactive Map Tests', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/destinations');
  });

  test('should display map with markers', async ({ page }) => {
    // Wait for map to fully load
    await page.waitForSelector('.leaflet-container', { timeout: 15000 });
    await page.waitForTimeout(2000); // Additional wait for markers to load
    
    // Check if markers are present
    const markers = page.locator('.leaflet-marker-icon');
    const markerCount = await markers.count();
    expect(markerCount).toBeGreaterThan(0);
  });

  test('should have map controls', async ({ page }) => {
    // Wait for map to load
    await page.waitForSelector('.leaflet-container', { timeout: 10000 });
    
    // Check for map search
    const mapSearch = page.locator('.leaflet-container').locator('..').locator('input[placeholder*="Search destinations"]');
    await expect(mapSearch).toBeVisible();
    
    // Check for filter button
    const filterButton = page.locator('button:has-text("Filters")');
    await expect(filterButton).toBeVisible();
  });

  test('should open filter panel', async ({ page }) => {
    // Wait for map to load
    await page.waitForSelector('.leaflet-container', { timeout: 10000 });
    
    // Click filter button
    await page.click('button:has-text("Filters")');
    
    // Check if filter panel opens
    await expect(page.locator('text=Filter by Category')).toBeVisible();
    
    // Check category options
    await expect(page.locator('text=All Destinations')).toBeVisible();
    await expect(page.locator('text=Cultural Sites')).toBeVisible();
    await expect(page.locator('text=Nature & Wildlife')).toBeVisible();
  });

  test('should filter map markers by category', async ({ page }) => {
    // Wait for map to load
    await page.waitForSelector('.leaflet-container', { timeout: 10000 });
    await page.waitForTimeout(2000);
    
    // Open filter panel
    await page.click('button:has-text("Filters")');
    
    // Select cultural sites
    await page.click('text=Cultural Sites');
    
    // Wait for filtering
    await page.waitForTimeout(1000);
    
    // Check results counter
    const counter = page.locator('text=/Showing \\d+ of \\d+ destinations/');
    await expect(counter).toBeVisible();
  });

  test('should display marker popups', async ({ page }) => {
    // Wait for map to load
    await page.waitForSelector('.leaflet-container', { timeout: 15000 });
    await page.waitForTimeout(2000);
    
    // Click on first marker
    const firstMarker = page.locator('.leaflet-marker-icon').first();
    await firstMarker.click();
    
    // Wait for popup to appear
    await page.waitForTimeout(1000);
    
    // Check if popup is displayed
    const popup = page.locator('.leaflet-popup');
    await expect(popup).toBeVisible();
    
    // Check popup content
    await expect(popup.locator('h3')).toBeVisible();
    await expect(popup.locator('button:has-text("View Details")')).toBeVisible();
  });
});
