import { test, expect } from '@playwright/test'

test.describe('Input Labels Debug', () => {
  test('Identify inputs without labels on all pages', async ({ page }) => {
    const pages = ['/', '/destinations', '/activities', '/culture', '/articles', '/travel-tips', '/contact']
    
    for (const url of pages) {
      await page.goto(url)
      await page.waitForLoadState('networkidle')
      
      const inputsWithoutLabels = await page.evaluate(() => {
        const inputs = Array.from(document.querySelectorAll('input, select, textarea'))
        const problematicInputs = inputs.filter(input => {
          const id = input.id
          const ariaLabel = input.getAttribute('aria-label')
          const ariaLabelledBy = input.getAttribute('aria-labelledby')
          const placeholder = input.getAttribute('placeholder')
          
          // Check if there's a label element for this input
          const label = id ? document.querySelector(`label[for="${id}"]`) : null
          
          // Input is accessible if it has any of these:
          // 1. Associated label element
          // 2. aria-label attribute
          // 3. aria-labelledby attribute
          const hasLabel = label !== null
          const hasAriaLabel = ariaLabel && ariaLabel.trim() !== ''
          const hasAriaLabelledBy = ariaLabelledBy && ariaLabelledBy.trim() !== ''
          
          const isAccessible = hasLabel || hasAriaLabel || hasAriaLabelledBy
          
          return !isAccessible
        })
        
        return problematicInputs.map(input => ({
          tagName: input.tagName,
          type: input.type || 'N/A',
          id: input.id,
          name: input.name || 'N/A',
          placeholder: input.getAttribute('placeholder'),
          ariaLabel: input.getAttribute('aria-label'),
          ariaLabelledBy: input.getAttribute('aria-labelledby'),
          className: input.className,
          outerHTML: input.outerHTML.slice(0, 200),
          parentClassName: input.parentElement?.className,
          parentTagName: input.parentElement?.tagName
        }))
      })
      
      console.log(`${url} - Inputs without labels:`, inputsWithoutLabels.length)
      if (inputsWithoutLabels.length > 0) {
        console.log(`${url} - Problematic inputs:`, inputsWithoutLabels)
      }
    }
  })

  test('Check specific pages with known issues', async ({ page }) => {
    const problematicPages = ['/', '/activities', '/articles']
    
    for (const url of problematicPages) {
      await page.goto(url)
      await page.waitForLoadState('networkidle')
      
      // Take screenshot for visual inspection
      await page.screenshot({
        path: `test-results/input-labels-${url.replace('/', 'home')}.png`,
        fullPage: true
      })
      
      const inputsWithoutLabels = await page.evaluate(() => {
        const inputs = Array.from(document.querySelectorAll('input, select, textarea'))
        return inputs.filter(input => {
          const id = input.id
          const ariaLabel = input.getAttribute('aria-label')
          const ariaLabelledBy = input.getAttribute('aria-labelledby')
          
          const label = id ? document.querySelector(`label[for="${id}"]`) : null
          const hasLabel = label !== null
          const hasAriaLabel = ariaLabel && ariaLabel.trim() !== ''
          const hasAriaLabelledBy = ariaLabelledBy && ariaLabelledBy.trim() !== ''
          
          return !(hasLabel || hasAriaLabel || hasAriaLabelledBy)
        }).map(input => ({
          tagName: input.tagName,
          type: input.type || 'N/A',
          id: input.id,
          placeholder: input.getAttribute('placeholder'),
          className: input.className,
          outerHTML: input.outerHTML
        }))
      })
      
      console.log(`\n=== ${url} ===`)
      console.log('Inputs without proper labels:', inputsWithoutLabels.length)
      inputsWithoutLabels.forEach((input, index) => {
        console.log(`Input ${index + 1}:`, {
          tag: input.tagName,
          type: input.type,
          id: input.id,
          placeholder: input.placeholder,
          className: input.className
        })
      })
    }
  })
})
