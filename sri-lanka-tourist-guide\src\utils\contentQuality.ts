// Content Quality Assurance System for Sri Lanka Tourist Guide

export interface QualityMetrics {
  wordCount: number
  readingTime: number
  headingStructure: HeadingAnalysis
  seoScore: number
  readabilityScore: number
  testimonialCount: number
  imageCount: number
  linkCount: number
  hasTableOfContents: boolean
  hasConclusion: boolean
  hasIntroduction: boolean
  qualityGrade: 'A' | 'B' | 'C' | 'D' | 'F'
}

export interface HeadingAnalysis {
  h1Count: number
  h2Count: number
  h3Count: number
  totalHeadings: number
  hasProperHierarchy: boolean
  averageWordsPerSection: number
}

export interface ContentIssue {
  type: 'error' | 'warning' | 'suggestion'
  category: 'structure' | 'seo' | 'readability' | 'content' | 'formatting'
  message: string
  line?: number
  severity: 'high' | 'medium' | 'low'
}

export class ContentQualityAnalyzer {
  private content: string
  private title: string
  
  constructor(content: string, title: string) {
    this.content = content
    this.title = title
  }

  // Main quality analysis method
  analyzeQuality(): { metrics: QualityMetrics; issues: ContentIssue[] } {
    const metrics = this.calculateMetrics()
    const issues = this.findIssues()
    
    return { metrics, issues }
  }

  // Calculate comprehensive quality metrics
  private calculateMetrics(): QualityMetrics {
    const wordCount = this.getWordCount()
    const readingTime = Math.ceil(wordCount / 250) // Average reading speed
    const headingStructure = this.analyzeHeadingStructure()
    const seoScore = this.calculateSEOScore()
    const readabilityScore = this.calculateReadabilityScore()
    const testimonialCount = this.countTestimonials()
    const imageCount = this.countImages()
    const linkCount = this.countLinks()
    const hasTableOfContents = this.hasTableOfContents()
    const hasConclusion = this.hasConclusion()
    const hasIntroduction = this.hasIntroduction()
    const qualityGrade = this.calculateOverallGrade({
      wordCount,
      headingStructure,
      seoScore,
      readabilityScore,
      testimonialCount,
      hasTableOfContents,
      hasConclusion,
      hasIntroduction
    })

    return {
      wordCount,
      readingTime,
      headingStructure,
      seoScore,
      readabilityScore,
      testimonialCount,
      imageCount,
      linkCount,
      hasTableOfContents,
      hasConclusion,
      hasIntroduction,
      qualityGrade
    }
  }

  // Find content issues and suggestions
  private findIssues(): ContentIssue[] {
    const issues: ContentIssue[] = []
    
    // Word count validation
    const wordCount = this.getWordCount()
    if (wordCount < 2000) {
      issues.push({
        type: 'error',
        category: 'content',
        message: `Article is too short (${wordCount} words). Target: 2000-3000 words.`,
        severity: 'high'
      })
    } else if (wordCount > 3500) {
      issues.push({
        type: 'warning',
        category: 'content',
        message: `Article is very long (${wordCount} words). Consider breaking into sections.`,
        severity: 'medium'
      })
    }

    // Heading structure validation
    const headings = this.analyzeHeadingStructure()
    if (headings.h1Count !== 1) {
      issues.push({
        type: 'error',
        category: 'structure',
        message: `Should have exactly 1 H1 heading, found ${headings.h1Count}`,
        severity: 'high'
      })
    }

    if (!headings.hasProperHierarchy) {
      issues.push({
        type: 'error',
        category: 'structure',
        message: 'Heading hierarchy is not proper (skipping levels)',
        severity: 'high'
      })
    }

    // SEO validation
    if (!this.hasMetaDescription()) {
      issues.push({
        type: 'error',
        category: 'seo',
        message: 'Missing meta description or excerpt',
        severity: 'high'
      })
    }

    // Content structure validation
    if (!this.hasIntroduction()) {
      issues.push({
        type: 'error',
        category: 'structure',
        message: 'Missing proper introduction section',
        severity: 'high'
      })
    }

    if (!this.hasConclusion()) {
      issues.push({
        type: 'warning',
        category: 'structure',
        message: 'Missing conclusion section',
        severity: 'medium'
      })
    }

    // Testimonial validation
    const testimonialCount = this.countTestimonials()
    if (testimonialCount === 0) {
      issues.push({
        type: 'warning',
        category: 'content',
        message: 'No testimonials found. Consider adding authentic tourist experiences.',
        severity: 'medium'
      })
    }

    // Image validation
    const imageCount = this.countImages()
    if (imageCount === 0) {
      issues.push({
        type: 'warning',
        category: 'content',
        message: 'No images found. Visual content improves engagement.',
        severity: 'medium'
      })
    }

    return issues
  }

  // Calculate word count
  private getWordCount(): number {
    return this.content
      .replace(/[#*`\[\]()]/g, '') // Remove markdown formatting
      .split(/\s+/)
      .filter(word => word.length > 0).length
  }

  // Analyze heading structure
  private analyzeHeadingStructure(): HeadingAnalysis {
    const headingRegex = /^(#{1,6})\s+(.+)$/gm
    const headings: { level: number; text: string }[] = []
    let match

    while ((match = headingRegex.exec(this.content)) !== null) {
      headings.push({
        level: match[1].length,
        text: match[2].trim()
      })
    }

    const h1Count = headings.filter(h => h.level === 1).length
    const h2Count = headings.filter(h => h.level === 2).length
    const h3Count = headings.filter(h => h.level === 3).length
    const totalHeadings = headings.length

    // Check hierarchy
    let hasProperHierarchy = true
    for (let i = 1; i < headings.length; i++) {
      const currentLevel = headings[i].level
      const previousLevel = headings[i - 1].level
      if (currentLevel > previousLevel + 1) {
        hasProperHierarchy = false
        break
      }
    }

    // Calculate average words per section
    const sections = this.content.split(/^#{2,6}\s+/gm)
    const averageWordsPerSection = sections.length > 1 
      ? Math.round(this.getWordCount() / sections.length)
      : this.getWordCount()

    return {
      h1Count,
      h2Count,
      h3Count,
      totalHeadings,
      hasProperHierarchy,
      averageWordsPerSection
    }
  }

  // Calculate SEO score
  private calculateSEOScore(): number {
    let score = 0
    const maxScore = 100

    // Title optimization (20 points)
    if (this.title.length >= 30 && this.title.length <= 60) score += 20
    else if (this.title.length >= 20 && this.title.length <= 70) score += 15
    else score += 5

    // Heading structure (20 points)
    const headings = this.analyzeHeadingStructure()
    if (headings.h1Count === 1) score += 10
    if (headings.h2Count >= 3) score += 10

    // Content length (20 points)
    const wordCount = this.getWordCount()
    if (wordCount >= 2000 && wordCount <= 3000) score += 20
    else if (wordCount >= 1500) score += 15
    else score += 5

    // Internal structure (20 points)
    if (this.hasIntroduction()) score += 10
    if (this.hasConclusion()) score += 10

    // Engagement elements (20 points)
    if (this.countTestimonials() > 0) score += 10
    if (this.countImages() > 0) score += 10

    return Math.round((score / maxScore) * 100)
  }

  // Calculate readability score (simplified Flesch Reading Ease)
  private calculateReadabilityScore(): number {
    const sentences = this.content.split(/[.!?]+/).filter(s => s.trim().length > 0)
    const words = this.getWordCount()
    const syllables = this.countSyllables()

    if (sentences.length === 0 || words === 0) return 0

    const avgSentenceLength = words / sentences.length
    const avgSyllablesPerWord = syllables / words

    // Simplified Flesch Reading Ease formula
    const score = 206.835 - (1.015 * avgSentenceLength) - (84.6 * avgSyllablesPerWord)
    
    return Math.max(0, Math.min(100, Math.round(score)))
  }

  // Count syllables in content
  private countSyllables(): number {
    const words = this.content.toLowerCase().match(/\b[a-z]+\b/g) || []
    return words.reduce((total, word) => {
      // Simple syllable counting
      const vowels = word.match(/[aeiouy]+/g) || []
      let syllableCount = vowels.length
      if (word.endsWith('e')) syllableCount--
      return total + Math.max(1, syllableCount)
    }, 0)
  }

  // Count testimonials
  private countTestimonials(): number {
    const testimonialPattern = /\*\*"[^"]+"\*\*\s*-\s*[^,]+,/g
    const matches = this.content.match(testimonialPattern) || []
    return matches.length
  }

  // Count images
  private countImages(): number {
    const imagePattern = /!\[[^\]]*\]\([^)]+\)/g
    const matches = this.content.match(imagePattern) || []
    return matches.length
  }

  // Count links
  private countLinks(): number {
    const linkPattern = /\[[^\]]+\]\([^)]+\)/g
    const matches = this.content.match(linkPattern) || []
    return matches.length
  }

  // Check for table of contents
  private hasTableOfContents(): boolean {
    return /table of contents|contents:/i.test(this.content)
  }

  // Check for conclusion
  private hasConclusion(): boolean {
    return /## Conclusion|# Conclusion/i.test(this.content)
  }

  // Check for introduction
  private hasIntroduction(): boolean {
    return /## Introduction|# Introduction/i.test(this.content)
  }

  // Check for meta description
  private hasMetaDescription(): boolean {
    return this.content.includes('*Last updated:') || this.content.includes('Reading time:')
  }

  // Calculate overall quality grade
  private calculateOverallGrade(metrics: Partial<QualityMetrics>): 'A' | 'B' | 'C' | 'D' | 'F' {
    let score = 0
    let maxScore = 0

    // Word count (25 points)
    maxScore += 25
    if (metrics.wordCount! >= 2000 && metrics.wordCount! <= 3000) score += 25
    else if (metrics.wordCount! >= 1500) score += 20
    else if (metrics.wordCount! >= 1000) score += 15
    else score += 5

    // SEO score (25 points)
    maxScore += 25
    score += Math.round((metrics.seoScore! / 100) * 25)

    // Structure (25 points)
    maxScore += 25
    if (metrics.hasIntroduction) score += 8
    if (metrics.hasConclusion) score += 8
    if (metrics.headingStructure!.hasProperHierarchy) score += 9

    // Engagement (25 points)
    maxScore += 25
    if (metrics.testimonialCount! > 0) score += 12
    if (metrics.hasTableOfContents) score += 8
    if (metrics.readabilityScore! > 60) score += 5

    const percentage = (score / maxScore) * 100

    if (percentage >= 90) return 'A'
    if (percentage >= 80) return 'B'
    if (percentage >= 70) return 'C'
    if (percentage >= 60) return 'D'
    return 'F'
  }
}

// Quality assurance utilities
export const qualityUtils = {
  // Analyze multiple articles
  analyzeArticles: (articles: Array<{ content: string; title: string }>) => {
    return articles.map(article => {
      const analyzer = new ContentQualityAnalyzer(article.content, article.title)
      return {
        title: article.title,
        ...analyzer.analyzeQuality()
      }
    })
  },

  // Generate quality report
  generateQualityReport: (analyses: Array<{ title: string; metrics: QualityMetrics; issues: ContentIssue[] }>) => {
    const totalArticles = analyses.length
    const averageWordCount = Math.round(
      analyses.reduce((sum, a) => sum + a.metrics.wordCount, 0) / totalArticles
    )
    const averageSEOScore = Math.round(
      analyses.reduce((sum, a) => sum + a.metrics.seoScore, 0) / totalArticles
    )
    const gradeDistribution = analyses.reduce((dist, a) => {
      dist[a.metrics.qualityGrade] = (dist[a.metrics.qualityGrade] || 0) + 1
      return dist
    }, {} as Record<string, number>)

    const totalIssues = analyses.reduce((sum, a) => sum + a.issues.length, 0)
    const highSeverityIssues = analyses.reduce(
      (sum, a) => sum + a.issues.filter(i => i.severity === 'high').length, 0
    )

    return {
      summary: {
        totalArticles,
        averageWordCount,
        averageSEOScore,
        gradeDistribution,
        totalIssues,
        highSeverityIssues
      },
      articles: analyses
    }
  }
}

export default ContentQualityAnalyzer
