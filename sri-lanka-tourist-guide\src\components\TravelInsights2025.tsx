import React, { useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import {
  ChartBarIcon,
  GlobeAltIcon,
  ArrowTrendingUpIcon,
  UserGroupIcon,
  QuestionMarkCircleIcon,
  ExclamationTriangleIcon,
  HeartIcon,
  LightBulbIcon
} from '@heroicons/react/24/outline'
import { travelTrends2025, touristInsights2025, globalRankings2025, travelStatistics2025 } from '@/data/travelTrends2025'

interface TravelInsights2025Props {
  className?: string
}

const TravelInsights2025: React.FC<TravelInsights2025Props> = ({ className = '' }) => {
  const [activeTab, setActiveTab] = useState<'trends' | 'insights' | 'rankings' | 'statistics'>('trends')

  const tabs = [
    { id: 'trends', label: '2025 Trends', icon: ArrowTrendingUpIcon },
    { id: 'insights', label: 'Tourist Insights', icon: UserGroupIcon },
    { id: 'rankings', label: 'Global Rankings', icon: ChartBarIcon },
    { id: 'statistics', label: 'Travel Stats', icon: GlobeAltIcon }
  ]

  const getInsightIcon = (category: string) => {
    switch (category) {
      case 'common-question':
        return QuestionMarkCircleIcon
      case 'pain-point':
        return ExclamationTriangleIcon
      case 'positive-feedback':
        return HeartIcon
      case 'recommendation':
        return LightBulbIcon
      default:
        return QuestionMarkCircleIcon
    }
  }

  const getInsightColor = (category: string) => {
    switch (category) {
      case 'common-question':
        return 'text-blue-600 bg-blue-100'
      case 'pain-point':
        return 'text-red-600 bg-red-100'
      case 'positive-feedback':
        return 'text-green-600 bg-green-100'
      case 'recommendation':
        return 'text-purple-600 bg-purple-100'
      default:
        return 'text-gray-600 bg-gray-100'
    }
  }

  return (
    <section className={`py-16 px-4 sm:px-6 lg:px-8 bg-gradient-to-br from-blue-50 to-purple-50 ${className}`}>
      <div className="container-max">
        <div className="text-center mb-12">
          <motion.h2
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
            className="text-3xl md:text-4xl font-bold text-gray-900 mb-4"
          >
            Sri Lanka Travel Insights 2025
          </motion.h2>
          <motion.p
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            viewport={{ once: true }}
            className="text-lg text-gray-600 max-w-3xl mx-auto"
          >
            Stay ahead with the latest travel trends, tourist insights, and comprehensive data about Sri Lanka
          </motion.p>
        </div>

        {/* Tab Navigation */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.3 }}
          viewport={{ once: true }}
          className="flex flex-wrap justify-center mb-8 bg-white rounded-lg p-2 shadow-lg"
        >
          {tabs.map((tab) => {
            const Icon = tab.icon
            return (
              <button
                type="button"
                key={tab.id}
                onClick={() => setActiveTab(tab.id as any)}
                className={`flex items-center space-x-2 px-6 py-3 rounded-lg font-medium transition-all duration-300 ${
                  activeTab === tab.id
                    ? 'bg-primary-600 text-white shadow-lg'
                    : 'text-gray-600 hover:text-primary-600 hover:bg-primary-50'
                }`}
              >
                <Icon className="h-5 w-5" />
                <span>{tab.label}</span>
              </button>
            )
          })}
        </motion.div>

        {/* Tab Content */}
        <AnimatePresence mode="wait">
          <motion.div
            key={activeTab}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            transition={{ duration: 0.4 }}
          >
            {activeTab === 'trends' && (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {travelTrends2025.map((trend) => (
                  <motion.div
                    key={trend.id}
                    className="bg-white rounded-lg shadow-lg p-6 hover:shadow-xl transition-shadow duration-300"
                    whileHover={{ y: -5 }}
                  >
                    <h3 className="text-xl font-bold text-gray-900 mb-3">{trend.name}</h3>
                    <p className="text-gray-600 mb-4">{trend.description}</p>
                    <div className="mb-4">
                      <h4 className="font-semibold text-gray-900 mb-2">In Sri Lanka:</h4>
                      <p className="text-sm text-gray-600">{trend.relevanceToSriLanka}</p>
                    </div>
                    <div className="mb-4">
                      <h4 className="font-semibold text-gray-900 mb-2">Examples:</h4>
                      <ul className="text-sm text-gray-600 space-y-1">
                        {trend.examples.slice(0, 3).map((example, idx) => (
                          <li key={idx} className="flex items-start">
                            <span className="text-primary-600 mr-2">•</span>
                            {example}
                          </li>
                        ))}
                      </ul>
                    </div>
                  </motion.div>
                ))}
              </div>
            )}

            {activeTab === 'insights' && (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {touristInsights2025.map((insight) => {
                  const Icon = getInsightIcon(insight.category)
                  const colorClass = getInsightColor(insight.category)
                  
                  return (
                    <motion.div
                      key={insight.id}
                      className="bg-white rounded-lg shadow-lg p-6 hover:shadow-xl transition-shadow duration-300"
                      whileHover={{ scale: 1.02 }}
                    >
                      <div className="flex items-start space-x-3 mb-4">
                        <div className={`p-2 rounded-lg ${colorClass}`}>
                          <Icon className="h-5 w-5" />
                        </div>
                        <div className="flex-1">
                          <div className="flex items-center space-x-2 mb-2">
                            <span className="text-xs font-medium text-gray-500 uppercase tracking-wide">
                              {insight.category.replace('-', ' ')}
                            </span>
                            <span className={`text-xs px-2 py-1 rounded-full ${
                              insight.frequency === 'very-common' ? 'bg-red-100 text-red-700' :
                              insight.frequency === 'common' ? 'bg-yellow-100 text-yellow-700' :
                              'bg-green-100 text-green-700'
                            }`}>
                              {insight.frequency}
                            </span>
                          </div>
                          <h3 className="text-lg font-semibold text-gray-900 mb-2">{insight.question}</h3>
                          <p className="text-gray-600 text-sm">{insight.answer}</p>
                        </div>
                      </div>
                    </motion.div>
                  )
                })}
              </div>
            )}

            {activeTab === 'rankings' && (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {globalRankings2025.map((ranking) => (
                  <motion.div
                    key={ranking.id}
                    className="bg-white rounded-lg shadow-lg p-6 hover:shadow-xl transition-shadow duration-300"
                    whileHover={{ y: -5 }}
                  >
                    <div className="text-center mb-4">
                      <div className="text-3xl font-bold text-primary-600 mb-2">{ranking.ranking}</div>
                      <h3 className="text-lg font-semibold text-gray-900">{ranking.category}</h3>
                    </div>
                    <p className="text-gray-600 text-sm mb-3">{ranking.description}</p>
                    <div className="text-xs text-gray-500">
                      <strong>Source:</strong> {ranking.source} ({ranking.year})
                    </div>
                  </motion.div>
                ))}
              </div>
            )}

            {activeTab === 'statistics' && (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                <motion.div
                  className="bg-white rounded-lg shadow-lg p-6 text-center"
                  whileHover={{ scale: 1.05 }}
                >
                  <div className="text-3xl font-bold text-primary-600 mb-2">{travelStatistics2025.averageStayDuration}</div>
                  <div className="text-gray-600 font-medium">Average Stay</div>
                </motion.div>

                <motion.div
                  className="bg-white rounded-lg shadow-lg p-6"
                  whileHover={{ scale: 1.05 }}
                >
                  <h4 className="font-semibold text-gray-900 mb-3">Top Source Markets</h4>
                  <ul className="text-sm text-gray-600 space-y-1">
                    {travelStatistics2025.topSourceMarkets.slice(0, 4).map((market, idx) => (
                      <li key={idx} className="flex items-center">
                        <span className="text-primary-600 mr-2">•</span>
                        {market}
                      </li>
                    ))}
                  </ul>
                </motion.div>

                <motion.div
                  className="bg-white rounded-lg shadow-lg p-6"
                  whileHover={{ scale: 1.05 }}
                >
                  <h4 className="font-semibold text-gray-900 mb-3">Popular Activities</h4>
                  <ul className="text-sm text-gray-600 space-y-1">
                    {travelStatistics2025.popularActivities.slice(0, 4).map((activity, idx) => (
                      <li key={idx} className="flex items-center">
                        <span className="text-primary-600 mr-2">•</span>
                        {activity}
                      </li>
                    ))}
                  </ul>
                </motion.div>

                <motion.div
                  className="bg-white rounded-lg shadow-lg p-6"
                  whileHover={{ scale: 1.05 }}
                >
                  <h4 className="font-semibold text-gray-900 mb-3">Sustainability Growth</h4>
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span className="text-gray-600">Eco Accommodation</span>
                      <span className="text-green-600 font-medium">+35%</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Wildlife Conservation</span>
                      <span className="text-green-600 font-medium">+28%</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Community Tourism</span>
                      <span className="text-green-600 font-medium">+42%</span>
                    </div>
                  </div>
                </motion.div>
              </div>
            )}
          </motion.div>
        </AnimatePresence>
      </div>
    </section>
  )
}

export default TravelInsights2025
