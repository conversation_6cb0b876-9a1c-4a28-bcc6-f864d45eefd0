import { test, expect } from '@playwright/test';

test.describe('Accessibility Testing', () => {
  test('WCAG 2.1 AA Compliance - Home Page', async ({ page }) => {
    await page.goto('/');
    await page.waitForLoadState('networkidle');
    
    // Check for basic accessibility requirements
    const accessibilityChecks = await page.evaluate(() => {
      const checks = {
        hasDoctype: document.doctype !== null,
        hasLang: document.documentElement.hasAttribute('lang'),
        hasTitle: document.title.length > 0,
        hasMetaViewport: document.querySelector('meta[name="viewport"]') !== null,
        hasSkipLink: document.querySelector('a[href="#main"]') !== null,
        hasHeadingStructure: document.querySelector('h1') !== null,
        imagesHaveAlt: Array.from(document.querySelectorAll('img')).every(img => img.hasAttribute('alt')),
        linksHaveText: Array.from(document.querySelectorAll('a')).every(link => 
          link.textContent?.trim() || link.getAttribute('aria-label') || link.querySelector('img[alt]')
        ),
        buttonsHaveText: Array.from(document.querySelectorAll('button')).every(btn => 
          btn.textContent?.trim() || btn.getAttribute('aria-label') || btn.querySelector('img[alt]')
        ),
        formsHaveLabels: Array.from(document.querySelectorAll('input, select, textarea')).every(input => {
          const id = input.id;
          return id && document.querySelector(`label[for="${id}"]`) || input.getAttribute('aria-label');
        })
      };
      
      return checks;
    });
    
    console.log('Accessibility checks:', accessibilityChecks);
    
    // Assert accessibility requirements
    expect(accessibilityChecks.hasDoctype).toBe(true);
    expect(accessibilityChecks.hasLang).toBe(true);
    expect(accessibilityChecks.hasTitle).toBe(true);
    expect(accessibilityChecks.hasMetaViewport).toBe(true);
    expect(accessibilityChecks.hasHeadingStructure).toBe(true);
    expect(accessibilityChecks.imagesHaveAlt).toBe(true);
    expect(accessibilityChecks.linksHaveText).toBe(true);
    expect(accessibilityChecks.buttonsHaveText).toBe(true);
  });

  test('Keyboard Navigation', async ({ page }) => {
    await page.goto('/');
    await page.waitForLoadState('networkidle');
    
    // Test tab navigation
    const focusableElements = await page.evaluate(() => {
      const elements = Array.from(document.querySelectorAll(
        'a, button, input, select, textarea, [tabindex]:not([tabindex="-1"])'
      ));
      return elements.length;
    });
    
    console.log(`Found ${focusableElements} focusable elements`);
    
    // Test keyboard navigation through first few elements
    for (let i = 0; i < Math.min(focusableElements, 10); i++) {
      await page.keyboard.press('Tab');
      await page.waitForTimeout(100);
      
      // Check if element is focused
      const focusedElement = await page.evaluate(() => {
        const focused = document.activeElement;
        return {
          tagName: focused?.tagName,
          hasVisibleFocus: window.getComputedStyle(focused!).outline !== 'none'
        };
      });
      
      expect(focusedElement.tagName).toBeTruthy();
    }
  });

  test('Color Contrast', async ({ page }) => {
    await page.goto('/');
    await page.waitForLoadState('networkidle');
    
    // Check color contrast for text elements
    const contrastIssues = await page.evaluate(() => {
      const getContrast = (fg: string, bg: string) => {
        // Simplified contrast calculation
        const getLuminance = (color: string) => {
          const rgb = color.match(/\d+/g);
          if (!rgb) return 0;
          const [r, g, b] = rgb.map(x => {
            x = parseInt(x) / 255;
            return x <= 0.03928 ? x / 12.92 : Math.pow((x + 0.055) / 1.055, 2.4);
          });
          return 0.2126 * r + 0.7152 * g + 0.0722 * b;
        };
        
        const l1 = getLuminance(fg);
        const l2 = getLuminance(bg);
        return (Math.max(l1, l2) + 0.05) / (Math.min(l1, l2) + 0.05);
      };
      
      const textElements = Array.from(document.querySelectorAll('p, h1, h2, h3, h4, h5, h6, span, a, button'));
      const issues: any[] = [];
      
      textElements.slice(0, 20).forEach((element, index) => {
        const styles = window.getComputedStyle(element);
        const color = styles.color;
        const backgroundColor = styles.backgroundColor;
        
        if (color && backgroundColor && backgroundColor !== 'rgba(0, 0, 0, 0)') {
          const contrast = getContrast(color, backgroundColor);
          if (contrast < 4.5) { // WCAG AA standard
            issues.push({
              element: element.tagName,
              color,
              backgroundColor,
              contrast,
              text: element.textContent?.substring(0, 50)
            });
          }
        }
      });
      
      return issues;
    });
    
    console.log('Color contrast issues:', contrastIssues);
    expect(contrastIssues.length).toBeLessThan(5); // Allow some flexibility
  });

  test('Screen Reader Compatibility', async ({ page }) => {
    await page.goto('/');
    await page.waitForLoadState('networkidle');
    
    // Check for screen reader friendly elements
    const screenReaderChecks = await page.evaluate(() => {
      return {
        hasMainLandmark: document.querySelector('main, [role="main"]') !== null,
        hasNavLandmark: document.querySelector('nav, [role="navigation"]') !== null,
        hasHeaderLandmark: document.querySelector('header, [role="banner"]') !== null,
        hasFooterLandmark: document.querySelector('footer, [role="contentinfo"]') !== null,
        hasAriaLabels: document.querySelectorAll('[aria-label]').length > 0,
        hasAriaDescribedBy: document.querySelectorAll('[aria-describedby]').length > 0,
        hasHeadingHierarchy: (() => {
          const headings = Array.from(document.querySelectorAll('h1, h2, h3, h4, h5, h6'));
          if (headings.length === 0) return false;
          
          const levels = headings.map(h => parseInt(h.tagName.charAt(1)));
          return levels[0] === 1; // Should start with h1
        })()
      };
    });
    
    console.log('Screen reader checks:', screenReaderChecks);
    
    expect(screenReaderChecks.hasMainLandmark).toBe(true);
    expect(screenReaderChecks.hasNavLandmark).toBe(true);
    expect(screenReaderChecks.hasHeaderLandmark).toBe(true);
    expect(screenReaderChecks.hasFooterLandmark).toBe(true);
    expect(screenReaderChecks.hasHeadingHierarchy).toBe(true);
  });

  test('Form Accessibility', async ({ page }) => {
    await page.goto('/contact');
    await page.waitForLoadState('networkidle');
    
    // Check form accessibility
    const formChecks = await page.evaluate(() => {
      const forms = Array.from(document.querySelectorAll('form'));
      const checks: any[] = [];
      
      forms.forEach((form, formIndex) => {
        const inputs = Array.from(form.querySelectorAll('input, select, textarea'));
        
        inputs.forEach((input, inputIndex) => {
          const id = input.id;
          const label = id ? document.querySelector(`label[for="${id}"]`) : null;
          const ariaLabel = input.getAttribute('aria-label');
          const ariaLabelledBy = input.getAttribute('aria-labelledby');
          const placeholder = input.getAttribute('placeholder');
          
          checks.push({
            formIndex,
            inputIndex,
            type: input.tagName,
            hasId: !!id,
            hasLabel: !!label,
            hasAriaLabel: !!ariaLabel,
            hasAriaLabelledBy: !!ariaLabelledBy,
            hasPlaceholder: !!placeholder,
            isAccessible: !!(label || ariaLabel || ariaLabelledBy)
          });
        });
      });
      
      return checks;
    });
    
    console.log('Form accessibility checks:', formChecks);
    
    // Assert all form inputs are accessible
    formChecks.forEach(check => {
      expect(check.isAccessible).toBe(true);
    });
  });

  test('Focus Management', async ({ page }) => {
    await page.goto('/');
    await page.waitForLoadState('networkidle');
    
    // Test focus trap in mobile menu
    await page.setViewportSize({ width: 375, height: 667 });
    await page.click('[aria-label="Open navigation menu"]');
    await page.waitForTimeout(500);
    
    // Check if focus is managed properly
    const focusInMenu = await page.evaluate(() => {
      const menu = document.querySelector('[data-testid="mobile-menu"]');
      const activeElement = document.activeElement;
      return menu?.contains(activeElement) || false;
    });
    
    // Test escape key closes menu
    await page.keyboard.press('Escape');
    await page.waitForTimeout(500);
    
    const menuClosed = await page.isHidden('[data-testid="mobile-menu"]');
    expect(menuClosed).toBe(true);
  });

  test('Alternative Text for Images', async ({ page }) => {
    const pages = ['/', '/destinations', '/articles'];
    
    for (const url of pages) {
      await page.goto(url);
      await page.waitForLoadState('networkidle');
      
      const imageIssues = await page.evaluate(() => {
        const images = Array.from(document.querySelectorAll('img'));
        return images.filter(img => !img.alt || img.alt.trim() === '').map(img => ({
          src: img.src,
          alt: img.alt
        }));
      });
      
      console.log(`${url} - Images without alt text:`, imageIssues.length);
      expect(imageIssues.length).toBe(0);
    }
  });

  test('ARIA Attributes Validation', async ({ page }) => {
    await page.goto('/');
    await page.waitForLoadState('networkidle');
    
    const ariaIssues = await page.evaluate(() => {
      const issues: any[] = [];
      
      // Check for invalid ARIA attributes
      const elementsWithAria = Array.from(document.querySelectorAll('[aria-label], [aria-labelledby], [aria-describedby], [role]'));
      
      elementsWithAria.forEach(element => {
        const ariaLabel = element.getAttribute('aria-label');
        const ariaLabelledBy = element.getAttribute('aria-labelledby');
        const ariaDescribedBy = element.getAttribute('aria-describedby');
        const role = element.getAttribute('role');
        
        // Check for empty aria-label
        if (ariaLabel === '') {
          issues.push({ type: 'empty-aria-label', element: element.tagName });
        }
        
        // Check for invalid aria-labelledby references
        if (ariaLabelledBy) {
          const referenced = document.getElementById(ariaLabelledBy);
          if (!referenced) {
            issues.push({ type: 'invalid-aria-labelledby', element: element.tagName, id: ariaLabelledBy });
          }
        }
        
        // Check for invalid aria-describedby references
        if (ariaDescribedBy) {
          const referenced = document.getElementById(ariaDescribedBy);
          if (!referenced) {
            issues.push({ type: 'invalid-aria-describedby', element: element.tagName, id: ariaDescribedBy });
          }
        }
      });
      
      return issues;
    });
    
    console.log('ARIA issues:', ariaIssues);
    expect(ariaIssues.length).toBe(0);
  });
});
