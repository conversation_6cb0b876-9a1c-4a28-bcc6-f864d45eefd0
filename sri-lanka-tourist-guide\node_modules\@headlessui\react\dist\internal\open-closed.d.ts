import React, { type ReactElement, type ReactNode } from 'react';
export declare enum State {
    Open = 1,
    Closed = 2,
    Closing = 4,
    Opening = 8
}
export declare function useOpenClosed(): State | null;
interface Props {
    value: State;
    children: ReactNode;
}
export declare function OpenClosedProvider({ value, children }: Props): ReactElement;
export declare function ResetOpenClosedProvider({ children }: {
    children: React.ReactNode;
}): ReactElement;
export {};
