<testsuites id="" name="" tests="40" failures="34" skipped="0" errors="0" time="266.068153">
<testsuite name="mobile-timeline.spec.ts" timestamp="2025-06-24T10:31:01.818Z" hostname="chromium" tests="8" failures="8" skipped="0" time="79.069" errors="0">
<testcase name="Mobile Timeline Layout › should display timeline correctly on mobile viewport" classname="mobile-timeline.spec.ts" time="14.956">
<failure message="mobile-timeline.spec.ts:9:3 should display timeline correctly on mobile viewport" type="FAILURE">
<![CDATA[  [chromium] › mobile-timeline.spec.ts:9:3 › Mobile Timeline Layout › should display timeline correctly on mobile viewport 

    TimeoutError: page.waitForSelector: Timeout 10000ms exceeded.
    Call log:
      - waiting for locator('[data-testid="enhanced-timeline"]') to be visible


      12 |     
      13 |     // Wait for timeline to load
    > 14 |     await page.waitForSelector('[data-testid="enhanced-timeline"]', { timeout: 10000 })
         |                ^
      15 |     
      16 |     // Check that timeline line is positioned on the left for mobile
      17 |     const timelineLine = page.locator('.absolute.left-6')
        at E:\Augment Code Testing\sri-lanka-tourist-guide\tests\mobile-timeline.spec.ts:14:16

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results\mobile-timeline-Mobile-Tim-846c2-orrectly-on-mobile-viewport-chromium\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results\mobile-timeline-Mobile-Tim-846c2-orrectly-on-mobile-viewport-chromium\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|mobile-timeline-Mobile-Tim-846c2-orrectly-on-mobile-viewport-chromium\test-failed-1.png]]

[[ATTACHMENT|mobile-timeline-Mobile-Tim-846c2-orrectly-on-mobile-viewport-chromium\video.webm]]
]]>
</system-out>
</testcase>
<testcase name="Mobile Timeline Layout › should display timeline correctly on desktop viewport" classname="mobile-timeline.spec.ts" time="16.118">
<failure message="mobile-timeline.spec.ts:40:3 should display timeline correctly on desktop viewport" type="FAILURE">
<![CDATA[  [chromium] › mobile-timeline.spec.ts:40:3 › Mobile Timeline Layout › should display timeline correctly on desktop viewport 

    TimeoutError: page.waitForSelector: Timeout 10000ms exceeded.
    Call log:
      - waiting for locator('[data-testid="enhanced-timeline"]') to be visible


      43 |     
      44 |     // Wait for timeline to load
    > 45 |     await page.waitForSelector('[data-testid="enhanced-timeline"]', { timeout: 10000 })
         |                ^
      46 |     
      47 |     // Check that timeline line is centered for desktop
      48 |     const timelineLine = page.locator('.md\\:left-1\\/2')
        at E:\Augment Code Testing\sri-lanka-tourist-guide\tests\mobile-timeline.spec.ts:45:16

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results\mobile-timeline-Mobile-Tim-ddc63-rrectly-on-desktop-viewport-chromium\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results\mobile-timeline-Mobile-Tim-ddc63-rrectly-on-desktop-viewport-chromium\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|mobile-timeline-Mobile-Tim-ddc63-rrectly-on-desktop-viewport-chromium\test-failed-1.png]]

[[ATTACHMENT|mobile-timeline-Mobile-Tim-ddc63-rrectly-on-desktop-viewport-chromium\video.webm]]
]]>
</system-out>
</testcase>
<testcase name="Mobile Timeline Layout › should have responsive text sizes" classname="mobile-timeline.spec.ts" time="8.665">
<failure message="mobile-timeline.spec.ts:70:3 should have responsive text sizes" type="FAILURE">
<![CDATA[  [chromium] › mobile-timeline.spec.ts:70:3 › Mobile Timeline Layout › should have responsive text sizes 

    Error: Timed out 5000ms waiting for expect(locator).toHaveClass(expected)

    Locator: locator('h3').first()
    Expected pattern: /text-lg/
    Received: <element(s) not found>
    Call log:
      - Expect "toHaveClass" with timeout 5000ms
      - waiting for locator('h3').first()


      73 |     
      74 |     const title = page.locator('h3').first()
    > 75 |     await expect(title).toHaveClass(/text-lg/)
         |                         ^
      76 |     await expect(title).toHaveClass(/md:text-2xl/)
      77 |     
      78 |     const description = page.locator('.timeline-content p').first()
        at E:\Augment Code Testing\sri-lanka-tourist-guide\tests\mobile-timeline.spec.ts:75:25

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results\mobile-timeline-Mobile-Tim-07475--have-responsive-text-sizes-chromium\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results\mobile-timeline-Mobile-Tim-07475--have-responsive-text-sizes-chromium\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|mobile-timeline-Mobile-Tim-07475--have-responsive-text-sizes-chromium\test-failed-1.png]]

[[ATTACHMENT|mobile-timeline-Mobile-Tim-07475--have-responsive-text-sizes-chromium\video.webm]]
]]>
</system-out>
</testcase>
<testcase name="Mobile Timeline Layout › should handle scroll-triggered animations on mobile" classname="mobile-timeline.spec.ts" time="10.141">
<failure message="mobile-timeline.spec.ts:83:3 should handle scroll-triggered animations on mobile" type="FAILURE">
<![CDATA[  [chromium] › mobile-timeline.spec.ts:83:3 › Mobile Timeline Layout › should handle scroll-triggered animations on mobile 

    Error: Timed out 5000ms waiting for expect(locator).toBeVisible()

    Locator: locator('.timeline-content').first()
    Expected: visible
    Received: <element(s) not found>
    Call log:
      - Expect "toBeVisible" with timeout 5000ms
      - waiting for locator('.timeline-content').first()


       96 |     
       97 |     // The item should be visible after scroll trigger
    >  98 |     await expect(firstItem).toBeVisible()
          |                             ^
       99 |   })
      100 |
      101 |   test('should maintain accessibility on mobile', async ({ page }) => {
        at E:\Augment Code Testing\sri-lanka-tourist-guide\tests\mobile-timeline.spec.ts:98:29

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results\mobile-timeline-Mobile-Tim-161fb-ggered-animations-on-mobile-chromium\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results\mobile-timeline-Mobile-Tim-161fb-ggered-animations-on-mobile-chromium\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|mobile-timeline-Mobile-Tim-161fb-ggered-animations-on-mobile-chromium\test-failed-1.png]]

[[ATTACHMENT|mobile-timeline-Mobile-Tim-161fb-ggered-animations-on-mobile-chromium\video.webm]]
]]>
</system-out>
</testcase>
<testcase name="Mobile Timeline Layout › should maintain accessibility on mobile" classname="mobile-timeline.spec.ts" time="3.072">
<failure message="mobile-timeline.spec.ts:101:3 should maintain accessibility on mobile" type="FAILURE">
<![CDATA[  [chromium] › mobile-timeline.spec.ts:101:3 › Mobile Timeline Layout › should maintain accessibility on mobile 

    Error: expect(received).toBeGreaterThan(expected)

    Expected: > 0
    Received:   0

      105 |     const headings = page.locator('h3, h4')
      106 |     const headingCount = await headings.count()
    > 107 |     expect(headingCount).toBeGreaterThan(0)
          |                          ^
      108 |     
      109 |     // Verify text contrast and readability
      110 |     const textElements = page.locator('.text-gray-600')
        at E:\Augment Code Testing\sri-lanka-tourist-guide\tests\mobile-timeline.spec.ts:107:26

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results\mobile-timeline-Mobile-Tim-6fecf-ain-accessibility-on-mobile-chromium\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results\mobile-timeline-Mobile-Tim-6fecf-ain-accessibility-on-mobile-chromium\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|mobile-timeline-Mobile-Tim-6fecf-ain-accessibility-on-mobile-chromium\test-failed-1.png]]

[[ATTACHMENT|mobile-timeline-Mobile-Tim-6fecf-ain-accessibility-on-mobile-chromium\video.webm]]
]]>
</system-out>
</testcase>
<testcase name="Mobile Timeline Layout › should handle content overflow properly on mobile" classname="mobile-timeline.spec.ts" time="15.38">
<failure message="mobile-timeline.spec.ts:126:3 should handle content overflow properly on mobile" type="FAILURE">
<![CDATA[  [chromium] › mobile-timeline.spec.ts:126:3 › Mobile Timeline Layout › should handle content overflow properly on mobile 

    TimeoutError: locator.boundingBox: Timeout 10000ms exceeded.
    Call log:
      - waiting for locator('.timeline-content').first()


      129 |     // Check that content doesn't overflow horizontally
      130 |     const timelineContent = page.locator('.timeline-content').first()
    > 131 |     const boundingBox = await timelineContent.boundingBox()
          |                                               ^
      132 |     
      133 |     if (boundingBox) {
      134 |       // Content should not exceed viewport width minus margins
        at E:\Augment Code Testing\sri-lanka-tourist-guide\tests\mobile-timeline.spec.ts:131:47

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results\mobile-timeline-Mobile-Tim-86076-overflow-properly-on-mobile-chromium\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results\mobile-timeline-Mobile-Tim-86076-overflow-properly-on-mobile-chromium\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|mobile-timeline-Mobile-Tim-86076-overflow-properly-on-mobile-chromium\test-failed-1.png]]

[[ATTACHMENT|mobile-timeline-Mobile-Tim-86076-overflow-properly-on-mobile-chromium\video.webm]]
]]>
</system-out>
</testcase>
<testcase name="Mobile Timeline Layout › should display era badges responsively" classname="mobile-timeline.spec.ts" time="7.875">
<failure message="mobile-timeline.spec.ts:144:3 should display era badges responsively" type="FAILURE">
<![CDATA[  [chromium] › mobile-timeline.spec.ts:144:3 › Mobile Timeline Layout › should display era badges responsively 

    Error: Timed out 5000ms waiting for expect(locator).toBeVisible()

    Locator: locator('.rounded-full').first()
    Expected: visible
    Received: <element(s) not found>
    Call log:
      - Expect "toBeVisible" with timeout 5000ms
      - waiting for locator('.rounded-full').first()


      147 |     
      148 |     const eraBadge = page.locator('.rounded-full').first()
    > 149 |     await expect(eraBadge).toBeVisible()
          |                            ^
      150 |     
      151 |     // Badge should have mobile-appropriate text size
      152 |     await expect(eraBadge).toHaveClass(/text-xs/)
        at E:\Augment Code Testing\sri-lanka-tourist-guide\tests\mobile-timeline.spec.ts:149:28

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results\mobile-timeline-Mobile-Tim-6f717-lay-era-badges-responsively-chromium\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results\mobile-timeline-Mobile-Tim-6f717-lay-era-badges-responsively-chromium\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|mobile-timeline-Mobile-Tim-6f717-lay-era-badges-responsively-chromium\test-failed-1.png]]

[[ATTACHMENT|mobile-timeline-Mobile-Tim-6f717-lay-era-badges-responsively-chromium\video.webm]]
]]>
</system-out>
</testcase>
<testcase name="Mobile Timeline Layout › should handle touch interactions on mobile" classname="mobile-timeline.spec.ts" time="2.862">
<failure message="mobile-timeline.spec.ts:162:3 should handle touch interactions on mobile" type="FAILURE">
<![CDATA[  [chromium] › mobile-timeline.spec.ts:162:3 › Mobile Timeline Layout › should handle touch interactions on mobile 

    Error: locator.tap: The page does not support tap. Use hasTouch context option to enable touch support.

      167 |     
      168 |     // Touch should not cause layout issues
    > 169 |     await firstTimelineItem.tap()
          |                             ^
      170 |     await page.waitForTimeout(500)
      171 |     
      172 |     // Item should still be visible and properly positioned
        at E:\Augment Code Testing\sri-lanka-tourist-guide\tests\mobile-timeline.spec.ts:169:29

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results\mobile-timeline-Mobile-Tim-eeae3-ouch-interactions-on-mobile-chromium\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results\mobile-timeline-Mobile-Tim-eeae3-ouch-interactions-on-mobile-chromium\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|mobile-timeline-Mobile-Tim-eeae3-ouch-interactions-on-mobile-chromium\test-failed-1.png]]

[[ATTACHMENT|mobile-timeline-Mobile-Tim-eeae3-ouch-interactions-on-mobile-chromium\video.webm]]
]]>
</system-out>
</testcase>
</testsuite>
<testsuite name="mobile-timeline.spec.ts" timestamp="2025-06-24T10:31:01.818Z" hostname="firefox" tests="8" failures="7" skipped="0" time="132.897" errors="0">
<testcase name="Mobile Timeline Layout › should display timeline correctly on mobile viewport" classname="mobile-timeline.spec.ts" time="30.481">
<failure message="mobile-timeline.spec.ts:9:3 should display timeline correctly on mobile viewport" type="FAILURE">
<![CDATA[  [firefox] › mobile-timeline.spec.ts:9:3 › Mobile Timeline Layout › should display timeline correctly on mobile viewport 

    Test timeout of 30000ms exceeded.

    Error: page.waitForSelector: Test timeout of 30000ms exceeded.
    Call log:
      - waiting for locator('[data-testid="enhanced-timeline"]') to be visible


      12 |     
      13 |     // Wait for timeline to load
    > 14 |     await page.waitForSelector('[data-testid="enhanced-timeline"]', { timeout: 10000 })
         |                ^
      15 |     
      16 |     // Check that timeline line is positioned on the left for mobile
      17 |     const timelineLine = page.locator('.absolute.left-6')
        at E:\Augment Code Testing\sri-lanka-tourist-guide\tests\mobile-timeline.spec.ts:14:16

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results\mobile-timeline-Mobile-Tim-846c2-orrectly-on-mobile-viewport-firefox\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results\mobile-timeline-Mobile-Tim-846c2-orrectly-on-mobile-viewport-firefox\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|mobile-timeline-Mobile-Tim-846c2-orrectly-on-mobile-viewport-firefox\test-failed-1.png]]

[[ATTACHMENT|mobile-timeline-Mobile-Tim-846c2-orrectly-on-mobile-viewport-firefox\video.webm]]
]]>
</system-out>
</testcase>
<testcase name="Mobile Timeline Layout › should display timeline correctly on desktop viewport" classname="mobile-timeline.spec.ts" time="18.77">
<failure message="mobile-timeline.spec.ts:40:3 should display timeline correctly on desktop viewport" type="FAILURE">
<![CDATA[  [firefox] › mobile-timeline.spec.ts:40:3 › Mobile Timeline Layout › should display timeline correctly on desktop viewport 

    TimeoutError: page.waitForSelector: Timeout 10000ms exceeded.
    Call log:
      - waiting for locator('[data-testid="enhanced-timeline"]') to be visible


      43 |     
      44 |     // Wait for timeline to load
    > 45 |     await page.waitForSelector('[data-testid="enhanced-timeline"]', { timeout: 10000 })
         |                ^
      46 |     
      47 |     // Check that timeline line is centered for desktop
      48 |     const timelineLine = page.locator('.md\\:left-1\\/2')
        at E:\Augment Code Testing\sri-lanka-tourist-guide\tests\mobile-timeline.spec.ts:45:16

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results\mobile-timeline-Mobile-Tim-ddc63-rrectly-on-desktop-viewport-firefox\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results\mobile-timeline-Mobile-Tim-ddc63-rrectly-on-desktop-viewport-firefox\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|mobile-timeline-Mobile-Tim-ddc63-rrectly-on-desktop-viewport-firefox\test-failed-1.png]]

[[ATTACHMENT|mobile-timeline-Mobile-Tim-ddc63-rrectly-on-desktop-viewport-firefox\video.webm]]
]]>
</system-out>
</testcase>
<testcase name="Mobile Timeline Layout › should have responsive text sizes" classname="mobile-timeline.spec.ts" time="13.422">
<failure message="mobile-timeline.spec.ts:70:3 should have responsive text sizes" type="FAILURE">
<![CDATA[  [firefox] › mobile-timeline.spec.ts:70:3 › Mobile Timeline Layout › should have responsive text sizes 

    Error: Timed out 5000ms waiting for expect(locator).toHaveClass(expected)

    Locator: locator('h3').first()
    Expected pattern: /text-lg/
    Received: <element(s) not found>
    Call log:
      - Expect "toHaveClass" with timeout 5000ms
      - waiting for locator('h3').first()


      73 |     
      74 |     const title = page.locator('h3').first()
    > 75 |     await expect(title).toHaveClass(/text-lg/)
         |                         ^
      76 |     await expect(title).toHaveClass(/md:text-2xl/)
      77 |     
      78 |     const description = page.locator('.timeline-content p').first()
        at E:\Augment Code Testing\sri-lanka-tourist-guide\tests\mobile-timeline.spec.ts:75:25

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results\mobile-timeline-Mobile-Tim-07475--have-responsive-text-sizes-firefox\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results\mobile-timeline-Mobile-Tim-07475--have-responsive-text-sizes-firefox\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|mobile-timeline-Mobile-Tim-07475--have-responsive-text-sizes-firefox\test-failed-1.png]]

[[ATTACHMENT|mobile-timeline-Mobile-Tim-07475--have-responsive-text-sizes-firefox\video.webm]]
]]>
</system-out>
</testcase>
<testcase name="Mobile Timeline Layout › should handle scroll-triggered animations on mobile" classname="mobile-timeline.spec.ts" time="12.455">
</testcase>
<testcase name="Mobile Timeline Layout › should maintain accessibility on mobile" classname="mobile-timeline.spec.ts" time="9.715">
<failure message="mobile-timeline.spec.ts:101:3 should maintain accessibility on mobile" type="FAILURE">
<![CDATA[  [firefox] › mobile-timeline.spec.ts:101:3 › Mobile Timeline Layout › should maintain accessibility on mobile 

    Error: expect(received).toBeGreaterThanOrEqual(expected)

    Expected: >= 16
    Received:    14.400001525878906

      119 |     if (boundingBox) {
      120 |       // On mobile, dots should be at least 16px (w-4 h-4)
    > 121 |       expect(boundingBox.width).toBeGreaterThanOrEqual(16)
          |                                 ^
      122 |       expect(boundingBox.height).toBeGreaterThanOrEqual(16)
      123 |     }
      124 |   })
        at E:\Augment Code Testing\sri-lanka-tourist-guide\tests\mobile-timeline.spec.ts:121:33

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results\mobile-timeline-Mobile-Tim-6fecf-ain-accessibility-on-mobile-firefox\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results\mobile-timeline-Mobile-Tim-6fecf-ain-accessibility-on-mobile-firefox\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\test-results\mobile-timeline-Mobile-Tim-6fecf-ain-accessibility-on-mobile-firefox\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|mobile-timeline-Mobile-Tim-6fecf-ain-accessibility-on-mobile-firefox\test-failed-1.png]]

[[ATTACHMENT|mobile-timeline-Mobile-Tim-6fecf-ain-accessibility-on-mobile-firefox\video.webm]]

[[ATTACHMENT|mobile-timeline-Mobile-Tim-6fecf-ain-accessibility-on-mobile-firefox\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Mobile Timeline Layout › should handle content overflow properly on mobile" classname="mobile-timeline.spec.ts" time="16.182">
<failure message="mobile-timeline.spec.ts:126:3 should handle content overflow properly on mobile" type="FAILURE">
<![CDATA[  [firefox] › mobile-timeline.spec.ts:126:3 › Mobile Timeline Layout › should handle content overflow properly on mobile 

    Error: Timed out 5000ms waiting for expect(locator).toHaveCSS(expected)

    Locator: locator('.timeline-content p').first()
    Expected string: "break-word"
    Received string: "normal"
    Call log:
      - Expect "toHaveCSS" with timeout 5000ms
      - waiting for locator('.timeline-content p').first()
        5 × locator resolved to <p class="text-sm md:text-base text-gray-600 mb-4 md:mb-6 leading-relaxed">The earliest human settlements in Sri Lanka, span…</p>
          - unexpected value "normal"


      139 |     const longTexts = page.locator('.timeline-content p')
      140 |     const firstText = longTexts.first()
    > 141 |     await expect(firstText).toHaveCSS('word-wrap', 'break-word')
          |                             ^
      142 |   })
      143 |
      144 |   test('should display era badges responsively', async ({ page }) => {
        at E:\Augment Code Testing\sri-lanka-tourist-guide\tests\mobile-timeline.spec.ts:141:29

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results\mobile-timeline-Mobile-Tim-86076-overflow-properly-on-mobile-firefox\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results\mobile-timeline-Mobile-Tim-86076-overflow-properly-on-mobile-firefox\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\test-results\mobile-timeline-Mobile-Tim-86076-overflow-properly-on-mobile-firefox\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|mobile-timeline-Mobile-Tim-86076-overflow-properly-on-mobile-firefox\test-failed-1.png]]

[[ATTACHMENT|mobile-timeline-Mobile-Tim-86076-overflow-properly-on-mobile-firefox\video.webm]]

[[ATTACHMENT|mobile-timeline-Mobile-Tim-86076-overflow-properly-on-mobile-firefox\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Mobile Timeline Layout › should display era badges responsively" classname="mobile-timeline.spec.ts" time="20.999">
<failure message="mobile-timeline.spec.ts:144:3 should display era badges responsively" type="FAILURE">
<![CDATA[  [firefox] › mobile-timeline.spec.ts:144:3 › Mobile Timeline Layout › should display era badges responsively 

    Error: Timed out 5000ms waiting for expect(locator).toHaveClass(expected)

    Locator: locator('.rounded-full').first()
    Expected pattern: /text-xs/
    Received string:  "inline-block px-3 py-1 bg-orange-100 text-orange-700 text-sm rounded-full"
    Call log:
      - Expect "toHaveClass" with timeout 5000ms
      - waiting for locator('.rounded-full').first()
        8 × locator resolved to <span class="inline-block px-3 py-1 bg-orange-100 text-orange-700 text-sm rounded-full">Sacred Tooth Relic</span>
          - unexpected value "inline-block px-3 py-1 bg-orange-100 text-orange-700 text-sm rounded-full"


      150 |     
      151 |     // Badge should have mobile-appropriate text size
    > 152 |     await expect(eraBadge).toHaveClass(/text-xs/)
          |                            ^
      153 |     await expect(eraBadge).toHaveClass(/md:text-sm/)
      154 |     
      155 |     // Test desktop
        at E:\Augment Code Testing\sri-lanka-tourist-guide\tests\mobile-timeline.spec.ts:152:28

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results\mobile-timeline-Mobile-Tim-6f717-lay-era-badges-responsively-firefox\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results\mobile-timeline-Mobile-Tim-6f717-lay-era-badges-responsively-firefox\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\test-results\mobile-timeline-Mobile-Tim-6f717-lay-era-badges-responsively-firefox\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|mobile-timeline-Mobile-Tim-6f717-lay-era-badges-responsively-firefox\test-failed-1.png]]

[[ATTACHMENT|mobile-timeline-Mobile-Tim-6f717-lay-era-badges-responsively-firefox\video.webm]]

[[ATTACHMENT|mobile-timeline-Mobile-Tim-6f717-lay-era-badges-responsively-firefox\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Mobile Timeline Layout › should handle touch interactions on mobile" classname="mobile-timeline.spec.ts" time="10.873">
<failure message="mobile-timeline.spec.ts:162:3 should handle touch interactions on mobile" type="FAILURE">
<![CDATA[  [firefox] › mobile-timeline.spec.ts:162:3 › Mobile Timeline Layout › should handle touch interactions on mobile 

    Error: locator.tap: The page does not support tap. Use hasTouch context option to enable touch support.

      167 |     
      168 |     // Touch should not cause layout issues
    > 169 |     await firstTimelineItem.tap()
          |                             ^
      170 |     await page.waitForTimeout(500)
      171 |     
      172 |     // Item should still be visible and properly positioned
        at E:\Augment Code Testing\sri-lanka-tourist-guide\tests\mobile-timeline.spec.ts:169:29

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results\mobile-timeline-Mobile-Tim-eeae3-ouch-interactions-on-mobile-firefox\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results\mobile-timeline-Mobile-Tim-eeae3-ouch-interactions-on-mobile-firefox\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\test-results\mobile-timeline-Mobile-Tim-eeae3-ouch-interactions-on-mobile-firefox\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|mobile-timeline-Mobile-Tim-eeae3-ouch-interactions-on-mobile-firefox\test-failed-1.png]]

[[ATTACHMENT|mobile-timeline-Mobile-Tim-eeae3-ouch-interactions-on-mobile-firefox\video.webm]]

[[ATTACHMENT|mobile-timeline-Mobile-Tim-eeae3-ouch-interactions-on-mobile-firefox\error-context.md]]
]]>
</system-out>
</testcase>
</testsuite>
<testsuite name="mobile-timeline.spec.ts" timestamp="2025-06-24T10:31:01.818Z" hostname="webkit" tests="8" failures="7" skipped="0" time="55.15" errors="0">
<testcase name="Mobile Timeline Layout › should display timeline correctly on mobile viewport" classname="mobile-timeline.spec.ts" time="4.982">
<failure message="mobile-timeline.spec.ts:9:3 should display timeline correctly on mobile viewport" type="FAILURE">
<![CDATA[  [webkit] › mobile-timeline.spec.ts:9:3 › Mobile Timeline Layout › should display timeline correctly on mobile viewport 

    Error: expect.toBeVisible: Error: strict mode violation: locator('.absolute.left-6') resolved to 14 elements:
        1) <div class="absolute left-6 md:left-1/2 transform md:-translate-x-1/2 w-1 h-full">…</div> aka locator('.absolute.left-6').first()
        2) <div class="absolute left-6 md:relative md:left-auto z-10 transform -translate-x-1/2 md:transform-none">…</div> aka locator('.absolute.left-6.md\\:relative').first()
        3) <div class="absolute left-6 md:relative md:left-auto z-10 transform -translate-x-1/2 md:transform-none">…</div> aka locator('.flex.items-center.flex-row.md\\:flex-row-reverse > .absolute.left-6').first()
        4) <div class="absolute left-6 md:relative md:left-auto z-10 transform -translate-x-1/2 md:transform-none">…</div> aka locator('div:nth-child(3) > .absolute.left-6')
        5) <div class="absolute left-6 md:relative md:left-auto z-10 transform -translate-x-1/2 md:transform-none">…</div> aka locator('div:nth-child(4) > .absolute.left-6')
        6) <div class="absolute left-6 md:relative md:left-auto z-10 transform -translate-x-1/2 md:transform-none">…</div> aka locator('div:nth-child(5) > .absolute.left-6')
        7) <div class="absolute left-6 md:relative md:left-auto z-10 transform -translate-x-1/2 md:transform-none">…</div> aka locator('div:nth-child(6) > .absolute.left-6')
        8) <div class="absolute left-6 md:relative md:left-auto z-10 transform -translate-x-1/2 md:transform-none">…</div> aka locator('div:nth-child(7) > .absolute.left-6')
        9) <div class="absolute left-6 md:relative md:left-auto z-10 transform -translate-x-1/2 md:transform-none">…</div> aka locator('div:nth-child(8) > .absolute.left-6')
        10) <div class="absolute left-6 md:relative md:left-auto z-10 transform -translate-x-1/2 md:transform-none">…</div> aka locator('div:nth-child(9) > .absolute.left-6')
        ...

    Call log:
      - Expect "toBeVisible" with timeout 5000ms
      - waiting for locator('.absolute.left-6')


      16 |     // Check that timeline line is positioned on the left for mobile
      17 |     const timelineLine = page.locator('.absolute.left-6')
    > 18 |     await expect(timelineLine).toBeVisible()
         |                                ^
      19 |     
      20 |     // Check that timeline items are in single column layout
      21 |     const timelineItems = page.locator('.flex.items-center.flex-row')
        at E:\Augment Code Testing\sri-lanka-tourist-guide\tests\mobile-timeline.spec.ts:18:32

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results\mobile-timeline-Mobile-Tim-846c2-orrectly-on-mobile-viewport-webkit\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results\mobile-timeline-Mobile-Tim-846c2-orrectly-on-mobile-viewport-webkit\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\test-results\mobile-timeline-Mobile-Tim-846c2-orrectly-on-mobile-viewport-webkit\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|mobile-timeline-Mobile-Tim-846c2-orrectly-on-mobile-viewport-webkit\test-failed-1.png]]

[[ATTACHMENT|mobile-timeline-Mobile-Tim-846c2-orrectly-on-mobile-viewport-webkit\video.webm]]

[[ATTACHMENT|mobile-timeline-Mobile-Tim-846c2-orrectly-on-mobile-viewport-webkit\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Mobile Timeline Layout › should display timeline correctly on desktop viewport" classname="mobile-timeline.spec.ts" time="11.111">
<failure message="mobile-timeline.spec.ts:40:3 should display timeline correctly on desktop viewport" type="FAILURE">
<![CDATA[  [webkit] › mobile-timeline.spec.ts:40:3 › Mobile Timeline Layout › should display timeline correctly on desktop viewport 

    Error: Timed out 5000ms waiting for expect(locator).toHaveClass(expected)

    Locator: locator('.flex.items-center').first()
    Expected pattern: /md:flex-row/
    Received string:  "flex justify-between items-center py-4"
    Call log:
      - Expect "toHaveClass" with timeout 5000ms
      - waiting for locator('.flex.items-center').first()
        8 × locator resolved to <div class="flex justify-between items-center py-4">…</div>
          - unexpected value "flex justify-between items-center py-4"


      64 |     
      65 |     // First item should be flex-row, second should be flex-row-reverse on desktop
    > 66 |     await expect(firstItem).toHaveClass(/md:flex-row/)
         |                             ^
      67 |     await expect(secondItem).toHaveClass(/md:flex-row-reverse/)
      68 |   })
      69 |
        at E:\Augment Code Testing\sri-lanka-tourist-guide\tests\mobile-timeline.spec.ts:66:29

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results\mobile-timeline-Mobile-Tim-ddc63-rrectly-on-desktop-viewport-webkit\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results\mobile-timeline-Mobile-Tim-ddc63-rrectly-on-desktop-viewport-webkit\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\test-results\mobile-timeline-Mobile-Tim-ddc63-rrectly-on-desktop-viewport-webkit\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|mobile-timeline-Mobile-Tim-ddc63-rrectly-on-desktop-viewport-webkit\test-failed-1.png]]

[[ATTACHMENT|mobile-timeline-Mobile-Tim-ddc63-rrectly-on-desktop-viewport-webkit\video.webm]]

[[ATTACHMENT|mobile-timeline-Mobile-Tim-ddc63-rrectly-on-desktop-viewport-webkit\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Mobile Timeline Layout › should have responsive text sizes" classname="mobile-timeline.spec.ts" time="8.577">
<failure message="mobile-timeline.spec.ts:70:3 should have responsive text sizes" type="FAILURE">
<![CDATA[  [webkit] › mobile-timeline.spec.ts:70:3 › Mobile Timeline Layout › should have responsive text sizes 

    Error: Timed out 5000ms waiting for expect(locator).toHaveClass(expected)

    Locator: locator('h3').first()
    Expected pattern: /text-lg/
    Received string:  "text-xl font-semibold mb-3 text-gray-900"
    Call log:
      - Expect "toHaveClass" with timeout 5000ms
      - waiting for locator('h3').first()
        4 × locator resolved to <h3 class="text-2xl font-bold text-gradient mb-4">Sri Lanka Guide</h3>
          - unexpected value "text-2xl font-bold text-gradient mb-4"
        4 × locator resolved to <h3 class="text-xl font-semibold mb-3 text-gray-900">Buddhism & Spirituality</h3>
          - unexpected value "text-xl font-semibold mb-3 text-gray-900"


      73 |     
      74 |     const title = page.locator('h3').first()
    > 75 |     await expect(title).toHaveClass(/text-lg/)
         |                         ^
      76 |     await expect(title).toHaveClass(/md:text-2xl/)
      77 |     
      78 |     const description = page.locator('.timeline-content p').first()
        at E:\Augment Code Testing\sri-lanka-tourist-guide\tests\mobile-timeline.spec.ts:75:25

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results\mobile-timeline-Mobile-Tim-07475--have-responsive-text-sizes-webkit\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results\mobile-timeline-Mobile-Tim-07475--have-responsive-text-sizes-webkit\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\test-results\mobile-timeline-Mobile-Tim-07475--have-responsive-text-sizes-webkit\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|mobile-timeline-Mobile-Tim-07475--have-responsive-text-sizes-webkit\test-failed-1.png]]

[[ATTACHMENT|mobile-timeline-Mobile-Tim-07475--have-responsive-text-sizes-webkit\video.webm]]

[[ATTACHMENT|mobile-timeline-Mobile-Tim-07475--have-responsive-text-sizes-webkit\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Mobile Timeline Layout › should handle scroll-triggered animations on mobile" classname="mobile-timeline.spec.ts" time="4.846">
</testcase>
<testcase name="Mobile Timeline Layout › should maintain accessibility on mobile" classname="mobile-timeline.spec.ts" time="4.39">
<failure message="mobile-timeline.spec.ts:101:3 should maintain accessibility on mobile" type="FAILURE">
<![CDATA[  [webkit] › mobile-timeline.spec.ts:101:3 › Mobile Timeline Layout › should maintain accessibility on mobile 

    Error: expect(received).toBeGreaterThanOrEqual(expected)

    Expected: >= 16
    Received:    14.39999771118164

      119 |     if (boundingBox) {
      120 |       // On mobile, dots should be at least 16px (w-4 h-4)
    > 121 |       expect(boundingBox.width).toBeGreaterThanOrEqual(16)
          |                                 ^
      122 |       expect(boundingBox.height).toBeGreaterThanOrEqual(16)
      123 |     }
      124 |   })
        at E:\Augment Code Testing\sri-lanka-tourist-guide\tests\mobile-timeline.spec.ts:121:33

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results\mobile-timeline-Mobile-Tim-6fecf-ain-accessibility-on-mobile-webkit\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results\mobile-timeline-Mobile-Tim-6fecf-ain-accessibility-on-mobile-webkit\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\test-results\mobile-timeline-Mobile-Tim-6fecf-ain-accessibility-on-mobile-webkit\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|mobile-timeline-Mobile-Tim-6fecf-ain-accessibility-on-mobile-webkit\test-failed-1.png]]

[[ATTACHMENT|mobile-timeline-Mobile-Tim-6fecf-ain-accessibility-on-mobile-webkit\video.webm]]

[[ATTACHMENT|mobile-timeline-Mobile-Tim-6fecf-ain-accessibility-on-mobile-webkit\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Mobile Timeline Layout › should handle content overflow properly on mobile" classname="mobile-timeline.spec.ts" time="9.129">
<failure message="mobile-timeline.spec.ts:126:3 should handle content overflow properly on mobile" type="FAILURE">
<![CDATA[  [webkit] › mobile-timeline.spec.ts:126:3 › Mobile Timeline Layout › should handle content overflow properly on mobile 

    Error: Timed out 5000ms waiting for expect(locator).toHaveCSS(expected)

    Locator: locator('.timeline-content p').first()
    Expected string: "break-word"
    Received string: "normal"
    Call log:
      - Expect "toHaveCSS" with timeout 5000ms
      - waiting for locator('.timeline-content p').first()
        8 × locator resolved to <p class="text-sm md:text-base text-gray-600 mb-4 md:mb-6 leading-relaxed">The earliest human settlements in Sri Lanka, span…</p>
          - unexpected value "normal"


      139 |     const longTexts = page.locator('.timeline-content p')
      140 |     const firstText = longTexts.first()
    > 141 |     await expect(firstText).toHaveCSS('word-wrap', 'break-word')
          |                             ^
      142 |   })
      143 |
      144 |   test('should display era badges responsively', async ({ page }) => {
        at E:\Augment Code Testing\sri-lanka-tourist-guide\tests\mobile-timeline.spec.ts:141:29

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results\mobile-timeline-Mobile-Tim-86076-overflow-properly-on-mobile-webkit\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results\mobile-timeline-Mobile-Tim-86076-overflow-properly-on-mobile-webkit\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\test-results\mobile-timeline-Mobile-Tim-86076-overflow-properly-on-mobile-webkit\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|mobile-timeline-Mobile-Tim-86076-overflow-properly-on-mobile-webkit\test-failed-1.png]]

[[ATTACHMENT|mobile-timeline-Mobile-Tim-86076-overflow-properly-on-mobile-webkit\video.webm]]

[[ATTACHMENT|mobile-timeline-Mobile-Tim-86076-overflow-properly-on-mobile-webkit\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Mobile Timeline Layout › should display era badges responsively" classname="mobile-timeline.spec.ts" time="9.257">
<failure message="mobile-timeline.spec.ts:144:3 should display era badges responsively" type="FAILURE">
<![CDATA[  [webkit] › mobile-timeline.spec.ts:144:3 › Mobile Timeline Layout › should display era badges responsively 

    Error: Timed out 5000ms waiting for expect(locator).toHaveClass(expected)

    Locator: locator('.rounded-full').first()
    Expected pattern: /text-xs/
    Received string:  "inline-block px-3 py-1 bg-orange-100 text-orange-700 text-sm rounded-full"
    Call log:
      - Expect "toHaveClass" with timeout 5000ms
      - waiting for locator('.rounded-full').first()
        8 × locator resolved to <span class="inline-block px-3 py-1 bg-orange-100 text-orange-700 text-sm rounded-full">Sacred Tooth Relic</span>
          - unexpected value "inline-block px-3 py-1 bg-orange-100 text-orange-700 text-sm rounded-full"


      150 |     
      151 |     // Badge should have mobile-appropriate text size
    > 152 |     await expect(eraBadge).toHaveClass(/text-xs/)
          |                            ^
      153 |     await expect(eraBadge).toHaveClass(/md:text-sm/)
      154 |     
      155 |     // Test desktop
        at E:\Augment Code Testing\sri-lanka-tourist-guide\tests\mobile-timeline.spec.ts:152:28

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results\mobile-timeline-Mobile-Tim-6f717-lay-era-badges-responsively-webkit\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results\mobile-timeline-Mobile-Tim-6f717-lay-era-badges-responsively-webkit\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\test-results\mobile-timeline-Mobile-Tim-6f717-lay-era-badges-responsively-webkit\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|mobile-timeline-Mobile-Tim-6f717-lay-era-badges-responsively-webkit\test-failed-1.png]]

[[ATTACHMENT|mobile-timeline-Mobile-Tim-6f717-lay-era-badges-responsively-webkit\video.webm]]

[[ATTACHMENT|mobile-timeline-Mobile-Tim-6f717-lay-era-badges-responsively-webkit\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Mobile Timeline Layout › should handle touch interactions on mobile" classname="mobile-timeline.spec.ts" time="2.858">
<failure message="mobile-timeline.spec.ts:162:3 should handle touch interactions on mobile" type="FAILURE">
<![CDATA[  [webkit] › mobile-timeline.spec.ts:162:3 › Mobile Timeline Layout › should handle touch interactions on mobile 

    Error: locator.tap: The page does not support tap. Use hasTouch context option to enable touch support.

      167 |     
      168 |     // Touch should not cause layout issues
    > 169 |     await firstTimelineItem.tap()
          |                             ^
      170 |     await page.waitForTimeout(500)
      171 |     
      172 |     // Item should still be visible and properly positioned
        at E:\Augment Code Testing\sri-lanka-tourist-guide\tests\mobile-timeline.spec.ts:169:29

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results\mobile-timeline-Mobile-Tim-eeae3-ouch-interactions-on-mobile-webkit\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results\mobile-timeline-Mobile-Tim-eeae3-ouch-interactions-on-mobile-webkit\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\test-results\mobile-timeline-Mobile-Tim-eeae3-ouch-interactions-on-mobile-webkit\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|mobile-timeline-Mobile-Tim-eeae3-ouch-interactions-on-mobile-webkit\test-failed-1.png]]

[[ATTACHMENT|mobile-timeline-Mobile-Tim-eeae3-ouch-interactions-on-mobile-webkit\video.webm]]

[[ATTACHMENT|mobile-timeline-Mobile-Tim-eeae3-ouch-interactions-on-mobile-webkit\error-context.md]]
]]>
</system-out>
</testcase>
</testsuite>
<testsuite name="mobile-timeline.spec.ts" timestamp="2025-06-24T10:31:01.818Z" hostname="Mobile Chrome" tests="8" failures="6" skipped="0" time="56.271" errors="0">
<testcase name="Mobile Timeline Layout › should display timeline correctly on mobile viewport" classname="mobile-timeline.spec.ts" time="5.224">
<failure message="mobile-timeline.spec.ts:9:3 should display timeline correctly on mobile viewport" type="FAILURE">
<![CDATA[  [Mobile Chrome] › mobile-timeline.spec.ts:9:3 › Mobile Timeline Layout › should display timeline correctly on mobile viewport 

    Error: expect.toBeVisible: Error: strict mode violation: locator('.absolute.left-6') resolved to 14 elements:
        1) <div class="absolute left-6 md:left-1/2 transform md:-translate-x-1/2 w-1 h-full">…</div> aka locator('.absolute.left-6').first()
        2) <div class="absolute left-6 md:relative md:left-auto z-10 transform -translate-x-1/2 md:transform-none">…</div> aka locator('.absolute.left-6.md\\:relative').first()
        3) <div class="absolute left-6 md:relative md:left-auto z-10 transform -translate-x-1/2 md:transform-none">…</div> aka locator('.flex.items-center.flex-row.md\\:flex-row-reverse > .absolute.left-6').first()
        4) <div class="absolute left-6 md:relative md:left-auto z-10 transform -translate-x-1/2 md:transform-none">…</div> aka locator('div:nth-child(3) > .absolute.left-6')
        5) <div class="absolute left-6 md:relative md:left-auto z-10 transform -translate-x-1/2 md:transform-none">…</div> aka locator('div:nth-child(4) > .absolute.left-6')
        6) <div class="absolute left-6 md:relative md:left-auto z-10 transform -translate-x-1/2 md:transform-none">…</div> aka locator('div:nth-child(5) > .absolute.left-6')
        7) <div class="absolute left-6 md:relative md:left-auto z-10 transform -translate-x-1/2 md:transform-none">…</div> aka locator('div:nth-child(6) > .absolute.left-6')
        8) <div class="absolute left-6 md:relative md:left-auto z-10 transform -translate-x-1/2 md:transform-none">…</div> aka locator('div:nth-child(7) > .absolute.left-6')
        9) <div class="absolute left-6 md:relative md:left-auto z-10 transform -translate-x-1/2 md:transform-none">…</div> aka locator('div:nth-child(8) > .absolute.left-6')
        10) <div class="absolute left-6 md:relative md:left-auto z-10 transform -translate-x-1/2 md:transform-none">…</div> aka locator('div:nth-child(9) > .absolute.left-6')
        ...

    Call log:
      - Expect "toBeVisible" with timeout 5000ms
      - waiting for locator('.absolute.left-6')


      16 |     // Check that timeline line is positioned on the left for mobile
      17 |     const timelineLine = page.locator('.absolute.left-6')
    > 18 |     await expect(timelineLine).toBeVisible()
         |                                ^
      19 |     
      20 |     // Check that timeline items are in single column layout
      21 |     const timelineItems = page.locator('.flex.items-center.flex-row')
        at E:\Augment Code Testing\sri-lanka-tourist-guide\tests\mobile-timeline.spec.ts:18:32

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results\mobile-timeline-Mobile-Tim-846c2-orrectly-on-mobile-viewport-Mobile-Chrome\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results\mobile-timeline-Mobile-Tim-846c2-orrectly-on-mobile-viewport-Mobile-Chrome\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\test-results\mobile-timeline-Mobile-Tim-846c2-orrectly-on-mobile-viewport-Mobile-Chrome\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|mobile-timeline-Mobile-Tim-846c2-orrectly-on-mobile-viewport-Mobile-Chrome\test-failed-1.png]]

[[ATTACHMENT|mobile-timeline-Mobile-Tim-846c2-orrectly-on-mobile-viewport-Mobile-Chrome\video.webm]]

[[ATTACHMENT|mobile-timeline-Mobile-Tim-846c2-orrectly-on-mobile-viewport-Mobile-Chrome\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Mobile Timeline Layout › should display timeline correctly on desktop viewport" classname="mobile-timeline.spec.ts" time="9.501">
<failure message="mobile-timeline.spec.ts:40:3 should display timeline correctly on desktop viewport" type="FAILURE">
<![CDATA[  [Mobile Chrome] › mobile-timeline.spec.ts:40:3 › Mobile Timeline Layout › should display timeline correctly on desktop viewport 

    Error: Timed out 5000ms waiting for expect(locator).toHaveClass(expected)

    Locator: locator('.flex.items-center').first()
    Expected pattern: /md:flex-row/
    Received string:  "flex justify-between items-center py-4"
    Call log:
      - Expect "toHaveClass" with timeout 5000ms
      - waiting for locator('.flex.items-center').first()
        9 × locator resolved to <div class="flex justify-between items-center py-4">…</div>
          - unexpected value "flex justify-between items-center py-4"


      64 |     
      65 |     // First item should be flex-row, second should be flex-row-reverse on desktop
    > 66 |     await expect(firstItem).toHaveClass(/md:flex-row/)
         |                             ^
      67 |     await expect(secondItem).toHaveClass(/md:flex-row-reverse/)
      68 |   })
      69 |
        at E:\Augment Code Testing\sri-lanka-tourist-guide\tests\mobile-timeline.spec.ts:66:29

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results\mobile-timeline-Mobile-Tim-ddc63-rrectly-on-desktop-viewport-Mobile-Chrome\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results\mobile-timeline-Mobile-Tim-ddc63-rrectly-on-desktop-viewport-Mobile-Chrome\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\test-results\mobile-timeline-Mobile-Tim-ddc63-rrectly-on-desktop-viewport-Mobile-Chrome\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|mobile-timeline-Mobile-Tim-ddc63-rrectly-on-desktop-viewport-Mobile-Chrome\test-failed-1.png]]

[[ATTACHMENT|mobile-timeline-Mobile-Tim-ddc63-rrectly-on-desktop-viewport-Mobile-Chrome\video.webm]]

[[ATTACHMENT|mobile-timeline-Mobile-Tim-ddc63-rrectly-on-desktop-viewport-Mobile-Chrome\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Mobile Timeline Layout › should have responsive text sizes" classname="mobile-timeline.spec.ts" time="9.925">
<failure message="mobile-timeline.spec.ts:70:3 should have responsive text sizes" type="FAILURE">
<![CDATA[  [Mobile Chrome] › mobile-timeline.spec.ts:70:3 › Mobile Timeline Layout › should have responsive text sizes 

    Error: Timed out 5000ms waiting for expect(locator).toHaveClass(expected)

    Locator: locator('h3').first()
    Expected pattern: /text-lg/
    Received string:  "text-xl font-semibold mb-3 text-gray-900"
    Call log:
      - Expect "toHaveClass" with timeout 5000ms
      - waiting for locator('h3').first()
        8 × locator resolved to <h3 class="text-xl font-semibold mb-3 text-gray-900">Buddhism & Spirituality</h3>
          - unexpected value "text-xl font-semibold mb-3 text-gray-900"


      73 |     
      74 |     const title = page.locator('h3').first()
    > 75 |     await expect(title).toHaveClass(/text-lg/)
         |                         ^
      76 |     await expect(title).toHaveClass(/md:text-2xl/)
      77 |     
      78 |     const description = page.locator('.timeline-content p').first()
        at E:\Augment Code Testing\sri-lanka-tourist-guide\tests\mobile-timeline.spec.ts:75:25

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results\mobile-timeline-Mobile-Tim-07475--have-responsive-text-sizes-Mobile-Chrome\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results\mobile-timeline-Mobile-Tim-07475--have-responsive-text-sizes-Mobile-Chrome\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\test-results\mobile-timeline-Mobile-Tim-07475--have-responsive-text-sizes-Mobile-Chrome\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|mobile-timeline-Mobile-Tim-07475--have-responsive-text-sizes-Mobile-Chrome\test-failed-1.png]]

[[ATTACHMENT|mobile-timeline-Mobile-Tim-07475--have-responsive-text-sizes-Mobile-Chrome\video.webm]]

[[ATTACHMENT|mobile-timeline-Mobile-Tim-07475--have-responsive-text-sizes-Mobile-Chrome\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Mobile Timeline Layout › should handle scroll-triggered animations on mobile" classname="mobile-timeline.spec.ts" time="4.122">
</testcase>
<testcase name="Mobile Timeline Layout › should maintain accessibility on mobile" classname="mobile-timeline.spec.ts" time="3.489">
<failure message="mobile-timeline.spec.ts:101:3 should maintain accessibility on mobile" type="FAILURE">
<![CDATA[  [Mobile Chrome] › mobile-timeline.spec.ts:101:3 › Mobile Timeline Layout › should maintain accessibility on mobile 

    Error: expect(received).toBeGreaterThanOrEqual(expected)

    Expected: >= 16
    Received:    14.39999771118164

      119 |     if (boundingBox) {
      120 |       // On mobile, dots should be at least 16px (w-4 h-4)
    > 121 |       expect(boundingBox.width).toBeGreaterThanOrEqual(16)
          |                                 ^
      122 |       expect(boundingBox.height).toBeGreaterThanOrEqual(16)
      123 |     }
      124 |   })
        at E:\Augment Code Testing\sri-lanka-tourist-guide\tests\mobile-timeline.spec.ts:121:33

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results\mobile-timeline-Mobile-Tim-6fecf-ain-accessibility-on-mobile-Mobile-Chrome\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results\mobile-timeline-Mobile-Tim-6fecf-ain-accessibility-on-mobile-Mobile-Chrome\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\test-results\mobile-timeline-Mobile-Tim-6fecf-ain-accessibility-on-mobile-Mobile-Chrome\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|mobile-timeline-Mobile-Tim-6fecf-ain-accessibility-on-mobile-Mobile-Chrome\test-failed-1.png]]

[[ATTACHMENT|mobile-timeline-Mobile-Tim-6fecf-ain-accessibility-on-mobile-Mobile-Chrome\video.webm]]

[[ATTACHMENT|mobile-timeline-Mobile-Tim-6fecf-ain-accessibility-on-mobile-Mobile-Chrome\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Mobile Timeline Layout › should handle content overflow properly on mobile" classname="mobile-timeline.spec.ts" time="8.295">
<failure message="mobile-timeline.spec.ts:126:3 should handle content overflow properly on mobile" type="FAILURE">
<![CDATA[  [Mobile Chrome] › mobile-timeline.spec.ts:126:3 › Mobile Timeline Layout › should handle content overflow properly on mobile 

    Error: Timed out 5000ms waiting for expect(locator).toHaveCSS(expected)

    Locator: locator('.timeline-content p').first()
    Expected string: "break-word"
    Received string: "normal"
    Call log:
      - Expect "toHaveCSS" with timeout 5000ms
      - waiting for locator('.timeline-content p').first()
        8 × locator resolved to <p class="text-sm md:text-base text-gray-600 mb-4 md:mb-6 leading-relaxed">The earliest human settlements in Sri Lanka, span…</p>
          - unexpected value "normal"


      139 |     const longTexts = page.locator('.timeline-content p')
      140 |     const firstText = longTexts.first()
    > 141 |     await expect(firstText).toHaveCSS('word-wrap', 'break-word')
          |                             ^
      142 |   })
      143 |
      144 |   test('should display era badges responsively', async ({ page }) => {
        at E:\Augment Code Testing\sri-lanka-tourist-guide\tests\mobile-timeline.spec.ts:141:29

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results\mobile-timeline-Mobile-Tim-86076-overflow-properly-on-mobile-Mobile-Chrome\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results\mobile-timeline-Mobile-Tim-86076-overflow-properly-on-mobile-Mobile-Chrome\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\test-results\mobile-timeline-Mobile-Tim-86076-overflow-properly-on-mobile-Mobile-Chrome\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|mobile-timeline-Mobile-Tim-86076-overflow-properly-on-mobile-Mobile-Chrome\test-failed-1.png]]

[[ATTACHMENT|mobile-timeline-Mobile-Tim-86076-overflow-properly-on-mobile-Mobile-Chrome\video.webm]]

[[ATTACHMENT|mobile-timeline-Mobile-Tim-86076-overflow-properly-on-mobile-Mobile-Chrome\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Mobile Timeline Layout › should display era badges responsively" classname="mobile-timeline.spec.ts" time="10.375">
<failure message="mobile-timeline.spec.ts:144:3 should display era badges responsively" type="FAILURE">
<![CDATA[  [Mobile Chrome] › mobile-timeline.spec.ts:144:3 › Mobile Timeline Layout › should display era badges responsively 

    Error: Timed out 5000ms waiting for expect(locator).toHaveClass(expected)

    Locator: locator('.rounded-full').first()
    Expected pattern: /text-xs/
    Received string:  "inline-block px-3 py-1 bg-orange-100 text-orange-700 text-sm rounded-full"
    Call log:
      - Expect "toHaveClass" with timeout 5000ms
      - waiting for locator('.rounded-full').first()
        8 × locator resolved to <span class="inline-block px-3 py-1 bg-orange-100 text-orange-700 text-sm rounded-full">Sacred Tooth Relic</span>
          - unexpected value "inline-block px-3 py-1 bg-orange-100 text-orange-700 text-sm rounded-full"


      150 |     
      151 |     // Badge should have mobile-appropriate text size
    > 152 |     await expect(eraBadge).toHaveClass(/text-xs/)
          |                            ^
      153 |     await expect(eraBadge).toHaveClass(/md:text-sm/)
      154 |     
      155 |     // Test desktop
        at E:\Augment Code Testing\sri-lanka-tourist-guide\tests\mobile-timeline.spec.ts:152:28

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results\mobile-timeline-Mobile-Tim-6f717-lay-era-badges-responsively-Mobile-Chrome\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results\mobile-timeline-Mobile-Tim-6f717-lay-era-badges-responsively-Mobile-Chrome\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\test-results\mobile-timeline-Mobile-Tim-6f717-lay-era-badges-responsively-Mobile-Chrome\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|mobile-timeline-Mobile-Tim-6f717-lay-era-badges-responsively-Mobile-Chrome\test-failed-1.png]]

[[ATTACHMENT|mobile-timeline-Mobile-Tim-6f717-lay-era-badges-responsively-Mobile-Chrome\video.webm]]

[[ATTACHMENT|mobile-timeline-Mobile-Tim-6f717-lay-era-badges-responsively-Mobile-Chrome\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Mobile Timeline Layout › should handle touch interactions on mobile" classname="mobile-timeline.spec.ts" time="5.34">
</testcase>
</testsuite>
<testsuite name="mobile-timeline.spec.ts" timestamp="2025-06-24T10:31:01.818Z" hostname="Mobile Safari" tests="8" failures="6" skipped="0" time="61.129" errors="0">
<testcase name="Mobile Timeline Layout › should display timeline correctly on mobile viewport" classname="mobile-timeline.spec.ts" time="4.442">
<failure message="mobile-timeline.spec.ts:9:3 should display timeline correctly on mobile viewport" type="FAILURE">
<![CDATA[  [Mobile Safari] › mobile-timeline.spec.ts:9:3 › Mobile Timeline Layout › should display timeline correctly on mobile viewport 

    Error: expect.toBeVisible: Error: strict mode violation: locator('.absolute.left-6') resolved to 14 elements:
        1) <div class="absolute left-6 md:left-1/2 transform md:-translate-x-1/2 w-1 h-full">…</div> aka locator('.absolute.left-6').first()
        2) <div class="absolute left-6 md:relative md:left-auto z-10 transform -translate-x-1/2 md:transform-none">…</div> aka locator('.absolute.left-6.md\\:relative').first()
        3) <div class="absolute left-6 md:relative md:left-auto z-10 transform -translate-x-1/2 md:transform-none">…</div> aka locator('.flex.items-center.flex-row.md\\:flex-row-reverse > .absolute.left-6').first()
        4) <div class="absolute left-6 md:relative md:left-auto z-10 transform -translate-x-1/2 md:transform-none">…</div> aka locator('div:nth-child(3) > .absolute.left-6')
        5) <div class="absolute left-6 md:relative md:left-auto z-10 transform -translate-x-1/2 md:transform-none">…</div> aka locator('div:nth-child(4) > .absolute.left-6')
        6) <div class="absolute left-6 md:relative md:left-auto z-10 transform -translate-x-1/2 md:transform-none">…</div> aka locator('div:nth-child(5) > .absolute.left-6')
        7) <div class="absolute left-6 md:relative md:left-auto z-10 transform -translate-x-1/2 md:transform-none">…</div> aka locator('div:nth-child(6) > .absolute.left-6')
        8) <div class="absolute left-6 md:relative md:left-auto z-10 transform -translate-x-1/2 md:transform-none">…</div> aka locator('div:nth-child(7) > .absolute.left-6')
        9) <div class="absolute left-6 md:relative md:left-auto z-10 transform -translate-x-1/2 md:transform-none">…</div> aka locator('div:nth-child(8) > .absolute.left-6')
        10) <div class="absolute left-6 md:relative md:left-auto z-10 transform -translate-x-1/2 md:transform-none">…</div> aka locator('div:nth-child(9) > .absolute.left-6')
        ...

    Call log:
      - Expect "toBeVisible" with timeout 5000ms
      - waiting for locator('.absolute.left-6')


      16 |     // Check that timeline line is positioned on the left for mobile
      17 |     const timelineLine = page.locator('.absolute.left-6')
    > 18 |     await expect(timelineLine).toBeVisible()
         |                                ^
      19 |     
      20 |     // Check that timeline items are in single column layout
      21 |     const timelineItems = page.locator('.flex.items-center.flex-row')
        at E:\Augment Code Testing\sri-lanka-tourist-guide\tests\mobile-timeline.spec.ts:18:32

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results\mobile-timeline-Mobile-Tim-846c2-orrectly-on-mobile-viewport-Mobile-Safari\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results\mobile-timeline-Mobile-Tim-846c2-orrectly-on-mobile-viewport-Mobile-Safari\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\test-results\mobile-timeline-Mobile-Tim-846c2-orrectly-on-mobile-viewport-Mobile-Safari\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|mobile-timeline-Mobile-Tim-846c2-orrectly-on-mobile-viewport-Mobile-Safari\test-failed-1.png]]

[[ATTACHMENT|mobile-timeline-Mobile-Tim-846c2-orrectly-on-mobile-viewport-Mobile-Safari\video.webm]]

[[ATTACHMENT|mobile-timeline-Mobile-Tim-846c2-orrectly-on-mobile-viewport-Mobile-Safari\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Mobile Timeline Layout › should display timeline correctly on desktop viewport" classname="mobile-timeline.spec.ts" time="10.023">
<failure message="mobile-timeline.spec.ts:40:3 should display timeline correctly on desktop viewport" type="FAILURE">
<![CDATA[  [Mobile Safari] › mobile-timeline.spec.ts:40:3 › Mobile Timeline Layout › should display timeline correctly on desktop viewport 

    Error: Timed out 5000ms waiting for expect(locator).toHaveClass(expected)

    Locator: locator('.flex.items-center').first()
    Expected pattern: /md:flex-row/
    Received string:  "flex justify-between items-center py-4"
    Call log:
      - Expect "toHaveClass" with timeout 5000ms
      - waiting for locator('.flex.items-center').first()
        9 × locator resolved to <div class="flex justify-between items-center py-4">…</div>
          - unexpected value "flex justify-between items-center py-4"


      64 |     
      65 |     // First item should be flex-row, second should be flex-row-reverse on desktop
    > 66 |     await expect(firstItem).toHaveClass(/md:flex-row/)
         |                             ^
      67 |     await expect(secondItem).toHaveClass(/md:flex-row-reverse/)
      68 |   })
      69 |
        at E:\Augment Code Testing\sri-lanka-tourist-guide\tests\mobile-timeline.spec.ts:66:29

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results\mobile-timeline-Mobile-Tim-ddc63-rrectly-on-desktop-viewport-Mobile-Safari\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results\mobile-timeline-Mobile-Tim-ddc63-rrectly-on-desktop-viewport-Mobile-Safari\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\test-results\mobile-timeline-Mobile-Tim-ddc63-rrectly-on-desktop-viewport-Mobile-Safari\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|mobile-timeline-Mobile-Tim-ddc63-rrectly-on-desktop-viewport-Mobile-Safari\test-failed-1.png]]

[[ATTACHMENT|mobile-timeline-Mobile-Tim-ddc63-rrectly-on-desktop-viewport-Mobile-Safari\video.webm]]

[[ATTACHMENT|mobile-timeline-Mobile-Tim-ddc63-rrectly-on-desktop-viewport-Mobile-Safari\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Mobile Timeline Layout › should have responsive text sizes" classname="mobile-timeline.spec.ts" time="8.86">
<failure message="mobile-timeline.spec.ts:70:3 should have responsive text sizes" type="FAILURE">
<![CDATA[  [Mobile Safari] › mobile-timeline.spec.ts:70:3 › Mobile Timeline Layout › should have responsive text sizes 

    Error: Timed out 5000ms waiting for expect(locator).toHaveClass(expected)

    Locator: locator('h3').first()
    Expected pattern: /text-lg/
    Received string:  "text-xl font-semibold mb-3 text-gray-900"
    Call log:
      - Expect "toHaveClass" with timeout 5000ms
      - waiting for locator('h3').first()
        8 × locator resolved to <h3 class="text-xl font-semibold mb-3 text-gray-900">Buddhism & Spirituality</h3>
          - unexpected value "text-xl font-semibold mb-3 text-gray-900"


      73 |     
      74 |     const title = page.locator('h3').first()
    > 75 |     await expect(title).toHaveClass(/text-lg/)
         |                         ^
      76 |     await expect(title).toHaveClass(/md:text-2xl/)
      77 |     
      78 |     const description = page.locator('.timeline-content p').first()
        at E:\Augment Code Testing\sri-lanka-tourist-guide\tests\mobile-timeline.spec.ts:75:25

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results\mobile-timeline-Mobile-Tim-07475--have-responsive-text-sizes-Mobile-Safari\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results\mobile-timeline-Mobile-Tim-07475--have-responsive-text-sizes-Mobile-Safari\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\test-results\mobile-timeline-Mobile-Tim-07475--have-responsive-text-sizes-Mobile-Safari\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|mobile-timeline-Mobile-Tim-07475--have-responsive-text-sizes-Mobile-Safari\test-failed-1.png]]

[[ATTACHMENT|mobile-timeline-Mobile-Tim-07475--have-responsive-text-sizes-Mobile-Safari\video.webm]]

[[ATTACHMENT|mobile-timeline-Mobile-Tim-07475--have-responsive-text-sizes-Mobile-Safari\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Mobile Timeline Layout › should handle scroll-triggered animations on mobile" classname="mobile-timeline.spec.ts" time="7.916">
</testcase>
<testcase name="Mobile Timeline Layout › should maintain accessibility on mobile" classname="mobile-timeline.spec.ts" time="5.978">
<failure message="mobile-timeline.spec.ts:101:3 should maintain accessibility on mobile" type="FAILURE">
<![CDATA[  [Mobile Safari] › mobile-timeline.spec.ts:101:3 › Mobile Timeline Layout › should maintain accessibility on mobile 

    Error: expect(received).toBeGreaterThanOrEqual(expected)

    Expected: >= 16
    Received:    14.400001525878906

      119 |     if (boundingBox) {
      120 |       // On mobile, dots should be at least 16px (w-4 h-4)
    > 121 |       expect(boundingBox.width).toBeGreaterThanOrEqual(16)
          |                                 ^
      122 |       expect(boundingBox.height).toBeGreaterThanOrEqual(16)
      123 |     }
      124 |   })
        at E:\Augment Code Testing\sri-lanka-tourist-guide\tests\mobile-timeline.spec.ts:121:33

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results\mobile-timeline-Mobile-Tim-6fecf-ain-accessibility-on-mobile-Mobile-Safari\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results\mobile-timeline-Mobile-Tim-6fecf-ain-accessibility-on-mobile-Mobile-Safari\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\test-results\mobile-timeline-Mobile-Tim-6fecf-ain-accessibility-on-mobile-Mobile-Safari\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|mobile-timeline-Mobile-Tim-6fecf-ain-accessibility-on-mobile-Mobile-Safari\test-failed-1.png]]

[[ATTACHMENT|mobile-timeline-Mobile-Tim-6fecf-ain-accessibility-on-mobile-Mobile-Safari\video.webm]]

[[ATTACHMENT|mobile-timeline-Mobile-Tim-6fecf-ain-accessibility-on-mobile-Mobile-Safari\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Mobile Timeline Layout › should handle content overflow properly on mobile" classname="mobile-timeline.spec.ts" time="11.664">
<failure message="mobile-timeline.spec.ts:126:3 should handle content overflow properly on mobile" type="FAILURE">
<![CDATA[  [Mobile Safari] › mobile-timeline.spec.ts:126:3 › Mobile Timeline Layout › should handle content overflow properly on mobile 

    Error: Timed out 5000ms waiting for expect(locator).toHaveCSS(expected)

    Locator: locator('.timeline-content p').first()
    Expected string: "break-word"
    Received string: "normal"
    Call log:
      - Expect "toHaveCSS" with timeout 5000ms
      - waiting for locator('.timeline-content p').first()
        8 × locator resolved to <p class="text-sm md:text-base text-gray-600 mb-4 md:mb-6 leading-relaxed">The earliest human settlements in Sri Lanka, span…</p>
          - unexpected value "normal"


      139 |     const longTexts = page.locator('.timeline-content p')
      140 |     const firstText = longTexts.first()
    > 141 |     await expect(firstText).toHaveCSS('word-wrap', 'break-word')
          |                             ^
      142 |   })
      143 |
      144 |   test('should display era badges responsively', async ({ page }) => {
        at E:\Augment Code Testing\sri-lanka-tourist-guide\tests\mobile-timeline.spec.ts:141:29

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results\mobile-timeline-Mobile-Tim-86076-overflow-properly-on-mobile-Mobile-Safari\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results\mobile-timeline-Mobile-Tim-86076-overflow-properly-on-mobile-Mobile-Safari\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\test-results\mobile-timeline-Mobile-Tim-86076-overflow-properly-on-mobile-Mobile-Safari\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|mobile-timeline-Mobile-Tim-86076-overflow-properly-on-mobile-Mobile-Safari\test-failed-1.png]]

[[ATTACHMENT|mobile-timeline-Mobile-Tim-86076-overflow-properly-on-mobile-Mobile-Safari\video.webm]]

[[ATTACHMENT|mobile-timeline-Mobile-Tim-86076-overflow-properly-on-mobile-Mobile-Safari\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Mobile Timeline Layout › should display era badges responsively" classname="mobile-timeline.spec.ts" time="7.448">
<failure message="mobile-timeline.spec.ts:144:3 should display era badges responsively" type="FAILURE">
<![CDATA[  [Mobile Safari] › mobile-timeline.spec.ts:144:3 › Mobile Timeline Layout › should display era badges responsively 

    Error: Timed out 5000ms waiting for expect(locator).toHaveClass(expected)

    Locator: locator('.rounded-full').first()
    Expected pattern: /text-xs/
    Received string:  "inline-block px-3 py-1 bg-orange-100 text-orange-700 text-sm rounded-full"
    Call log:
      - Expect "toHaveClass" with timeout 5000ms
      - waiting for locator('.rounded-full').first()
        8 × locator resolved to <span class="inline-block px-3 py-1 bg-orange-100 text-orange-700 text-sm rounded-full">Sacred Tooth Relic</span>
          - unexpected value "inline-block px-3 py-1 bg-orange-100 text-orange-700 text-sm rounded-full"


      150 |     
      151 |     // Badge should have mobile-appropriate text size
    > 152 |     await expect(eraBadge).toHaveClass(/text-xs/)
          |                            ^
      153 |     await expect(eraBadge).toHaveClass(/md:text-sm/)
      154 |     
      155 |     // Test desktop
        at E:\Augment Code Testing\sri-lanka-tourist-guide\tests\mobile-timeline.spec.ts:152:28

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results\mobile-timeline-Mobile-Tim-6f717-lay-era-badges-responsively-Mobile-Safari\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results\mobile-timeline-Mobile-Tim-6f717-lay-era-badges-responsively-Mobile-Safari\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\test-results\mobile-timeline-Mobile-Tim-6f717-lay-era-badges-responsively-Mobile-Safari\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|mobile-timeline-Mobile-Tim-6f717-lay-era-badges-responsively-Mobile-Safari\test-failed-1.png]]

[[ATTACHMENT|mobile-timeline-Mobile-Tim-6f717-lay-era-badges-responsively-Mobile-Safari\video.webm]]

[[ATTACHMENT|mobile-timeline-Mobile-Tim-6f717-lay-era-badges-responsively-Mobile-Safari\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Mobile Timeline Layout › should handle touch interactions on mobile" classname="mobile-timeline.spec.ts" time="4.798">
</testcase>
</testsuite>
</testsuites>