<testsuites id="" name="" tests="5" failures="5" skipped="0" errors="0" time="89.246773">
<testsuite name="visual.spec.ts" timestamp="2025-06-24T05:51:09.939Z" hostname="chromium" tests="1" failures="1" skipped="0" time="25.164" errors="0">
<testcase name="Visual Testing › Map component rendering" classname="visual.spec.ts" time="25.164">
<failure message="visual.spec.ts:88:3 Map component rendering" type="FAILURE">
<![CDATA[  [chromium] › visual.spec.ts:88:3 › Visual Testing › Map component rendering ──────────────────────

    Error: Timed out 5000ms waiting for expect(locator).toHaveScreenshot(expected)

      Timeout 5000ms exceeded.

    Expected: E:\Augment Code Testing\sri-lanka-tourist-guide\tests\visual.spec.ts-snapshots\interactive-map-chromium-win32.png
    Received: E:\Augment Code Testing\sri-lanka-tourist-guide\test-results\visual-Visual-Testing-Map-component-rendering-chromium\interactive-map-actual.png
        Diff: E:\Augment Code Testing\sri-lanka-tourist-guide\test-results\visual-Visual-Testing-Map-component-rendering-chromium\interactive-map-diff.png

    Call log:
      - Expect "toHaveScreenshot(interactive-map.png)" with timeout 5000ms
        - verifying given screenshot expectation
      - waiting for locator('.leaflet-container').first()
        - locator resolved to <div tabindex="0" class="z-0 leaflet-map-container leaflet-container leaflet-touch leaflet-fade-anim leaflet-grab leaflet-touch-drag leaflet-touch-zoom">…</div>
      - taking element screenshot
        - disabled all CSS animations
      - waiting for fonts to load...
      - fonts loaded
      - attempting scroll into view action
        - waiting for element to be stable
      - 39850 pixels (ratio 0.07 of all image pixels) are different.
      - waiting 100ms before taking screenshot
      - waiting for locator('.leaflet-container').first()
        - locator resolved to <div tabindex="0" class="z-0 leaflet-map-container leaflet-container leaflet-touch leaflet-fade-anim leaflet-grab leaflet-touch-drag leaflet-touch-zoom">…</div>
      - taking element screenshot
        - disabled all CSS animations
      - waiting for fonts to load...
      - fonts loaded
      - attempting scroll into view action
        - waiting for element to be stable
      - Timeout 5000ms exceeded.


       95 |     
       96 |     // Take screenshot of map area
    >  97 |     await expect(page.locator('.leaflet-container').first()).toHaveScreenshot('interactive-map.png');
          |                                                              ^
       98 |   });
       99 | });
      100 |
        at E:\Augment Code Testing\sri-lanka-tourist-guide\tests\visual.spec.ts:97:62

    attachment #1: interactive-map-expected.png (image/png) ────────────────────────────────────────
    tests\visual.spec.ts-snapshots\interactive-map-chromium-win32.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: interactive-map-actual.png (image/png) ──────────────────────────────────────────
    test-results\visual-Visual-Testing-Map-component-rendering-chromium\interactive-map-actual.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #3: interactive-map-diff.png (image/png) ────────────────────────────────────────────
    test-results\visual-Visual-Testing-Map-component-rendering-chromium\interactive-map-diff.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #4: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results\visual-Visual-Testing-Map-component-rendering-chromium\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #5: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results\visual-Visual-Testing-Map-component-rendering-chromium\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\test-results\visual-Visual-Testing-Map-component-rendering-chromium\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|..\tests\visual.spec.ts-snapshots\interactive-map-chromium-win32.png]]

[[ATTACHMENT|visual-Visual-Testing-Map-component-rendering-chromium\interactive-map-actual.png]]

[[ATTACHMENT|visual-Visual-Testing-Map-component-rendering-chromium\interactive-map-diff.png]]

[[ATTACHMENT|visual-Visual-Testing-Map-component-rendering-chromium\test-failed-1.png]]

[[ATTACHMENT|visual-Visual-Testing-Map-component-rendering-chromium\video.webm]]

[[ATTACHMENT|visual-Visual-Testing-Map-component-rendering-chromium\error-context.md]]
]]>
</system-out>
</testcase>
</testsuite>
<testsuite name="visual.spec.ts" timestamp="2025-06-24T05:51:09.939Z" hostname="firefox" tests="1" failures="1" skipped="0" time="28.453" errors="0">
<testcase name="Visual Testing › Map component rendering" classname="visual.spec.ts" time="28.453">
<failure message="visual.spec.ts:88:3 Map component rendering" type="FAILURE">
<![CDATA[  [firefox] › visual.spec.ts:88:3 › Visual Testing › Map component rendering ───────────────────────

    Error: expect(locator).toHaveScreenshot(expected)

      450151 pixels (ratio 0.74 of all image pixels) are different.

    Expected: E:\Augment Code Testing\sri-lanka-tourist-guide\tests\visual.spec.ts-snapshots\interactive-map-firefox-win32.png
    Received: E:\Augment Code Testing\sri-lanka-tourist-guide\test-results\visual-Visual-Testing-Map-component-rendering-firefox\interactive-map-actual.png
        Diff: E:\Augment Code Testing\sri-lanka-tourist-guide\test-results\visual-Visual-Testing-Map-component-rendering-firefox\interactive-map-diff.png

    Call log:
      - Expect "toHaveScreenshot(interactive-map.png)" with timeout 5000ms
        - verifying given screenshot expectation
      - waiting for locator('.leaflet-container').first()
        - locator resolved to <div tabindex="0" class="z-0 leaflet-map-container leaflet-container leaflet-touch leaflet-fade-anim leaflet-grab leaflet-touch-drag leaflet-touch-zoom">…</div>
      - taking element screenshot
        - disabled all CSS animations
      - waiting for fonts to load...
      - fonts loaded
      - attempting scroll into view action
        - waiting for element to be stable
      - 39806 pixels (ratio 0.07 of all image pixels) are different.
      - waiting 100ms before taking screenshot
      - waiting for locator('.leaflet-container').first()
        - locator resolved to <div tabindex="0" class="z-0 leaflet-map-container leaflet-container leaflet-touch leaflet-fade-anim leaflet-grab leaflet-touch-drag leaflet-touch-zoom">…</div>
      - taking element screenshot
        - disabled all CSS animations
      - waiting for fonts to load...
      - fonts loaded
      - attempting scroll into view action
        - waiting for element to be stable
      - 507906 pixels (ratio 0.84 of all image pixels) are different.
      - waiting 250ms before taking screenshot
      - waiting for locator('.leaflet-container').first()
        - locator resolved to <div tabindex="0" class="z-0 leaflet-map-container leaflet-container leaflet-touch leaflet-fade-anim leaflet-grab leaflet-touch-drag leaflet-touch-zoom">…</div>
      - taking element screenshot
        - disabled all CSS animations
      - waiting for fonts to load...
      - fonts loaded
      - attempting scroll into view action
        - waiting for element to be stable
      - captured a stable screenshot
      - 450151 pixels (ratio 0.74 of all image pixels) are different.


       95 |     
       96 |     // Take screenshot of map area
    >  97 |     await expect(page.locator('.leaflet-container').first()).toHaveScreenshot('interactive-map.png');
          |                                                              ^
       98 |   });
       99 | });
      100 |
        at E:\Augment Code Testing\sri-lanka-tourist-guide\tests\visual.spec.ts:97:62

    attachment #1: interactive-map-expected.png (image/png) ────────────────────────────────────────
    tests\visual.spec.ts-snapshots\interactive-map-firefox-win32.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: interactive-map-actual.png (image/png) ──────────────────────────────────────────
    test-results\visual-Visual-Testing-Map-component-rendering-firefox\interactive-map-actual.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #3: interactive-map-diff.png (image/png) ────────────────────────────────────────────
    test-results\visual-Visual-Testing-Map-component-rendering-firefox\interactive-map-diff.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #4: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results\visual-Visual-Testing-Map-component-rendering-firefox\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #5: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results\visual-Visual-Testing-Map-component-rendering-firefox\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\test-results\visual-Visual-Testing-Map-component-rendering-firefox\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|..\tests\visual.spec.ts-snapshots\interactive-map-firefox-win32.png]]

[[ATTACHMENT|visual-Visual-Testing-Map-component-rendering-firefox\interactive-map-actual.png]]

[[ATTACHMENT|visual-Visual-Testing-Map-component-rendering-firefox\interactive-map-diff.png]]

[[ATTACHMENT|visual-Visual-Testing-Map-component-rendering-firefox\test-failed-1.png]]

[[ATTACHMENT|visual-Visual-Testing-Map-component-rendering-firefox\video.webm]]

[[ATTACHMENT|visual-Visual-Testing-Map-component-rendering-firefox\error-context.md]]
]]>
</system-out>
</testcase>
</testsuite>
<testsuite name="visual.spec.ts" timestamp="2025-06-24T05:51:09.939Z" hostname="webkit" tests="1" failures="1" skipped="0" time="21.758" errors="0">
<testcase name="Visual Testing › Map component rendering" classname="visual.spec.ts" time="21.758">
<failure message="visual.spec.ts:88:3 Map component rendering" type="FAILURE">
<![CDATA[  [webkit] › visual.spec.ts:88:3 › Visual Testing › Map component rendering ────────────────────────

    Error: Timed out 5000ms waiting for expect(locator).toHaveScreenshot(expected)

    Locator: locator('.leaflet-container').first()
      Timeout 5000ms exceeded.

    Call log:
      - Expect "toHaveScreenshot(interactive-map.png)" with timeout 5000ms
        - generating new stable screenshot expectation
      - waiting for locator('.leaflet-container').first()
        - locator resolved to <div tabindex="0" class="z-0 leaflet-map-container leaflet-container leaflet-touch leaflet-retina leaflet-safari leaflet-fade-anim leaflet-grab leaflet-touch-drag leaflet-touch-zoom">…</div>
      - taking element screenshot
        - disabled all CSS animations
      - waiting for fonts to load...
      - fonts loaded
      - attempting scroll into view action
        - waiting for element to be stable
      - waiting 100ms before taking screenshot
      - waiting for locator('.leaflet-container').first()
        - locator resolved to <div tabindex="0" class="z-0 leaflet-map-container leaflet-container leaflet-touch leaflet-retina leaflet-safari leaflet-fade-anim leaflet-grab leaflet-touch-drag leaflet-touch-zoom">…</div>
      - Timeout 5000ms exceeded.


       95 |     
       96 |     // Take screenshot of map area
    >  97 |     await expect(page.locator('.leaflet-container').first()).toHaveScreenshot('interactive-map.png');
          |                                                              ^
       98 |   });
       99 | });
      100 |
        at E:\Augment Code Testing\sri-lanka-tourist-guide\tests\visual.spec.ts:97:62

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results\visual-Visual-Testing-Map-component-rendering-webkit\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results\visual-Visual-Testing-Map-component-rendering-webkit\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\test-results\visual-Visual-Testing-Map-component-rendering-webkit\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|visual-Visual-Testing-Map-component-rendering-webkit\test-failed-1.png]]

[[ATTACHMENT|visual-Visual-Testing-Map-component-rendering-webkit\video.webm]]

[[ATTACHMENT|visual-Visual-Testing-Map-component-rendering-webkit\error-context.md]]
]]>
</system-out>
</testcase>
</testsuite>
<testsuite name="visual.spec.ts" timestamp="2025-06-24T05:51:09.939Z" hostname="Mobile Chrome" tests="1" failures="1" skipped="0" time="12.389" errors="0">
<testcase name="Visual Testing › Map component rendering" classname="visual.spec.ts" time="12.389">
<failure message="visual.spec.ts:88:3 Map component rendering" type="FAILURE">
<![CDATA[  [Mobile Chrome] › visual.spec.ts:88:3 › Visual Testing › Map component rendering ─────────────────

    Error: expect(locator).toHaveScreenshot(expected)

      66339 pixels (ratio 0.37 of all image pixels) are different.

    Expected: E:\Augment Code Testing\sri-lanka-tourist-guide\tests\visual.spec.ts-snapshots\interactive-map-Mobile-Chrome-win32.png
    Received: E:\Augment Code Testing\sri-lanka-tourist-guide\test-results\visual-Visual-Testing-Map-component-rendering-Mobile-Chrome\interactive-map-actual.png
        Diff: E:\Augment Code Testing\sri-lanka-tourist-guide\test-results\visual-Visual-Testing-Map-component-rendering-Mobile-Chrome\interactive-map-diff.png

    Call log:
      - Expect "toHaveScreenshot(interactive-map.png)" with timeout 5000ms
        - verifying given screenshot expectation
      - waiting for locator('.leaflet-container').first()
        - locator resolved to <div tabindex="0" class="z-0 leaflet-map-container leaflet-container leaflet-touch leaflet-retina leaflet-fade-anim leaflet-grab leaflet-touch-drag leaflet-touch-zoom">…</div>
      - taking element screenshot
        - disabled all CSS animations
      - waiting for fonts to load...
      - fonts loaded
      - attempting scroll into view action
        - waiting for element to be stable
      - 61950 pixels (ratio 0.35 of all image pixels) are different.
      - waiting 100ms before taking screenshot
      - waiting for locator('.leaflet-container').first()
        - locator resolved to <div tabindex="0" class="z-0 leaflet-map-container leaflet-container leaflet-touch leaflet-retina leaflet-fade-anim leaflet-grab leaflet-touch-drag leaflet-touch-zoom">…</div>
      - taking element screenshot
        - disabled all CSS animations
      - waiting for fonts to load...
      - fonts loaded
      - attempting scroll into view action
        - waiting for element to be stable
      - 123705 pixels (ratio 0.69 of all image pixels) are different.
      - waiting 250ms before taking screenshot
      - waiting for locator('.leaflet-container').first()
        - locator resolved to <div tabindex="0" class="z-0 leaflet-map-container leaflet-container leaflet-touch leaflet-retina leaflet-fade-anim leaflet-grab leaflet-touch-drag leaflet-touch-zoom">…</div>
      - taking element screenshot
        - disabled all CSS animations
      - waiting for fonts to load...
      - fonts loaded
      - attempting scroll into view action
        - waiting for element to be stable
      - captured a stable screenshot
      - 66339 pixels (ratio 0.37 of all image pixels) are different.


       95 |     
       96 |     // Take screenshot of map area
    >  97 |     await expect(page.locator('.leaflet-container').first()).toHaveScreenshot('interactive-map.png');
          |                                                              ^
       98 |   });
       99 | });
      100 |
        at E:\Augment Code Testing\sri-lanka-tourist-guide\tests\visual.spec.ts:97:62

    attachment #1: interactive-map-expected.png (image/png) ────────────────────────────────────────
    tests\visual.spec.ts-snapshots\interactive-map-Mobile-Chrome-win32.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: interactive-map-actual.png (image/png) ──────────────────────────────────────────
    test-results\visual-Visual-Testing-Map-component-rendering-Mobile-Chrome\interactive-map-actual.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #3: interactive-map-diff.png (image/png) ────────────────────────────────────────────
    test-results\visual-Visual-Testing-Map-component-rendering-Mobile-Chrome\interactive-map-diff.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #4: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results\visual-Visual-Testing-Map-component-rendering-Mobile-Chrome\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #5: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results\visual-Visual-Testing-Map-component-rendering-Mobile-Chrome\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\test-results\visual-Visual-Testing-Map-component-rendering-Mobile-Chrome\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|..\tests\visual.spec.ts-snapshots\interactive-map-Mobile-Chrome-win32.png]]

[[ATTACHMENT|visual-Visual-Testing-Map-component-rendering-Mobile-Chrome\interactive-map-actual.png]]

[[ATTACHMENT|visual-Visual-Testing-Map-component-rendering-Mobile-Chrome\interactive-map-diff.png]]

[[ATTACHMENT|visual-Visual-Testing-Map-component-rendering-Mobile-Chrome\test-failed-1.png]]

[[ATTACHMENT|visual-Visual-Testing-Map-component-rendering-Mobile-Chrome\video.webm]]

[[ATTACHMENT|visual-Visual-Testing-Map-component-rendering-Mobile-Chrome\error-context.md]]
]]>
</system-out>
</testcase>
</testsuite>
<testsuite name="visual.spec.ts" timestamp="2025-06-24T05:51:09.939Z" hostname="Mobile Safari" tests="1" failures="1" skipped="0" time="15.585" errors="0">
<testcase name="Visual Testing › Map component rendering" classname="visual.spec.ts" time="15.585">
<failure message="visual.spec.ts:88:3 Map component rendering" type="FAILURE">
<![CDATA[  [Mobile Safari] › visual.spec.ts:88:3 › Visual Testing › Map component rendering ─────────────────

    Error: Timed out 5000ms waiting for expect(locator).toHaveScreenshot(expected)

    Locator: locator('.leaflet-container').first()
      Timeout 5000ms exceeded.

    Call log:
      - Expect "toHaveScreenshot(interactive-map.png)" with timeout 5000ms
        - generating new stable screenshot expectation
      - waiting for locator('.leaflet-container').first()
        - locator resolved to <div tabindex="0" class="z-0 leaflet-map-container leaflet-container leaflet-touch leaflet-retina leaflet-safari leaflet-fade-anim leaflet-grab leaflet-touch-drag leaflet-touch-zoom">…</div>
      - taking element screenshot
        - disabled all CSS animations
      - waiting for fonts to load...
      - fonts loaded
      - attempting scroll into view action
        - waiting for element to be stable
      - waiting 100ms before taking screenshot
      - waiting for locator('.leaflet-container').first()
        - locator resolved to <div tabindex="0" class="z-0 leaflet-map-container leaflet-container leaflet-touch leaflet-retina leaflet-safari leaflet-fade-anim leaflet-grab leaflet-touch-drag leaflet-touch-zoom">…</div>
      - taking element screenshot
        - disabled all CSS animations
      - waiting for fonts to load...
      - fonts loaded
      - attempting scroll into view action
        - waiting for element to be stable
      - Timeout 5000ms exceeded.


       95 |     
       96 |     // Take screenshot of map area
    >  97 |     await expect(page.locator('.leaflet-container').first()).toHaveScreenshot('interactive-map.png');
          |                                                              ^
       98 |   });
       99 | });
      100 |
        at E:\Augment Code Testing\sri-lanka-tourist-guide\tests\visual.spec.ts:97:62

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results\visual-Visual-Testing-Map-component-rendering-Mobile-Safari\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results\visual-Visual-Testing-Map-component-rendering-Mobile-Safari\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\test-results\visual-Visual-Testing-Map-component-rendering-Mobile-Safari\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|visual-Visual-Testing-Map-component-rendering-Mobile-Safari\test-failed-1.png]]

[[ATTACHMENT|visual-Visual-Testing-Map-component-rendering-Mobile-Safari\video.webm]]

[[ATTACHMENT|visual-Visual-Testing-Map-component-rendering-Mobile-Safari\error-context.md]]
]]>
</system-out>
</testcase>
</testsuite>
</testsuites>