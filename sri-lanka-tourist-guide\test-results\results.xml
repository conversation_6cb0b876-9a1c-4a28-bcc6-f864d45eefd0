<testsuites id="" name="" tests="5" failures="2" skipped="3" errors="0" time="69.318002">
<testsuite name="accessibility.spec.ts" timestamp="2025-06-24T06:41:40.278Z" hostname="chromium" tests="1" failures="1" skipped="0" time="16.456" errors="0">
<testcase name="Accessibility Testing › Alternative Text for Images" classname="accessibility.spec.ts" time="16.456">
<failure message="accessibility.spec.ts:230:3 Alternative Text for Images" type="FAILURE">
<![CDATA[  [chromium] › accessibility.spec.ts:230:3 › Accessibility Testing › Alternative Text for Images ───

    Error: expect(received).toBe(expected) // Object.is equality

    Expected: 0
    Received: 35

      244 |       
      245 |       console.log(`${url} - Images without alt text:`, imageIssues.length);
    > 246 |       expect(imageIssues.length).toBe(0);
          |                                  ^
      247 |     }
      248 |   });
      249 |
        at E:\Augment Code Testing\sri-lanka-tourist-guide\tests\accessibility.spec.ts:246:34

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results\accessibility-Accessibilit-e553e-Alternative-Text-for-Images-chromium\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results\accessibility-Accessibilit-e553e-Alternative-Text-for-Images-chromium\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\test-results\accessibility-Accessibilit-e553e-Alternative-Text-for-Images-chromium\error-context.md
]]>
</failure>
<system-out>
<![CDATA[/ - Images without alt text: [33m0[39m
/destinations - Images without alt text: [33m35[39m

[[ATTACHMENT|accessibility-Accessibilit-e553e-Alternative-Text-for-Images-chromium\test-failed-1.png]]

[[ATTACHMENT|accessibility-Accessibilit-e553e-Alternative-Text-for-Images-chromium\video.webm]]

[[ATTACHMENT|accessibility-Accessibilit-e553e-Alternative-Text-for-Images-chromium\error-context.md]]
]]>
</system-out>
</testcase>
</testsuite>
<testsuite name="accessibility.spec.ts" timestamp="2025-06-24T06:41:40.278Z" hostname="firefox" tests="1" failures="1" skipped="0" time="18.053" errors="0">
<testcase name="Accessibility Testing › Alternative Text for Images" classname="accessibility.spec.ts" time="18.053">
<failure message="accessibility.spec.ts:230:3 Alternative Text for Images" type="FAILURE">
<![CDATA[  [firefox] › accessibility.spec.ts:230:3 › Accessibility Testing › Alternative Text for Images ────

    Error: expect(received).toBe(expected) // Object.is equality

    Expected: 0
    Received: 35

      244 |       
      245 |       console.log(`${url} - Images without alt text:`, imageIssues.length);
    > 246 |       expect(imageIssues.length).toBe(0);
          |                                  ^
      247 |     }
      248 |   });
      249 |
        at E:\Augment Code Testing\sri-lanka-tourist-guide\tests\accessibility.spec.ts:246:34

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results\accessibility-Accessibilit-e553e-Alternative-Text-for-Images-firefox\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results\accessibility-Accessibilit-e553e-Alternative-Text-for-Images-firefox\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\test-results\accessibility-Accessibilit-e553e-Alternative-Text-for-Images-firefox\error-context.md
]]>
</failure>
<system-out>
<![CDATA[/ - Images without alt text: [33m0[39m
/destinations - Images without alt text: [33m35[39m

[[ATTACHMENT|accessibility-Accessibilit-e553e-Alternative-Text-for-Images-firefox\test-failed-1.png]]

[[ATTACHMENT|accessibility-Accessibilit-e553e-Alternative-Text-for-Images-firefox\video.webm]]

[[ATTACHMENT|accessibility-Accessibilit-e553e-Alternative-Text-for-Images-firefox\error-context.md]]
]]>
</system-out>
</testcase>
</testsuite>
<testsuite name="accessibility.spec.ts" timestamp="2025-06-24T06:41:40.278Z" hostname="webkit" tests="1" failures="0" skipped="1" time="0.001" errors="0">
<testcase name="Accessibility Testing › Alternative Text for Images" classname="accessibility.spec.ts" time="0.001">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="accessibility.spec.ts" timestamp="2025-06-24T06:41:40.278Z" hostname="Mobile Chrome" tests="1" failures="0" skipped="1" time="0" errors="0">
<testcase name="Accessibility Testing › Alternative Text for Images" classname="accessibility.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="accessibility.spec.ts" timestamp="2025-06-24T06:41:40.278Z" hostname="Mobile Safari" tests="1" failures="0" skipped="1" time="0" errors="0">
<testcase name="Accessibility Testing › Alternative Text for Images" classname="accessibility.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
</testsuites>