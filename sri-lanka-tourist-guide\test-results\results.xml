<testsuites id="" name="" tests="5" failures="0" skipped="0" errors="0" time="35.994228">
<testsuite name="articles.spec.ts" timestamp="2025-06-24T06:24:51.419Z" hostname="chromium" tests="1" failures="0" skipped="0" time="10.309" errors="0">
<testcase name="Articles Page Tests › should display articles page correctly" classname="articles.spec.ts" time="10.309">
</testcase>
</testsuite>
<testsuite name="articles.spec.ts" timestamp="2025-06-24T06:24:51.419Z" hostname="firefox" tests="1" failures="0" skipped="0" time="11.166" errors="0">
<testcase name="Articles Page Tests › should display articles page correctly" classname="articles.spec.ts" time="11.166">
</testcase>
</testsuite>
<testsuite name="articles.spec.ts" timestamp="2025-06-24T06:24:51.419Z" hostname="webkit" tests="1" failures="0" skipped="0" time="5.978" errors="0">
<testcase name="Articles Page Tests › should display articles page correctly" classname="articles.spec.ts" time="5.978">
</testcase>
</testsuite>
<testsuite name="articles.spec.ts" timestamp="2025-06-24T06:24:51.419Z" hostname="Mobile Chrome" tests="1" failures="0" skipped="0" time="3.613" errors="0">
<testcase name="Articles Page Tests › should display articles page correctly" classname="articles.spec.ts" time="3.613">
</testcase>
</testsuite>
<testsuite name="articles.spec.ts" timestamp="2025-06-24T06:24:51.419Z" hostname="Mobile Safari" tests="1" failures="0" skipped="0" time="4.678" errors="0">
<testcase name="Articles Page Tests › should display articles page correctly" classname="articles.spec.ts" time="4.678">
</testcase>
</testsuite>
</testsuites>