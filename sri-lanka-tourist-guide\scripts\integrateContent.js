#!/usr/bin/env node

const fs = require('fs')
const path = require('path')

// Content integration script for Sri Lanka Tourist Guide
// This script processes all markdown articles and integrates them into the React application

const CONTENT_DIR = path.join(__dirname, '../content-generation/articles')
const OUTPUT_DIR = path.join(__dirname, '../src/data')
const ARTICLES_FILE = path.join(OUTPUT_DIR, 'articles.ts')

// Category mappings
const CATEGORY_MAPPINGS = {
  'accommodation': 'Practical Travel',
  'budget': 'Practical Travel',
  'cultural-etiquette': 'Practical Travel',
  'health-safety': 'Practical Travel',
  'transportation': 'Practical Travel',
  'visa': 'Practical Travel',
  'weather': 'Practical Travel',
  'adams-peak': 'Adventure & Activities',
  'cycling': 'Adventure & Activities',
  'diving': 'Adventure & Activities',
  'knuckles': 'Adventure & Activities',
  'rafting': 'Adventure & Activities',
  'rock-climbing': 'Adventure & Activities',
  'surfing': 'Adventure & Activities',
  'wildlife-safaris': 'Adventure & Activities',
  'cuisine': 'Food & Culinary',
  'food-markets': 'Food & Culinary',
  'seafood': 'Food & Culinary',
  'spice-gardens': 'Food & Culinary',
  'street-food': 'Food & Culinary',
  'sweets-desserts': 'Food & Culinary',
  'vegetarian': 'Food & Culinary',
  'anuradhapura': 'Cultural Heritage',
  'ayurveda': 'Cultural Heritage',
  'buddhism': 'Cultural Heritage',
  'colonial-heritage': 'Cultural Heritage',
  'dambulla': 'Cultural Heritage',
  'festivals': 'Cultural Heritage',
  'mihintale': 'Cultural Heritage',
  'polonnaruwa': 'Cultural Heritage',
  'tamil-culture': 'Cultural Heritage',
  'tea-culture': 'Cultural Heritage',
  'temple-of-tooth': 'Cultural Heritage',
  'traditional-arts': 'Cultural Heritage'
}

// Difficulty mappings
const DIFFICULTY_MAPPINGS = {
  'adams-peak': 'Challenging',
  'knuckles': 'Moderate to Challenging',
  'rock-climbing': 'Challenging',
  'rafting': 'Moderate',
  'diving': 'Easy to Moderate',
  'cycling': 'Easy to Moderate',
  'surfing': 'Moderate',
  'horton-plains': 'Moderate',
  'sinharaja': 'Moderate'
}

// Best time to visit mappings
const BEST_TIME_MAPPINGS = {
  'yala': 'February to July',
  'minneriya': 'July to October',
  'udawalawe': 'Year-round',
  'adams-peak': 'December to April',
  'ella': 'December to March',
  'nuwara-eliya': 'December to April',
  'surfing': 'November to April (West/South), May to September (East)',
  'diving': 'November to April (West/South), May to September (East)'
}

function extractMetadata(content, filename) {
  const lines = content.split('\n')
  const title = lines.find(line => line.startsWith('# '))?.replace('# ', '') || filename
  
  // Extract excerpt from first paragraph after title
  let excerpt = ''
  let foundTitle = false
  for (const line of lines) {
    if (line.startsWith('# ')) {
      foundTitle = true
      continue
    }
    if (foundTitle && line.trim() && !line.startsWith('*') && !line.startsWith('#')) {
      excerpt = line.trim()
      break
    }
  }
  
  // Extract reading time
  const readingTimeMatch = content.match(/Reading time: (\d+) minutes?/)
  const readTime = readingTimeMatch ? `${readingTimeMatch[1]} min read` : '15 min read'
  
  // Extract publish date
  const dateMatch = content.match(/Last updated: ([^|]+)/)
  const publishDate = dateMatch ? dateMatch[1].trim() : '2025-06-24'
  
  // Determine category
  let category = 'Destinations'
  for (const [key, cat] of Object.entries(CATEGORY_MAPPINGS)) {
    if (filename.includes(key)) {
      category = cat
      break
    }
  }
  
  // Determine difficulty
  let difficulty = 'Easy'
  for (const [key, diff] of Object.entries(DIFFICULTY_MAPPINGS)) {
    if (filename.includes(key)) {
      difficulty = diff
      break
    }
  }
  
  // Determine best time to visit
  let bestTimeToVisit = 'Year-round'
  for (const [key, time] of Object.entries(BEST_TIME_MAPPINGS)) {
    if (filename.includes(key)) {
      bestTimeToVisit = time
      break
    }
  }
  
  // Extract tags from content
  const tags = []
  if (content.includes('UNESCO')) tags.push('UNESCO')
  if (content.includes('wildlife') || content.includes('safari')) tags.push('Wildlife')
  if (content.includes('temple') || content.includes('Buddhist')) tags.push('Religious Sites')
  if (content.includes('beach') || content.includes('coast')) tags.push('Beaches')
  if (content.includes('hiking') || content.includes('trekking')) tags.push('Hiking')
  if (content.includes('food') || content.includes('cuisine')) tags.push('Food')
  if (content.includes('culture') || content.includes('traditional')) tags.push('Culture')
  if (content.includes('adventure') || content.includes('activity')) tags.push('Adventure')
  if (content.includes('photography')) tags.push('Photography')
  if (content.includes('family')) tags.push('Family Friendly')
  
  // Generate SEO keywords
  const seoKeywords = []
  const titleWords = title.toLowerCase().split(' ')
  titleWords.forEach(word => {
    if (word.length > 3 && !['guide', 'complete', 'travel', 'tourism'].includes(word)) {
      seoKeywords.push(word)
    }
  })
  seoKeywords.push('Sri Lanka', 'travel guide', 'tourism')
  
  return {
    title,
    excerpt: excerpt.substring(0, 200) + (excerpt.length > 200 ? '...' : ''),
    readTime,
    publishDate,
    category,
    difficulty,
    bestTimeToVisit,
    tags: tags.slice(0, 6), // Limit to 6 tags
    seoKeywords: [...new Set(seoKeywords)].slice(0, 10) // Unique keywords, max 10
  }
}

function generateImagePath(filename) {
  // Generate appropriate image path based on content type
  const baseImagePath = '/images'
  
  if (filename.includes('yala')) return `${baseImagePath}/yala-leopard-safari.jpg`
  if (filename.includes('ella')) return `${baseImagePath}/ella-nine-arch-bridge.jpg`
  if (filename.includes('sigiriya')) return `${baseImagePath}/sigiriya-rock-fortress.jpg`
  if (filename.includes('kandy')) return `${baseImagePath}/kandy-temple-tooth.jpg`
  if (filename.includes('galle')) return `${baseImagePath}/galle-fort-colonial.jpg`
  if (filename.includes('adams-peak')) return `${baseImagePath}/adams-peak-sunrise.jpg`
  if (filename.includes('nuwara-eliya')) return `${baseImagePath}/nuwara-eliya-tea-plantations.jpg`
  if (filename.includes('colombo')) return `${baseImagePath}/colombo-city-skyline.jpg`
  if (filename.includes('anuradhapura')) return `${baseImagePath}/anuradhapura-ancient-stupas.jpg`
  if (filename.includes('polonnaruwa')) return `${baseImagePath}/polonnaruwa-ancient-ruins.jpg`
  if (filename.includes('dambulla')) return `${baseImagePath}/dambulla-cave-temple.jpg`
  if (filename.includes('minneriya')) return `${baseImagePath}/minneriya-elephant-gathering.jpg`
  if (filename.includes('udawalawe')) return `${baseImagePath}/udawalawe-elephants.jpg`
  if (filename.includes('sinharaja')) return `${baseImagePath}/sinharaja-rainforest.jpg`
  if (filename.includes('horton-plains')) return `${baseImagePath}/horton-plains-worlds-end.jpg`
  if (filename.includes('knuckles')) return `${baseImagePath}/knuckles-mountain-range.jpg`
  if (filename.includes('bentota')) return `${baseImagePath}/bentota-beach-resort.jpg`
  if (filename.includes('negombo')) return `${baseImagePath}/negombo-fishing-boats.jpg`
  if (filename.includes('trincomalee')) return `${baseImagePath}/trincomalee-ancient-port.jpg`
  if (filename.includes('jaffna')) return `${baseImagePath}/jaffna-northern-culture.jpg`
  if (filename.includes('cuisine')) return `${baseImagePath}/sri-lankan-rice-curry.jpg`
  if (filename.includes('tea')) return `${baseImagePath}/ceylon-tea-plantation.jpg`
  if (filename.includes('spice')) return `${baseImagePath}/spice-garden-cinnamon.jpg`
  if (filename.includes('street-food')) return `${baseImagePath}/sri-lanka-street-food.jpg`
  if (filename.includes('seafood')) return `${baseImagePath}/sri-lanka-seafood-curry.jpg`
  if (filename.includes('ayurveda')) return `${baseImagePath}/ayurveda-treatment.jpg`
  if (filename.includes('buddhism')) return `${baseImagePath}/buddhist-monks-ceremony.jpg`
  if (filename.includes('festivals')) return `${baseImagePath}/esala-perahera-festival.jpg`
  if (filename.includes('surfing')) return `${baseImagePath}/surfing-arugam-bay.jpg`
  if (filename.includes('diving')) return `${baseImagePath}/scuba-diving-coral.jpg`
  if (filename.includes('cycling')) return `${baseImagePath}/cycling-tea-plantations.jpg`
  if (filename.includes('rafting')) return `${baseImagePath}/white-water-rafting.jpg`
  if (filename.includes('accommodation')) return `${baseImagePath}/sri-lanka-luxury-resort.jpg`
  if (filename.includes('transportation')) return `${baseImagePath}/sri-lanka-train-journey.jpg`
  if (filename.includes('budget')) return `${baseImagePath}/sri-lanka-money-budget.jpg`
  
  // Default image
  return `${baseImagePath}/sri-lanka-landscape.jpg`
}

function processArticles() {
  console.log('🔄 Processing articles for integration...')
  
  if (!fs.existsSync(CONTENT_DIR)) {
    console.error('❌ Content directory not found:', CONTENT_DIR)
    process.exit(1)
  }
  
  const files = fs.readdirSync(CONTENT_DIR).filter(file => file.endsWith('.md'))
  console.log(`📄 Found ${files.length} articles to process`)
  
  const articles = {}
  let featuredCount = 0
  
  files.forEach((file, index) => {
    const filePath = path.join(CONTENT_DIR, file)
    const content = fs.readFileSync(filePath, 'utf-8')
    const filename = path.basename(file, '.md')
    const id = filename
    
    const metadata = extractMetadata(content, filename)
    const imagePath = generateImagePath(filename)
    
    // Mark some articles as featured (top 20%)
    const isFeatured = featuredCount < Math.ceil(files.length * 0.2)
    if (isFeatured) featuredCount++
    
    articles[id] = {
      id,
      title: metadata.title,
      excerpt: metadata.excerpt,
      content: `# ${metadata.title}

*Last updated: ${metadata.publishDate} | Reading time: ${metadata.readTime}*

${content.replace(/^# .+\n/, '')}`, // Remove duplicate title
      category: metadata.category,
      readTime: metadata.readTime,
      publishDate: metadata.publishDate,
      image: imagePath,
      tags: metadata.tags,
      author: 'Sri Lanka Travel Expert',
      featured: isFeatured,
      difficulty: metadata.difficulty,
      bestTimeToVisit: metadata.bestTimeToVisit,
      seoKeywords: metadata.seoKeywords,
      relatedArticles: [] // Will be populated later
    }
    
    console.log(`✅ Processed: ${metadata.title}`)
  })
  
  // Generate related articles
  Object.keys(articles).forEach(id => {
    const article = articles[id]
    const related = Object.keys(articles)
      .filter(otherId => otherId !== id)
      .filter(otherId => {
        const other = articles[otherId]
        return other.category === article.category || 
               article.tags.some(tag => other.tags.includes(tag))
      })
      .slice(0, 3)
    
    article.relatedArticles = related
  })
  
  return articles
}

function generateArticlesFile(articles) {
  console.log('📝 Generating articles.ts file...')
  
  const articlesCount = Object.keys(articles).length
  const categories = [...new Set(Object.values(articles).map(a => a.category))]
  
  const fileContent = `// Auto-generated articles data for Sri Lanka Tourist Guide
// Generated on: ${new Date().toISOString()}
// Total articles: ${articlesCount}
// Categories: ${categories.join(', ')}

export interface Article {
  id: string
  title: string
  content: string
  excerpt: string
  category: string
  readTime: string
  publishDate: string
  image: string
  tags: string[]
  author: string
  featured: boolean
  difficulty?: string
  bestTimeToVisit?: string
  seoKeywords?: string[]
  relatedArticles?: string[]
}

export const articles: Record<string, Article> = ${JSON.stringify(articles, null, 2)}

// Export function to get all articles
export const getAllArticles = (): Article[] => {
  return Object.values(articles).sort((a, b) => 
    new Date(b.publishDate).getTime() - new Date(a.publishDate).getTime()
  )
}

// Export function to get article by ID
export const getArticleById = (id: string): Article | undefined => {
  return articles[id]
}

// Export function to get articles by category
export const getArticlesByCategory = (category: string): Article[] => {
  return getAllArticles().filter(article => article.category === category)
}

// Export function to get featured articles
export const getFeaturedArticles = (): Article[] => {
  return getAllArticles().filter(article => article.featured)
}

// Export function to search articles
export const searchArticles = (query: string): Article[] => {
  const lowercaseQuery = query.toLowerCase()
  return getAllArticles().filter(article =>
    article.title.toLowerCase().includes(lowercaseQuery) ||
    article.excerpt.toLowerCase().includes(lowercaseQuery) ||
    article.tags.some(tag => tag.toLowerCase().includes(lowercaseQuery)) ||
    (article.seoKeywords && article.seoKeywords.some(keyword => 
      keyword.toLowerCase().includes(lowercaseQuery)
    ))
  )
}

export default articles`

  fs.writeFileSync(ARTICLES_FILE, fileContent)
  console.log(`✅ Generated articles file with ${articlesCount} articles`)
}

function main() {
  console.log('🚀 Starting content integration process...')
  
  try {
    const articles = processArticles()
    generateArticlesFile(articles)
    
    console.log('🎉 Content integration completed successfully!')
    console.log(`📊 Summary:`)
    console.log(`   - Total articles: ${Object.keys(articles).length}`)
    console.log(`   - Categories: ${[...new Set(Object.values(articles).map(a => a.category))].length}`)
    console.log(`   - Featured articles: ${Object.values(articles).filter(a => a.featured).length}`)
    console.log(`   - Average word count: ${Math.round(Object.values(articles).reduce((sum, a) => sum + a.content.split(' ').length, 0) / Object.keys(articles).length)}`)
    
  } catch (error) {
    console.error('❌ Content integration failed:', error)
    process.exit(1)
  }
}

if (require.main === module) {
  main()
}

module.exports = { processArticles, generateArticlesFile }
