import { test, expect } from '@playwright/test';

test.describe('Articles Page Tests', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/articles');
  });

  test('should display articles page correctly', async ({ page }) => {
    await expect(page).toHaveTitle(/Articles/);
    await expect(page.locator('h1')).toContainText('Sri Lanka Travel Articles');
    
    // Check for search functionality
    await expect(page.locator('input[placeholder*="Search articles"]')).toBeVisible();
    
    // Check for category filters
    await expect(page.locator('button:has-text("All")')).toBeVisible();
    await expect(page.locator('button:has-text("Cultural Sites")')).toBeVisible();
    await expect(page.locator('button:has-text("Wildlife")')).toBeVisible();
  });

  test('should display article cards', async ({ page }) => {
    // Wait for articles to load
    await page.waitForSelector('[data-testid="article-card"], article', { timeout: 10000 });
    
    // Check if articles are displayed
    const articleCards = page.locator('article, [data-testid="article-card"]');
    await expect(articleCards.first()).toBeVisible();
    
    // Check article card content
    await expect(articleCards.first()).toContainText('Sigiriya');
    await expect(articleCards.first().locator('text=Read Article')).toBeVisible();
  });

  test('should filter articles by category', async ({ page }) => {
    // Wait for articles to load
    await page.waitForSelector('article', { timeout: 10000 });
    
    // Click on Cultural Sites filter
    await page.click('button:has-text("Cultural Sites")');
    
    // Wait for filtering to complete
    await page.waitForTimeout(1000);
    
    // Check that only cultural articles are shown
    const articles = page.locator('article');
    const count = await articles.count();
    expect(count).toBeGreaterThan(0);
    
    // Verify cultural category is selected
    const culturalButton = page.locator('button:has-text("Cultural Sites")');
    await expect(culturalButton).toHaveClass(/bg-primary-600/);
  });

  test('should search articles', async ({ page }) => {
    // Wait for articles to load
    await page.waitForSelector('article', { timeout: 10000 });
    
    // Search for Sigiriya
    await page.fill('input[placeholder*="Search articles"]', 'Sigiriya');
    
    // Wait for search results
    await page.waitForTimeout(1000);
    
    // Check search results
    const articles = page.locator('article');
    const count = await articles.count();
    expect(count).toBeGreaterThan(0);
    
    // Verify Sigiriya article is shown
    await expect(page.locator('h3:has-text("Sigiriya")')).toBeVisible();
  });

  test('should navigate to article detail page', async ({ page }) => {
    // Wait for articles to load
    await page.waitForSelector('article', { timeout: 10000 });
    
    // Click on first "Read Article" button
    await page.click('text=Read Article >> nth=0');
    
    // Should navigate to article detail page
    await expect(page).toHaveURL(/\/articles\/.+/);
    
    // Should display article content
    await expect(page.locator('h1')).toBeVisible();
    await expect(page.locator('text=Back to Articles')).toBeVisible();
  });

  test('should display featured articles section', async ({ page }) => {
    // Check for featured articles section
    await expect(page.locator('text=Featured Articles')).toBeVisible();
    
    // Featured articles should be displayed in larger cards
    const featuredSection = page.locator('text=Featured Articles').locator('..').locator('..');
    await expect(featuredSection.locator('article').first()).toBeVisible();
  });

  test('should show newsletter signup', async ({ page }) => {
    // Scroll to newsletter section
    await page.locator('text=Stay Updated').scrollIntoViewIfNeeded();
    
    // Check newsletter signup elements
    await expect(page.locator('text=Stay Updated with Latest Travel Guides')).toBeVisible();
    await expect(page.locator('input[type="email"]')).toBeVisible();
    await expect(page.locator('button:has-text("Subscribe")')).toBeVisible();
  });
});

test.describe('Article Detail Page Tests', () => {
  test('should display article detail correctly', async ({ page }) => {
    // Navigate to a specific article
    await page.goto('/articles/sigiriya-complete-guide');
    
    // Check page title and content
    await expect(page).toHaveTitle(/Sigiriya/);
    await expect(page.locator('h1')).toContainText('Sigiriya');
    
    // Check back navigation
    await expect(page.locator('text=Back to Articles')).toBeVisible();
    
    // Check article metadata
    await expect(page.locator('text=Cultural Sites')).toBeVisible();
    await expect(page.locator('text=12 minutes')).toBeVisible();
    await expect(page.locator('span:has-text("January 2025")')).toBeVisible();
  });

  test('should have working back navigation', async ({ page }) => {
    await page.goto('/articles/sigiriya-complete-guide');
    
    // Click back to articles
    await page.click('text=Back to Articles');
    
    // Should return to articles page
    await expect(page).toHaveURL('/articles');
  });

  test('should display article tags', async ({ page }) => {
    await page.goto('/articles/sigiriya-complete-guide');
    
    // Check for tags
    await expect(page.locator('text=UNESCO')).toBeVisible();
    await expect(page.locator('text=Ancient History')).toBeVisible();
    await expect(page.locator('span:has-text("Cultural Triangle")')).toBeVisible();
  });

  test('should have share and save buttons', async ({ page }) => {
    await page.goto('/articles/sigiriya-complete-guide');
    
    // Check for action buttons
    await expect(page.locator('button:has-text("Share")')).toBeVisible();
    await expect(page.locator('button:has-text("Save")')).toBeVisible();
  });

  test('should redirect to articles page for invalid article', async ({ page }) => {
    await page.goto('/articles/non-existent-article');
    
    // Should redirect to articles page
    await expect(page).toHaveURL('/articles');
  });
});
