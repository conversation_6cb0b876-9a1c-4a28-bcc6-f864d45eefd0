import { test, expect } from '@playwright/test'

test.describe('Image Alt Text Debug', () => {
  test('Identify images without alt text on destinations page', async ({ page }) => {
    await page.goto('/destinations')
    await page.waitForLoadState('networkidle')
    
    // Find all images without alt text
    const imagesWithoutAlt = await page.evaluate(() => {
      const images = Array.from(document.querySelectorAll('img'))
      const problematicImages = images.filter(img => {
        const alt = img.getAttribute('alt')
        return !alt || alt.trim() === ''
      })
      
      return problematicImages.map(img => ({
        src: img.src,
        alt: img.getAttribute('alt'),
        className: img.className,
        parentClassName: img.parentElement?.className,
        parentTagName: img.parentElement?.tagName,
        outerHTML: img.outerHTML.slice(0, 200),
        position: {
          x: img.getBoundingClientRect().x,
          y: img.getBoundingClientRect().y,
          width: img.getBoundingClientRect().width,
          height: img.getBoundingClientRect().height
        }
      }))
    })
    
    console.log('Images without alt text:', imagesWithoutAlt)
    console.log('Total count:', imagesWithoutAlt.length)
    
    // Take screenshot for visual inspection
    await page.screenshot({
      path: 'test-results/destinations-images-debug.png',
      fullPage: true
    })
    
    // Log details for each problematic image
    for (let i = 0; i < Math.min(imagesWithoutAlt.length, 10); i++) {
      const img = imagesWithoutAlt[i]
      console.log(`Image ${i + 1}:`, {
        src: img.src,
        alt: img.alt,
        className: img.className,
        parentClassName: img.parentClassName,
        outerHTML: img.outerHTML
      })
    }
    
    expect(imagesWithoutAlt.length).toBe(0)
  })

  test('Check all pages for images without alt text', async ({ page }) => {
    const pages = ['/', '/destinations', '/activities', '/culture', '/articles', '/travel-tips', '/contact']
    
    for (const url of pages) {
      await page.goto(url)
      await page.waitForLoadState('networkidle')
      
      const imagesWithoutAlt = await page.evaluate(() => {
        const images = Array.from(document.querySelectorAll('img'))
        return images.filter(img => {
          const alt = img.getAttribute('alt')
          return !alt || alt.trim() === ''
        }).map(img => ({
          src: img.src,
          alt: img.getAttribute('alt'),
          className: img.className,
          outerHTML: img.outerHTML.slice(0, 100)
        }))
      })
      
      console.log(`${url} - Images without alt text:`, imagesWithoutAlt.length)
      if (imagesWithoutAlt.length > 0) {
        console.log(`${url} - First few problematic images:`, imagesWithoutAlt.slice(0, 3))
      }
    }
  })
})
