import { test, expect } from '@playwright/test'

test.describe('Enhanced Navigation Accessibility', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/')
    await page.waitForLoadState('networkidle')
  })

  test('Skip link functionality', async ({ page }) => {
    // Check if skip link exists
    const skipLink = page.locator('a[href="#main-content"]')
    await expect(skipLink).toBeAttached()

    // Focus on skip link manually (it's the first focusable element)
    await skipLink.focus()

    // Check if skip link is focused and becomes visible on focus
    await expect(skipLink).toBeFocused()
    await expect(skipLink).toBeVisible()

    // Activate skip link with keyboard (more reliable than click)
    await page.keyboard.press('Enter')
    await page.waitForTimeout(100)

    // Verify main content is focused
    const mainContent = page.locator('#main-content')
    await expect(mainContent).toBeFocused()
  })

  test('Desktop navigation accessibility', async ({ page }) => {
    // Test desktop navigation at larger viewport
    await page.setViewportSize({ width: 1024, height: 768 })
    
    // Check navigation structure
    const nav = page.locator('nav[aria-label="Main navigation"]')
    await expect(nav).toBeVisible()
    
    // Test each navigation link
    const navLinks = nav.locator('a')
    const linkCount = await navLinks.count()
    expect(linkCount).toBeGreaterThan(0)
    
    for (let i = 0; i < linkCount; i++) {
      const link = navLinks.nth(i)
      
      // Check link has proper attributes
      await expect(link).toHaveAttribute('tabindex', '0')
      
      // Check focus styles
      await link.focus()
      await expect(link).toBeFocused()
      
      // Check for aria-current on active page
      const href = await link.getAttribute('href')
      if (href === '/') {
        await expect(link).toHaveAttribute('aria-current', 'page')
      }
    }
  })

  test('Mobile navigation accessibility', async ({ page }) => {
    // Set mobile viewport
    await page.setViewportSize({ width: 375, height: 667 })

    // Check mobile menu button using a more stable selector
    const menuButton = page.locator('.mobile-menu-button')
    await expect(menuButton).toBeVisible()
    await expect(menuButton).toHaveAttribute('aria-expanded', 'false')
    await expect(menuButton).toHaveAttribute('aria-controls', 'mobile-menu')
    await expect(menuButton).toHaveAttribute('aria-haspopup', 'true')

    // Open mobile menu
    await menuButton.click()
    await page.waitForTimeout(300)

    // Check menu button state after opening
    await expect(menuButton).toHaveAttribute('aria-expanded', 'true')
    await expect(menuButton).toHaveAttribute('aria-label', 'Close navigation menu')
    
    // Check mobile menu
    const mobileMenu = page.locator('#mobile-menu')
    await expect(mobileMenu).toBeVisible()
    await expect(mobileMenu).toHaveAttribute('aria-hidden', 'false')
    await expect(mobileMenu).toHaveAttribute('role', 'navigation')
    await expect(mobileMenu).toHaveAttribute('aria-label', 'Mobile navigation')
    
    // Test mobile menu links
    const mobileLinks = mobileMenu.locator('a')
    const mobileLinkCount = await mobileLinks.count()
    expect(mobileLinkCount).toBeGreaterThan(0)
    
    for (let i = 0; i < mobileLinkCount; i++) {
      const link = mobileLinks.nth(i)
      await expect(link).toHaveAttribute('tabindex', '0')
      
      // Check for aria-current on active page
      const href = await link.getAttribute('href')
      if (href === '/') {
        await expect(link).toHaveAttribute('aria-current', 'page')
      }
    }
  })

  test('Mobile menu keyboard navigation', async ({ page }) => {
    await page.setViewportSize({ width: 375, height: 667 })

    // Open mobile menu with keyboard
    const menuButton = page.locator('.mobile-menu-button')
    await menuButton.focus()
    await page.keyboard.press('Enter')
    await page.waitForTimeout(300)
    
    // Check if first menu item is focused
    const firstMenuItem = page.locator('#mobile-menu a').first()
    await expect(firstMenuItem).toBeFocused()
    
    // Test arrow key navigation
    await page.keyboard.press('ArrowDown')
    const secondMenuItem = page.locator('#mobile-menu a').nth(1)
    await expect(secondMenuItem).toBeFocused()
    
    // Test Home key
    await page.keyboard.press('Home')
    await expect(firstMenuItem).toBeFocused()
    
    // Test End key
    await page.keyboard.press('End')
    const lastMenuItem = page.locator('#mobile-menu a').last()
    await expect(lastMenuItem).toBeFocused()
    
    // Test Escape key closes menu
    await page.keyboard.press('Escape')
    await page.waitForTimeout(300)
    
    const mobileMenu = page.locator('#mobile-menu')
    await expect(mobileMenu).toHaveAttribute('aria-hidden', 'true')
    await expect(menuButton).toBeFocused() // Focus should return to button
  })

  test('Mobile menu click outside closes menu', async ({ page }) => {
    await page.setViewportSize({ width: 375, height: 667 })

    // Open mobile menu
    const menuButton = page.locator('.mobile-menu-button')
    await menuButton.click()
    await page.waitForTimeout(300)
    
    // Click outside menu
    await page.click('body', { position: { x: 50, y: 50 } })
    await page.waitForTimeout(300)
    
    // Check menu is closed
    const mobileMenu = page.locator('#mobile-menu')
    await expect(mobileMenu).toHaveAttribute('aria-hidden', 'true')
    await expect(menuButton).toHaveAttribute('aria-expanded', 'false')
  })

  test('Focus management and visual indicators', async ({ page }) => {
    // Test focus ring visibility on desktop navigation
    await page.setViewportSize({ width: 1024, height: 768 })
    
    const navLinks = page.locator('nav[aria-label="Main navigation"] a')
    const firstLink = navLinks.first()
    
    // Focus first link
    await firstLink.focus()
    
    // Check focus styles are applied (focus ring should be visible)
    const focusStyles = await firstLink.evaluate((el) => {
      const styles = window.getComputedStyle(el, ':focus')
      return {
        outline: styles.outline,
        boxShadow: styles.boxShadow,
        outlineOffset: styles.outlineOffset
      }
    })
    
    // Should have focus indicators (outline or box-shadow)
    const hasFocusIndicator = focusStyles.outline !== 'none' || 
                             focusStyles.boxShadow !== 'none' ||
                             focusStyles.boxShadow.includes('ring')
    
    expect(hasFocusIndicator).toBe(true)
  })

  test('Screen reader announcements', async ({ page }) => {
    await page.setViewportSize({ width: 375, height: 667 })
    
    // Check for screen reader only text
    const srOnlyElements = page.locator('.sr-only')
    const srCount = await srOnlyElements.count()
    expect(srCount).toBeGreaterThan(0)
    
    // Check mobile menu button has screen reader text
    const menuButton = page.locator('.mobile-menu-button')
    const srText = menuButton.locator('.sr-only')
    await expect(srText).toHaveText('Open navigation menu')

    // Open menu and check updated screen reader text
    await menuButton.click()
    await page.waitForTimeout(300)

    const updatedSrText = menuButton.locator('.sr-only')
    await expect(updatedSrText).toHaveText('Close navigation menu')
  })

  test('Navigation landmarks and structure', async ({ page }) => {
    // Check for proper landmark structure
    const header = page.locator('header[role="banner"]')
    await expect(header).toBeVisible()
    
    const nav = page.locator('nav[role="navigation"]')
    await expect(nav).toBeVisible()
    
    const main = page.locator('main#main-content')
    await expect(main).toBeVisible()
    await expect(main).toHaveAttribute('tabindex', '-1')
    
    // Check navigation has proper aria-label
    const mainNav = page.locator('nav[aria-label="Main navigation"]')
    await expect(mainNav).toBeVisible()
  })

  test('Color contrast and visual accessibility', async ({ page }) => {
    // Test navigation link color contrast
    const navLinks = page.locator('nav[aria-label="Main navigation"] a')
    
    if (await navLinks.count() > 0) {
      const firstLink = navLinks.first()
      
      // Get computed styles
      const styles = await firstLink.evaluate((el) => {
        const computed = window.getComputedStyle(el)
        return {
          color: computed.color,
          backgroundColor: computed.backgroundColor,
          fontSize: computed.fontSize
        }
      })
      
      // Basic checks for readable styles
      expect(styles.color).not.toBe('transparent')
      expect(styles.fontSize).not.toBe('0px')
    }
  })

  test('Responsive navigation behavior', async ({ page }) => {
    // Test navigation at different viewport sizes
    const viewports = [
      { width: 320, height: 568 },  // Small mobile
      { width: 768, height: 1024 }, // Tablet
      { width: 1024, height: 768 }, // Desktop
      { width: 1920, height: 1080 } // Large desktop
    ]
    
    for (const viewport of viewports) {
      await page.setViewportSize(viewport)
      await page.waitForTimeout(300)
      
      if (viewport.width < 768) {
        // Mobile: hamburger menu should be visible
        const menuButton = page.locator('.mobile-menu-button')
        await expect(menuButton).toBeVisible()

        const desktopNav = page.locator('nav[aria-label="Main navigation"]')
        await expect(desktopNav).toBeHidden()
      } else {
        // Desktop: navigation links should be visible
        const desktopNav = page.locator('nav[aria-label="Main navigation"]')
        await expect(desktopNav).toBeVisible()

        const menuButton = page.locator('.mobile-menu-button')
        await expect(menuButton).toBeHidden()
      }
    }
  })
})
