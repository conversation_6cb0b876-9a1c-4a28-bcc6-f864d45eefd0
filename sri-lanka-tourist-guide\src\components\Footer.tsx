import React from 'react'
import { Link } from 'react-router-dom'

const Footer: React.FC = () => {
  return (
    <footer className="bg-gray-900 text-white">
      <div className="container-max section-padding">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
          {/* Brand */}
          <div className="col-span-1 md:col-span-2">
            <h3 className="text-2xl font-bold text-gradient mb-4">Sri Lanka Guide</h3>
            <p className="text-gray-300 mb-4">
              Your comprehensive travel companion for exploring Sri Lanka in 2025.
              Discover ancient temples, pristine beaches, wildlife adventures, and cultural treasures
              in the Pearl of the Indian Ocean.
            </p>
            <div className="mb-4">
              <p className="text-sm text-gray-400 mb-2">
                <strong className="text-gray-300">Last Updated:</strong> January 2025
              </p>
              <p className="text-sm text-gray-400 mb-2">
                <strong className="text-gray-300">Coverage:</strong> 15+ Destinations, 50+ Articles, Cultural Insights
              </p>
              <p className="text-sm text-gray-400">
                <strong className="text-gray-300">Powered by:</strong> <span className="text-primary-400">Tera Works</span>
              </p>
            </div>
          </div>

          {/* Explore */}
          <div>
            <h4 className="text-lg font-semibold mb-4">Explore</h4>
            <ul className="space-y-2">
              <li>
                <Link to="/destinations" className="text-gray-300 hover:text-white transition-colors inline-block py-2 min-h-[44px] flex items-center">
                  Top Destinations
                </Link>
              </li>
              <li>
                <Link to="/activities" className="text-gray-300 hover:text-white transition-colors inline-block py-2 min-h-[44px] flex items-center">
                  Activities & Tours
                </Link>
              </li>
              <li>
                <Link to="/culture" className="text-gray-300 hover:text-white transition-colors">
                  Culture & Heritage
                </Link>
              </li>
              <li>
                <Link to="/articles" className="text-gray-300 hover:text-white transition-colors">
                  Travel Articles
                </Link>
              </li>
              <li>
                <Link to="/travel-tips" className="text-gray-300 hover:text-white transition-colors">
                  Travel Tips
                </Link>
              </li>
            </ul>
          </div>

          {/* Resources & Contact */}
          <div>
            <h4 className="text-lg font-semibold mb-4">Resources</h4>
            <ul className="space-y-2 text-gray-300 mb-6">
              <li>
                <Link to="/contact" className="hover:text-white transition-colors">
                  Contact Us
                </Link>
              </li>
              <li>Travel Planning Guide</li>
              <li>Weather Information</li>
              <li>Visa Requirements</li>
              <li>Currency & Costs</li>
            </ul>

            <div className="text-sm">
              <p className="text-gray-400 mb-1">
                <strong className="text-gray-300">Email:</strong> <EMAIL>
              </p>
              <p className="text-gray-400 mb-1">
                <strong className="text-gray-300">Phone:</strong> +94 11 123 4567
              </p>
              <p className="text-gray-400">
                <strong className="text-gray-300">Location:</strong> Colombo, Sri Lanka
              </p>
            </div>
          </div>
        </div>

        <div className="border-t border-gray-800 mt-8 pt-8">
          <div className="flex flex-col md:flex-row justify-between items-center text-gray-400 text-sm">
            <div className="mb-4 md:mb-0">
              <p>&copy; 2025 Sri Lanka Tourist Guide. All rights reserved.</p>
              <p className="mt-1">Comprehensive travel information for Sri Lanka tourism.</p>
            </div>
            <div className="flex space-x-6">
              <span>Privacy Policy</span>
              <span>Terms of Service</span>
              <span>Sitemap</span>
            </div>
          </div>
        </div>
      </div>
    </footer>
  )
}

export default Footer
