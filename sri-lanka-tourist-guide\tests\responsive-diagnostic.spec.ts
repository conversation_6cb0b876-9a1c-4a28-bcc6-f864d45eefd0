import { test, expect } from '@playwright/test'

test.describe('Responsive Layout Diagnostic', () => {
  test('Identify specific layout issues on mobile', async ({ page }) => {
    await page.goto('/')
    await page.waitForLoadState('networkidle')
    
    // Set mobile viewport
    await page.setViewportSize({ width: 375, height: 667 })
    await page.waitForTimeout(500)
    
    // Take screenshot for visual inspection
    await page.screenshot({
      path: 'test-results/mobile-layout-diagnostic.png',
      fullPage: true
    })
    
    // Check for common responsive issues
    const diagnostics = await page.evaluate(() => {
      const issues: string[] = []
      const elements = document.querySelectorAll('*')
      
      elements.forEach((el, index) => {
        const rect = el.getBoundingClientRect()
        const styles = window.getComputedStyle(el)
        
        // Check for horizontal overflow
        if (rect.width > window.innerWidth) {
          issues.push(`Element ${index} (${el.tagName}.${el.className}) extends beyond viewport: ${rect.width}px > ${window.innerWidth}px`)
        }
        
        // Check for fixed widths that might cause issues
        if (styles.width && styles.width.includes('px') && !styles.width.includes('max-width')) {
          const width = parseInt(styles.width)
          if (width > window.innerWidth) {
            issues.push(`Element ${index} (${el.tagName}.${el.className}) has fixed width larger than viewport: ${width}px`)
          }
        }
        
        // Check for elements with negative margins causing layout issues
        const marginLeft = parseInt(styles.marginLeft) || 0
        const marginRight = parseInt(styles.marginRight) || 0
        if (marginLeft < -50 || marginRight < -50) {
          issues.push(`Element ${index} (${el.tagName}.${el.className}) has large negative margins: left=${marginLeft}px, right=${marginRight}px`)
        }
        
        // Check for absolute positioned elements that might overlap
        if (styles.position === 'absolute' || styles.position === 'fixed') {
          const zIndex = styles.zIndex
          issues.push(`Positioned element ${index} (${el.tagName}.${el.className}) - position: ${styles.position}, z-index: ${zIndex}`)
        }
      })
      
      return {
        viewportWidth: window.innerWidth,
        viewportHeight: window.innerHeight,
        totalElements: elements.length,
        issues: issues.slice(0, 20) // Limit to first 20 issues
      }
    })
    
    console.log('Mobile Layout Diagnostics:', diagnostics)
    
    // Check specific components that commonly cause issues
    const componentChecks = await page.evaluate(() => {
      const checks = {
        header: {
          exists: !!document.querySelector('header'),
          width: document.querySelector('header')?.getBoundingClientRect().width || 0,
          overflows: (document.querySelector('header')?.getBoundingClientRect().width || 0) > window.innerWidth
        },
        navigation: {
          exists: !!document.querySelector('nav'),
          mobileMenuVisible: !!document.querySelector('[data-testid="mobile-menu"]') && 
                            window.getComputedStyle(document.querySelector('[data-testid="mobile-menu"]')!).display !== 'none'
        },
        mainContent: {
          exists: !!document.querySelector('main'),
          width: document.querySelector('main')?.getBoundingClientRect().width || 0,
          overflows: (document.querySelector('main')?.getBoundingClientRect().width || 0) > window.innerWidth
        },
        images: {
          total: document.querySelectorAll('img').length,
          oversized: Array.from(document.querySelectorAll('img')).filter(img => 
            img.getBoundingClientRect().width > window.innerWidth
          ).length
        },
        containers: {
          withFixedWidth: Array.from(document.querySelectorAll('*')).filter(el => {
            const styles = window.getComputedStyle(el)
            return styles.width && styles.width.includes('px') && parseInt(styles.width) > window.innerWidth
          }).length
        }
      }
      
      return checks
    })
    
    console.log('Component Checks:', componentChecks)
    
    // Assert basic responsive requirements
    expect(componentChecks.header.overflows).toBe(false)
    expect(componentChecks.mainContent.overflows).toBe(false)
    expect(componentChecks.images.oversized).toBeLessThan(3)
    expect(componentChecks.containers.withFixedWidth).toBeLessThan(5)
  })

  test('Check tablet layout issues', async ({ page }) => {
    await page.goto('/')
    await page.waitForLoadState('networkidle')
    
    // Set tablet viewport
    await page.setViewportSize({ width: 768, height: 1024 })
    await page.waitForTimeout(500)
    
    // Take screenshot
    await page.screenshot({
      path: 'test-results/tablet-layout-diagnostic.png',
      fullPage: true
    })
    
    const tabletChecks = await page.evaluate(() => {
      return {
        viewportWidth: window.innerWidth,
        elementsOverflowing: Array.from(document.querySelectorAll('*')).filter(el => 
          el.getBoundingClientRect().width > window.innerWidth
        ).length,
        navigationVisible: window.getComputedStyle(document.querySelector('nav[aria-label="Main navigation"]')!).display !== 'none',
        mobileMenuHidden: (() => {
          const button = document.querySelector('.mobile-menu-button')
          if (!button) return true
          const rect = button.getBoundingClientRect()
          return rect.width === 0 && rect.height === 0
        })()
      }
    })
    
    console.log('Tablet Layout Checks:', tabletChecks)
    
    expect(tabletChecks.elementsOverflowing).toBeLessThan(5)
    expect(tabletChecks.navigationVisible).toBe(true)
    expect(tabletChecks.mobileMenuHidden).toBe(true)
  })

  test('Check desktop layout issues', async ({ page }) => {
    await page.goto('/')
    await page.waitForLoadState('networkidle')
    
    // Set desktop viewport
    await page.setViewportSize({ width: 1920, height: 1080 })
    await page.waitForTimeout(500)
    
    // Take screenshot
    await page.screenshot({
      path: 'test-results/desktop-layout-diagnostic.png',
      fullPage: true
    })
    
    const desktopChecks = await page.evaluate(() => {
      const maxWidth = Math.max(...Array.from(document.querySelectorAll('*')).map(el => 
        el.getBoundingClientRect().width
      ))
      
      return {
        viewportWidth: window.innerWidth,
        maxElementWidth: maxWidth,
        elementsOverflowing: Array.from(document.querySelectorAll('*')).filter(el => 
          el.getBoundingClientRect().width > window.innerWidth
        ).length,
        containerMaxWidth: document.querySelector('.container-max')?.getBoundingClientRect().width || 0
      }
    })
    
    console.log('Desktop Layout Checks:', desktopChecks)
    
    expect(desktopChecks.elementsOverflowing).toBe(0)
    expect(desktopChecks.maxElementWidth).toBeLessThanOrEqual(desktopChecks.viewportWidth)
  })

  test('Check specific pages for responsive issues', async ({ page }) => {
    const pages = ['/destinations', '/activities', '/culture', '/articles']
    
    for (const pagePath of pages) {
      await page.goto(pagePath)
      await page.waitForLoadState('networkidle')
      
      // Test mobile
      await page.setViewportSize({ width: 375, height: 667 })
      await page.waitForTimeout(300)
      
      const mobileIssues = await page.evaluate(() => {
        return Array.from(document.querySelectorAll('*')).filter(el => 
          el.getBoundingClientRect().width > window.innerWidth
        ).length
      })
      
      console.log(`${pagePath} mobile overflow elements: ${mobileIssues}`)
      
      // Test desktop
      await page.setViewportSize({ width: 1920, height: 1080 })
      await page.waitForTimeout(300)
      
      const desktopIssues = await page.evaluate(() => {
        return Array.from(document.querySelectorAll('*')).filter(el => 
          el.getBoundingClientRect().width > window.innerWidth
        ).length
      })
      
      console.log(`${pagePath} desktop overflow elements: ${desktopIssues}`)
      
      // Basic assertions
      expect(mobileIssues).toBeLessThan(10) // Allow some tolerance for now
      expect(desktopIssues).toBe(0)
    }
  })

  test('Identify CSS issues causing layout problems', async ({ page }) => {
    await page.goto('/')
    await page.waitForLoadState('networkidle')
    await page.setViewportSize({ width: 375, height: 667 })
    
    const cssIssues = await page.evaluate(() => {
      const issues: string[] = []
      const elements = document.querySelectorAll('*')
      
      elements.forEach((el, index) => {
        const styles = window.getComputedStyle(el)
        const rect = el.getBoundingClientRect()
        
        // Check for common CSS issues
        if (styles.minWidth && parseInt(styles.minWidth) > window.innerWidth) {
          issues.push(`Element ${index} has min-width larger than viewport: ${styles.minWidth}`)
        }
        
        if (styles.width === '100vw' && rect.width > window.innerWidth) {
          issues.push(`Element ${index} uses 100vw but overflows viewport`)
        }
        
        if (styles.position === 'absolute' && (parseInt(styles.left) < 0 || parseInt(styles.right) < 0)) {
          issues.push(`Element ${index} has negative positioning: left=${styles.left}, right=${styles.right}`)
        }
        
        if (styles.transform && styles.transform.includes('translate') && rect.width > window.innerWidth) {
          issues.push(`Element ${index} uses transform and overflows: ${styles.transform}`)
        }
        
        // Check for flexbox issues
        if (styles.display === 'flex' && styles.flexWrap === 'nowrap' && rect.width > window.innerWidth) {
          issues.push(`Element ${index} is flex nowrap and overflows viewport`)
        }
      })
      
      return issues.slice(0, 15)
    })
    
    console.log('CSS Issues Found:', cssIssues)
    
    // Log for debugging but don't fail the test yet
    expect(cssIssues.length).toBeLessThan(50) // Very lenient for diagnostic purposes
  })
})
