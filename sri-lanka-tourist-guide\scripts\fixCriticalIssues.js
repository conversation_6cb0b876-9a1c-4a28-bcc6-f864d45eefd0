#!/usr/bin/env node

const fs = require('fs')
const path = require('path')

// Critical issues fix script for Sri Lanka Tourist Guide
console.log('🔧 Starting critical issues fix process...')

const PROJECT_ROOT = path.join(__dirname, '..')

// List of critical fixes to apply
const CRITICAL_FIXES = [
  {
    name: 'Verify ArticleDetail.tsx syntax',
    file: 'src/pages/ArticleDetail.tsx',
    action: 'verify'
  },
  {
    name: 'Check for leftover content in components',
    file: 'src/components',
    action: 'scan'
  },
  {
    name: 'Validate article data structure',
    file: 'src/data/articles.ts',
    action: 'validate'
  },
  {
    name: 'Fix import statements',
    file: 'src',
    action: 'fixImports'
  },
  {
    name: 'Ensure proper TypeScript configuration',
    file: 'tsconfig.json',
    action: 'verify'
  }
]

// Utility functions
function fileExists(filePath) {
  return fs.existsSync(path.join(PROJECT_ROOT, filePath))
}

function readFile(filePath) {
  try {
    return fs.readFileSync(path.join(PROJECT_ROOT, filePath), 'utf-8')
  } catch (error) {
    console.error(`❌ Error reading file ${filePath}:`, error.message)
    return null
  }
}

function writeFile(filePath, content) {
  try {
    fs.writeFileSync(path.join(PROJECT_ROOT, filePath), content)
    console.log(`✅ Updated file: ${filePath}`)
    return true
  } catch (error) {
    console.error(`❌ Error writing file ${filePath}:`, error.message)
    return false
  }
}

// Fix functions
function verifyArticleDetailSyntax() {
  console.log('🔍 Verifying ArticleDetail.tsx syntax...')
  
  const filePath = 'src/pages/ArticleDetail.tsx'
  if (!fileExists(filePath)) {
    console.error(`❌ File not found: ${filePath}`)
    return false
  }
  
  const content = readFile(filePath)
  if (!content) return false
  
  // Check for common syntax issues
  const issues = []
  
  // Check for leftover article data
  if (content.includes("'sigiriya-complete-guide': {")) {
    issues.push('Leftover article data found in component')
  }
  
  // Check for missing imports
  if (content.includes('useParams') && !content.includes("import { useParams }")) {
    issues.push('Missing useParams import')
  }
  
  // Check for proper React imports
  if (!content.includes("import React") && !content.includes("import { ")) {
    issues.push('Missing React imports')
  }
  
  // Check for unclosed brackets/braces
  const openBraces = (content.match(/{/g) || []).length
  const closeBraces = (content.match(/}/g) || []).length
  if (openBraces !== closeBraces) {
    issues.push(`Mismatched braces: ${openBraces} open, ${closeBraces} close`)
  }
  
  if (issues.length > 0) {
    console.error('❌ Syntax issues found in ArticleDetail.tsx:')
    issues.forEach(issue => console.error(`   - ${issue}`))
    return false
  }
  
  console.log('✅ ArticleDetail.tsx syntax is valid')
  return true
}

function scanForLeftoverContent() {
  console.log('🔍 Scanning for leftover content in components...')
  
  const componentsDir = path.join(PROJECT_ROOT, 'src/components')
  if (!fs.existsSync(componentsDir)) {
    console.error('❌ Components directory not found')
    return false
  }
  
  const files = fs.readdirSync(componentsDir, { recursive: true })
    .filter(file => file.endsWith('.tsx') || file.endsWith('.ts'))
  
  let issuesFound = false
  
  files.forEach(file => {
    const filePath = path.join(componentsDir, file)
    const content = fs.readFileSync(filePath, 'utf-8')
    
    // Check for article data in components
    if (content.includes("'sigiriya-complete-guide'") || 
        content.includes("'kandy-cultural-capital'") ||
        content.includes("id: 'sigiriya")) {
      console.error(`❌ Found leftover article data in: ${file}`)
      issuesFound = true
    }
    
    // Check for incomplete exports
    if (content.includes('export {') && !content.includes('}')) {
      console.error(`❌ Incomplete export statement in: ${file}`)
      issuesFound = true
    }
  })
  
  if (!issuesFound) {
    console.log('✅ No leftover content found in components')
  }
  
  return !issuesFound
}

function validateArticleDataStructure() {
  console.log('🔍 Validating article data structure...')
  
  const filePath = 'src/data/articles.ts'
  if (!fileExists(filePath)) {
    console.error(`❌ File not found: ${filePath}`)
    return false
  }
  
  const content = readFile(filePath)
  if (!content) return false
  
  // Check for proper TypeScript interface
  if (!content.includes('export interface Article')) {
    console.error('❌ Missing Article interface export')
    return false
  }
  
  // Check for proper database export
  if (!content.includes('export const articlesDatabase')) {
    console.error('❌ Missing articlesDatabase export')
    return false
  }
  
  // Check for proper structure
  const requiredFields = ['id', 'title', 'content', 'excerpt', 'category']
  const interfaceMatch = content.match(/export interface Article \{([\s\S]*?)\}/m)
  
  if (interfaceMatch) {
    const interfaceContent = interfaceMatch[1]
    const missingFields = requiredFields.filter(field => 
      !interfaceContent.includes(`${field}:`)
    )
    
    if (missingFields.length > 0) {
      console.error('❌ Missing required fields in Article interface:', missingFields)
      return false
    }
  }
  
  console.log('✅ Article data structure is valid')
  return true
}

function fixImportStatements() {
  console.log('🔍 Checking and fixing import statements...')
  
  const srcDir = path.join(PROJECT_ROOT, 'src')
  const files = []
  
  // Recursively find all TypeScript files
  function findTsFiles(dir) {
    const items = fs.readdirSync(dir)
    items.forEach(item => {
      const fullPath = path.join(dir, item)
      const stat = fs.statSync(fullPath)
      
      if (stat.isDirectory()) {
        findTsFiles(fullPath)
      } else if (item.endsWith('.tsx') || item.endsWith('.ts')) {
        files.push(fullPath)
      }
    })
  }
  
  findTsFiles(srcDir)
  
  let fixesApplied = 0
  
  files.forEach(filePath => {
    const content = fs.readFileSync(filePath, 'utf-8')
    let updatedContent = content
    let fileChanged = false
    
    // Fix common import issues
    
    // Fix React imports
    if (content.includes('useState') || content.includes('useEffect')) {
      if (!content.includes("import React") && !content.includes("import { ")) {
        updatedContent = `import React from 'react'\n${updatedContent}`
        fileChanged = true
      }
    }
    
    // Fix router imports
    if (content.includes('useParams') || content.includes('useNavigate')) {
      if (!content.includes("from 'react-router-dom'")) {
        const routerImports = []
        if (content.includes('useParams')) routerImports.push('useParams')
        if (content.includes('useNavigate')) routerImports.push('useNavigate')
        if (content.includes('Link')) routerImports.push('Link')
        
        if (routerImports.length > 0) {
          const importLine = `import { ${routerImports.join(', ')} } from 'react-router-dom'\n`
          updatedContent = importLine + updatedContent
          fileChanged = true
        }
      }
    }
    
    if (fileChanged) {
      fs.writeFileSync(filePath, updatedContent)
      fixesApplied++
      console.log(`✅ Fixed imports in: ${path.relative(PROJECT_ROOT, filePath)}`)
    }
  })
  
  console.log(`✅ Import fixes applied to ${fixesApplied} files`)
  return true
}

function verifyTypeScriptConfig() {
  console.log('🔍 Verifying TypeScript configuration...')
  
  const filePath = 'tsconfig.json'
  if (!fileExists(filePath)) {
    console.error(`❌ File not found: ${filePath}`)
    return false
  }
  
  const content = readFile(filePath)
  if (!content) return false
  
  try {
    const config = JSON.parse(content)
    
    // Check for essential compiler options
    const requiredOptions = {
      'target': 'ES2020',
      'lib': ['ES2020', 'DOM', 'DOM.Iterable'],
      'allowJs': false,
      'skipLibCheck': true,
      'esModuleInterop': false,
      'allowSyntheticDefaultImports': true,
      'strict': true,
      'forceConsistentCasingInFileNames': true,
      'module': 'ESNext',
      'moduleResolution': 'bundler',
      'resolveJsonModule': true,
      'isolatedModules': true,
      'noEmit': true,
      'jsx': 'react-jsx'
    }
    
    const compilerOptions = config.compilerOptions || {}
    const missingOptions = []
    
    Object.entries(requiredOptions).forEach(([key, value]) => {
      if (!(key in compilerOptions)) {
        missingOptions.push(key)
      }
    })
    
    if (missingOptions.length > 0) {
      console.warn('⚠️ Missing TypeScript compiler options:', missingOptions)
    } else {
      console.log('✅ TypeScript configuration is valid')
    }
    
    return true
  } catch (error) {
    console.error('❌ Invalid JSON in tsconfig.json:', error.message)
    return false
  }
}

// Main execution
function main() {
  console.log('🚀 Sri Lanka Tourist Guide - Critical Issues Fix')
  console.log('=' .repeat(50))
  
  const results = []
  
  // Execute all fixes
  results.push({ name: 'ArticleDetail.tsx syntax', success: verifyArticleDetailSyntax() })
  results.push({ name: 'Leftover content scan', success: scanForLeftoverContent() })
  results.push({ name: 'Article data validation', success: validateArticleDataStructure() })
  results.push({ name: 'Import statements fix', success: fixImportStatements() })
  results.push({ name: 'TypeScript config', success: verifyTypeScriptConfig() })
  
  // Summary
  console.log('\n📊 Fix Results Summary:')
  console.log('=' .repeat(30))
  
  const successful = results.filter(r => r.success).length
  const total = results.length
  
  results.forEach(result => {
    const status = result.success ? '✅' : '❌'
    console.log(`${status} ${result.name}`)
  })
  
  console.log(`\n🎯 Success Rate: ${successful}/${total} (${Math.round(successful/total*100)}%)`)
  
  if (successful === total) {
    console.log('🎉 All critical issues have been resolved!')
    console.log('✅ The application should now build and run correctly.')
  } else {
    console.log('⚠️ Some issues remain. Please review the errors above.')
    process.exit(1)
  }
}

if (require.main === module) {
  main()
}

module.exports = {
  verifyArticleDetailSyntax,
  scanForLeftoverContent,
  validateArticleDataStructure,
  fixImportStatements,
  verifyTypeScriptConfig
}
