import React, { useEffect, useState } from 'react'
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON> } from 'react-leaflet'
import { Icon } from 'leaflet'
import { Activity } from '@/data/activities'
import { motion } from 'framer-motion'
import {
  ClockIcon,
  CurrencyDollarIcon,
  MapPinIcon
} from '@heroicons/react/24/outline'

// Fix for default markers in React Leaflet
delete (Icon.Default.prototype as any)._getIconUrl
Icon.Default.mergeOptions({
  iconRetinaUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon-2x.png',
  iconUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon.png',
  shadowUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-shadow.png',
})

// Create custom icons for different activity categories
const createActivityIcon = (category: string) => {
  const iconColors = {
    'adventure': '#ef4444', // red
    'cultural': '#8b5cf6', // purple
    'wildlife': '#10b981', // green
    'water-sports': '#3b82f6', // blue
    'wellness': '#f59e0b', // amber
    'food': '#f97316' // orange
  }

  const color = iconColors[category as keyof typeof iconColors] || '#6b7280'
  
  return new Icon({
    iconUrl: `data:image/svg+xml;base64,${btoa(`
      <svg width="25" height="41" viewBox="0 0 25 41" xmlns="http://www.w3.org/2000/svg">
        <path d="M12.5 0C5.6 0 0 5.6 0 12.5C0 19.4 12.5 41 12.5 41S25 19.4 25 12.5C25 5.6 19.4 0 12.5 0Z" fill="${color}"/>
        <circle cx="12.5" cy="12.5" r="6" fill="white"/>
      </svg>
    `)}`,
    iconSize: [25, 41],
    iconAnchor: [12, 41],
    popupAnchor: [1, -34],
  })
}

interface ActivitiesMapProps {
  activities: Activity[]
  selectedCategory?: string
  height?: string
  className?: string
}

const ActivitiesMap: React.FC<ActivitiesMapProps> = ({ 
  activities, 
  selectedCategory = 'all',
  height = '500px',
  className = ''
}) => {
  const [mapReady, setMapReady] = useState(false)
  const [mapError, setMapError] = useState<string | null>(null)

  // Filter activities based on selected category
  const filteredActivities = activities.filter(activity => 
    selectedCategory === 'all' || activity.category === selectedCategory
  )

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'easy': return 'text-green-600 bg-green-100'
      case 'moderate': return 'text-yellow-600 bg-yellow-100'
      case 'challenging': return 'text-red-600 bg-red-100'
      default: return 'text-gray-600 bg-gray-100'
    }
  }

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'adventure': return '🏔️'
      case 'cultural': return '🏛️'
      case 'wildlife': return '🦁'
      case 'water-sports': return '🏄‍♂️'
      case 'wellness': return '🧘‍♀️'
      case 'food': return '🍽️'
      default: return '🎯'
    }
  }

  useEffect(() => {
    // Import Leaflet CSS dynamically
    const link = document.createElement('link')
    link.rel = 'stylesheet'
    link.href = 'https://unpkg.com/leaflet@1.9.4/dist/leaflet.css'
    document.head.appendChild(link)

    return () => {
      document.head.removeChild(link)
    }
  }, [])

  if (mapError) {
    return (
      <div className={`flex items-center justify-center bg-gray-100 rounded-lg ${className}`} style={{ height }}>
        <div className="text-center p-8">
          <div className="text-4xl mb-4">🗺️</div>
          <h3 className="text-lg font-semibold text-gray-900 mb-2">Map Unavailable</h3>
          <p className="text-gray-600">Unable to load the interactive map. Please try refreshing the page.</p>
        </div>
      </div>
    )
  }

  return (
    <motion.div 
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6 }}
      className={`relative rounded-lg overflow-hidden shadow-lg ${className}`}
      style={{ height }}
    >
      {!mapReady && (
        <div className="absolute inset-0 flex items-center justify-center bg-gray-100 z-10">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto mb-4"></div>
            <p className="text-gray-600">Loading interactive map...</p>
          </div>
        </div>
      )}

      <MapContainer
        center={[7.8731, 80.7718] as [number, number]} // Center of Sri Lanka
        zoom={7}
        style={{ height: '100%', width: '100%' }}
        className="z-0"
        whenReady={() => {
          setMapReady(true)
          setMapError(null)
        }}
      >
        <TileLayer
          attribution='&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
          url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"
        />

        {filteredActivities.map((activity) => {
          if (!activity.coordinates) return null
          
          return (
            <Marker
              key={activity.id}
              position={[activity.coordinates.lat, activity.coordinates.lng] as [number, number]}
              icon={createActivityIcon(activity.category)}
            >
              <Popup maxWidth={350} className="activity-popup">
                <div className="p-3">
                  {/* Header */}
                  <div className="flex items-start justify-between mb-3">
                    <h3 className="text-lg font-bold text-gray-900 pr-2 leading-tight">
                      {activity.name}
                    </h3>
                    <span className="flex-shrink-0 text-2xl">
                      {getCategoryIcon(activity.category)}
                    </span>
                  </div>

                  {/* Description */}
                  <p className="text-gray-600 text-sm mb-3 line-clamp-2">
                    {activity.description}
                  </p>

                  {/* Meta Information */}
                  <div className="grid grid-cols-2 gap-2 text-xs text-gray-500 mb-3">
                    <div className="flex items-center">
                      <MapPinIcon className="h-3 w-3 mr-1" />
                      <span className="truncate">{activity.location}</span>
                    </div>
                    <div className="flex items-center">
                      <ClockIcon className="h-3 w-3 mr-1" />
                      <span>{activity.duration}</span>
                    </div>
                  </div>

                  {/* Price and Difficulty */}
                  <div className="flex items-center justify-between mb-3">
                    <div className="flex items-center text-sm">
                      <CurrencyDollarIcon className="h-4 w-4 text-green-600 mr-1" />
                      <span className="font-semibold text-green-600">
                        ${activity.price.min}-${activity.price.max}
                      </span>
                    </div>
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${getDifficultyColor(activity.difficulty)}`}>
                      {activity.difficulty}
                    </span>
                  </div>

                  {/* Highlights */}
                  <div className="mb-3">
                    <h4 className="text-sm font-semibold text-gray-900 mb-1">Highlights:</h4>
                    <div className="flex flex-wrap gap-1">
                      {activity.highlights.slice(0, 3).map((highlight, index) => (
                        <span
                          key={index}
                          className="inline-block px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded"
                        >
                          {highlight}
                        </span>
                      ))}
                      {activity.highlights.length > 3 && (
                        <span className="inline-block px-2 py-1 bg-gray-100 text-gray-600 text-xs rounded">
                          +{activity.highlights.length - 3} more
                        </span>
                      )}
                    </div>
                  </div>

                  {/* Best Time */}
                  <div className="text-xs text-gray-500 border-t pt-2">
                    <strong>Best time:</strong> {activity.bestTime}
                  </div>
                </div>
              </Popup>
            </Marker>
          )
        })}
      </MapContainer>

      {/* Map Legend */}
      <div className="absolute bottom-4 left-4 bg-white bg-opacity-95 rounded-lg p-3 shadow-lg z-10">
        <h4 className="text-sm font-semibold text-gray-900 mb-2">Activity Categories</h4>
        <div className="grid grid-cols-2 gap-1 text-xs">
          {[
            { category: 'adventure', label: 'Adventure', icon: '🏔️' },
            { category: 'cultural', label: 'Cultural', icon: '🏛️' },
            { category: 'wildlife', label: 'Wildlife', icon: '🦁' },
            { category: 'water-sports', label: 'Water Sports', icon: '🏄‍♂️' },
            { category: 'wellness', label: 'Wellness', icon: '🧘‍♀️' },
            { category: 'food', label: 'Food', icon: '🍽️' }
          ].map(({ category, label }) => (
            <div key={category} className="flex items-center space-x-1">
              <div 
                className="w-3 h-3 rounded-full border border-gray-300"
                style={{ 
                  backgroundColor: {
                    'adventure': '#ef4444',
                    'cultural': '#8b5cf6',
                    'wildlife': '#10b981',
                    'water-sports': '#3b82f6',
                    'wellness': '#f59e0b',
                    'food': '#f97316'
                  }[category] || '#6b7280'
                }}
              />
              <span className="text-gray-700">{label}</span>
            </div>
          ))}
        </div>
      </div>

      {/* Activity Count */}
      <div className="absolute top-4 right-4 bg-white bg-opacity-95 rounded-lg px-3 py-2 shadow-lg z-10">
        <div className="text-sm font-semibold text-gray-900">
          {filteredActivities.length} {filteredActivities.length === 1 ? 'Activity' : 'Activities'}
        </div>
        {selectedCategory !== 'all' && (
          <div className="text-xs text-gray-600">
            {selectedCategory.replace('-', ' ')} category
          </div>
        )}
      </div>
    </motion.div>
  )
}

export default ActivitiesMap
