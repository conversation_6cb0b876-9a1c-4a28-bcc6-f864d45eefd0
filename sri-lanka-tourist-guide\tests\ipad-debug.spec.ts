import { test, expect } from '@playwright/test'

test.describe('iPad Layout Debug', () => {
  test('Identify horizontal scroll and overflow issues on iPad', async ({ page }) => {
    await page.goto('/')
    await page.waitForLoadState('networkidle')
    
    // Set iPad viewport
    await page.setViewportSize({ width: 768, height: 1024 })
    await page.waitForTimeout(500)
    
    // Check for horizontal scrollbars
    const scrollInfo = await page.evaluate(() => {
      return {
        documentWidth: document.documentElement.scrollWidth,
        clientWidth: document.documentElement.clientWidth,
        hasHorizontalScroll: document.documentElement.scrollWidth > document.documentElement.clientWidth,
        bodyWidth: document.body.scrollWidth,
        bodyClientWidth: document.body.clientWidth
      }
    })
    
    console.log('iPad Scroll Info:', scrollInfo)
    
    // Find overflowing elements
    const overflowingElements = await page.evaluate(() => {
      const elements = Array.from(document.querySelectorAll('*'))
      const overflowing = elements.filter(el => {
        const rect = el.getBoundingClientRect()
        return rect.right > window.innerWidth || rect.left < 0
      })
      
      return overflowing.map(el => ({
        tag: el.tagName,
        className: el.className,
        id: el.id,
        width: el.getBoundingClientRect().width,
        right: el.getBoundingClientRect().right,
        left: el.getBoundingClientRect().left,
        viewportWidth: window.innerWidth,
        overflow: el.getBoundingClientRect().right - window.innerWidth,
        textContent: el.textContent?.slice(0, 50),
        styles: {
          position: window.getComputedStyle(el).position,
          display: window.getComputedStyle(el).display,
          width: window.getComputedStyle(el).width,
          minWidth: window.getComputedStyle(el).minWidth,
          maxWidth: window.getComputedStyle(el).maxWidth,
          transform: window.getComputedStyle(el).transform,
          overflow: window.getComputedStyle(el).overflow
        }
      }))
    })
    
    console.log('Overflowing Elements on iPad:', overflowingElements)
    
    // Take screenshot for visual inspection
    await page.screenshot({
      path: 'test-results/ipad-overflow-debug.png',
      fullPage: true
    })
    
    // Check specific components that might cause issues
    const componentWidths = await page.evaluate(() => {
      const components = {
        body: document.body,
        html: document.documentElement,
        header: document.querySelector('header'),
        nav: document.querySelector('nav'),
        main: document.querySelector('main'),
        sections: document.querySelectorAll('section'),
        containers: document.querySelectorAll('.container-max'),
        grids: document.querySelectorAll('[class*="grid"]'),
        flexboxes: document.querySelectorAll('[class*="flex"]')
      }
      
      const results: any = {}
      
      Object.entries(components).forEach(([name, element]) => {
        if (element) {
          if (element instanceof NodeList) {
            results[name] = Array.from(element).map((el, index) => ({
              index,
              width: el.getBoundingClientRect().width,
              overflows: el.getBoundingClientRect().right > window.innerWidth,
              className: el.className,
              computedWidth: window.getComputedStyle(el).width
            }))
          } else {
            results[name] = {
              width: element.getBoundingClientRect().width,
              overflows: element.getBoundingClientRect().right > window.innerWidth,
              className: element.className,
              computedWidth: window.getComputedStyle(element).width
            }
          }
        }
      })
      
      return results
    })
    
    console.log('Component Widths on iPad:', componentWidths)
    
    expect(overflowingElements.length).toBeLessThan(5) // Allow some tolerance for debugging
  })

  test('Check specific layout elements causing horizontal scroll', async ({ page }) => {
    await page.goto('/')
    await page.waitForLoadState('networkidle')
    await page.setViewportSize({ width: 768, height: 1024 })
    
    // Check for elements with fixed widths that might be too large
    const fixedWidthElements = await page.evaluate(() => {
      const elements = Array.from(document.querySelectorAll('*'))
      const problematic = elements.filter(el => {
        const styles = window.getComputedStyle(el)
        const rect = el.getBoundingClientRect()
        
        // Check for fixed widths larger than viewport
        if (styles.width && styles.width.includes('px')) {
          const width = parseInt(styles.width)
          if (width > window.innerWidth) {
            return true
          }
        }
        
        // Check for min-width issues
        if (styles.minWidth && styles.minWidth.includes('px')) {
          const minWidth = parseInt(styles.minWidth)
          if (minWidth > window.innerWidth) {
            return true
          }
        }
        
        // Check for elements that are actually overflowing
        if (rect.right > window.innerWidth) {
          return true
        }
        
        return false
      })
      
      return problematic.map(el => ({
        tag: el.tagName,
        className: el.className,
        id: el.id,
        width: el.getBoundingClientRect().width,
        computedWidth: window.getComputedStyle(el).width,
        minWidth: window.getComputedStyle(el).minWidth,
        maxWidth: window.getComputedStyle(el).maxWidth,
        position: window.getComputedStyle(el).position,
        transform: window.getComputedStyle(el).transform
      }))
    })
    
    console.log('Fixed Width Elements:', fixedWidthElements)
    
    expect(fixedWidthElements.length).toBeLessThan(10) // Allow tolerance for debugging
  })
})
