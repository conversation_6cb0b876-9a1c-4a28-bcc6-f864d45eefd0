import React, { useState, useEffect } from 'react'
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, useMap } from 'react-leaflet'
import { motion, AnimatePresence } from 'framer-motion'
import { Icon, LatLngBounds } from 'leaflet'
import { destinations } from '@/data/destinations'
import {
  MapPinIcon,
  FunnelIcon,
  XMarkIcon,
  MagnifyingGlassIcon
} from '@heroicons/react/24/outline'

// Fix for default markers in React Leaflet
delete (Icon.Default.prototype as any)._getIconUrl
Icon.Default.mergeOptions({
  iconRetinaUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon-2x.png',
  iconUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon.png',
  shadowUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-shadow.png',
})

// Custom marker icons for different categories
const createCustomIcon = (category: string) => {
  const colors = {
    cultural: '#8B5CF6', // Purple
    nature: '#10B981',   // Green
    beach: '#3B82F6',    // Blue
    city: '#F59E0B',     // Orange
    adventure: '#EF4444' // Red
  }

  const color = colors[category as keyof typeof colors] || '#6B7280'

  try {
    return new Icon({
      iconUrl: `data:image/svg+xml;base64,${btoa(`
        <svg width="25" height="41" viewBox="0 0 25 41" xmlns="http://www.w3.org/2000/svg">
          <path d="M12.5 0C5.6 0 0 5.6 0 12.5C0 19.4 12.5 41 12.5 41S25 19.4 25 12.5C25 5.6 19.4 0 12.5 0Z" fill="${color}"/>
          <circle cx="12.5" cy="12.5" r="6" fill="white"/>
        </svg>
      `)}`,
      iconSize: [25, 41],
      iconAnchor: [12, 41],
      popupAnchor: [1, -34],
      shadowUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-shadow.png',
      shadowSize: [41, 41],
      shadowAnchor: [12, 41]
    })
  } catch (error) {
    console.warn('Failed to create custom icon, falling back to default:', error)
    // Fallback to default icon if custom icon creation fails
    return new Icon.Default()
  }
}

interface InteractiveMapProps {
  className?: string
  height?: string
}

// Component to fit map bounds to all markers and handle map invalidation
const FitBounds: React.FC<{ destinations: typeof destinations }> = ({ destinations }) => {
  const map = useMap()

  useEffect(() => {
    if (destinations.length > 0) {
      const bounds = new LatLngBounds(
        destinations.map(dest => [dest.coordinates.lat, dest.coordinates.lng])
      )
      map.fitBounds(bounds, { padding: [20, 20] })
    }
  }, [destinations, map])

  // Handle map invalidation to fix display issues
  useEffect(() => {
    const timer = setTimeout(() => {
      map.invalidateSize()
    }, 100)

    return () => clearTimeout(timer)
  }, [map])

  return null
}

const InteractiveMap: React.FC<InteractiveMapProps> = ({
  className = '',
  height = '600px'
}) => {
  const [filteredDestinations, setFilteredDestinations] = useState(destinations)
  const [selectedCategory, setSelectedCategory] = useState<string>('all')
  const [searchTerm, setSearchTerm] = useState('')
  const [showFilters, setShowFilters] = useState(false)
  const [mapReady, setMapReady] = useState(false)
  const [mapError, setMapError] = useState<string | null>(null)

  const categories = [
    { id: 'all', name: 'All Destinations', count: destinations.length },
    { id: 'cultural', name: 'Cultural Sites', count: destinations.filter(d => d.category === 'cultural').length },
    { id: 'nature', name: 'Nature & Wildlife', count: destinations.filter(d => d.category === 'nature').length },
    { id: 'beach', name: 'Beaches', count: destinations.filter(d => d.category === 'beach').length },
    { id: 'city', name: 'Cities', count: destinations.filter(d => d.category === 'city').length },
    { id: 'adventure', name: 'Adventure', count: destinations.filter(d => d.category === 'adventure').length }
  ]

  // Filter destinations based on category and search term
  useEffect(() => {
    let filtered = destinations

    // Filter by category
    if (selectedCategory !== 'all') {
      filtered = filtered.filter(dest => dest.category === selectedCategory)
    }

    // Filter by search term
    if (searchTerm) {
      filtered = filtered.filter(dest =>
        dest.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        dest.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
        dest.province.toLowerCase().includes(searchTerm.toLowerCase())
      )
    }

    setFilteredDestinations(filtered)
  }, [selectedCategory, searchTerm])

  return (
    <div className={`relative ${className}`}>
      {/* Map Controls - positioned above map */}
      <div className="mb-4 flex flex-col sm:flex-row gap-4">
        {/* Search Bar */}
        <div className="relative flex-1">
          <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
          <input
            type="text"
            placeholder="Search destinations on map..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full pl-10 pr-4 py-2 bg-white rounded-lg shadow-lg border border-gray-200 focus:outline-none focus:ring-2 focus:ring-primary-500"
          />
        </div>

        {/* Filter Toggle */}
        <button
          type="button"
          onClick={() => setShowFilters(!showFilters)}
          className="flex items-center space-x-2 px-4 py-2 bg-white rounded-lg shadow-lg border border-gray-200 hover:bg-gray-50 transition-colors"
        >
          <FunnelIcon className="h-5 w-5 text-gray-600" />
          <span className="text-gray-700 font-medium">Filters</span>
        </button>
      </div>

      {/* Filter Panel */}
      <AnimatePresence>
        {showFilters && (
          <motion.div
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            className="mb-4 bg-white rounded-lg shadow-xl border border-gray-200 p-4"
          >
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-gray-900">Filter by Category</h3>
              <button
                type="button"
                onClick={() => setShowFilters(false)}
                className="p-1 hover:bg-gray-100 rounded-full transition-colors"
                aria-label="Close filters"
              >
                <XMarkIcon className="h-5 w-5 text-gray-500" />
              </button>
            </div>
            
            <div className="grid grid-cols-2 sm:grid-cols-3 gap-2">
              {categories.map((category) => (
                <button
                  key={category.id}
                  type="button"
                  onClick={() => setSelectedCategory(category.id)}
                  className={`p-3 rounded-lg text-left transition-colors ${
                    selectedCategory === category.id
                      ? 'bg-primary-100 text-primary-700 border-2 border-primary-300'
                      : 'bg-gray-50 text-gray-700 border-2 border-transparent hover:bg-gray-100'
                  }`}
                >
                  <div className="font-medium">{category.name}</div>
                  <div className="text-sm opacity-75">{category.count} destinations</div>
                </button>
              ))}
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Map Container */}
      <div className="relative">
        <div
          className={`rounded-lg overflow-hidden shadow-lg ${height === '100%' ? 'map-container-full' : 'map-container'}`}
        >
          {/* Loading overlay */}
          {!mapReady && (
            <div className="absolute inset-0 bg-gray-100 flex items-center justify-center z-[1000]">
              <div className="text-center">
                <div className="loading-spinner mx-auto mb-4"></div>
                <p className="text-gray-600">Loading map...</p>
              </div>
            </div>
          )}

          {/* Error overlay */}
          {mapError && (
            <div className="absolute inset-0 bg-red-50 flex items-center justify-center z-[1000]">
              <div className="text-center p-6">
                <p className="text-red-600 mb-4">Failed to load map</p>
                <button
                  type="button"
                  onClick={() => {
                    setMapError(null)
                    setMapReady(false)
                  }}
                  className="bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 transition-colors"
                >
                  Retry
                </button>
              </div>
            </div>
          )}

          <MapContainer
            center={[7.8731, 80.7718] as [number, number]} // Center of Sri Lanka
            zoom={8}
            style={{ height: '100%', width: '100%' }}
            className="z-0 leaflet-map-container"
            whenReady={() => {
              setMapReady(true)
              setMapError(null)
            }}
            onError={(error) => {
              console.error('Map error:', error)
              setMapError('Failed to load map tiles')
            }}
          >
          <TileLayer
            attribution='&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
            url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"
          />
          
          <FitBounds destinations={filteredDestinations} />

          {filteredDestinations.map((destination) => {
            try {
              return (
                <Marker
                  key={destination.id}
                  position={[destination.coordinates.lat, destination.coordinates.lng] as [number, number]}
                  icon={createCustomIcon(destination.category)}
                >
              <Popup maxWidth={300} className="custom-popup">
                <div className="p-2">
                  <h3 className="text-lg font-bold text-gray-900 mb-2">
                    {destination.name}
                  </h3>
                  <p className="text-gray-600 text-sm mb-3">
                    {destination.shortDescription}
                  </p>
                  
                  <div className="space-y-2 text-sm">
                    <div className="flex items-center space-x-2">
                      <MapPinIcon className="h-4 w-4 text-gray-500" />
                      <span className="text-gray-600">{destination.province} Province</span>
                    </div>
                    
                    <div className="flex items-center space-x-2">
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                        destination.category === 'cultural' ? 'bg-purple-100 text-purple-700' :
                        destination.category === 'nature' ? 'bg-green-100 text-green-700' :
                        destination.category === 'beach' ? 'bg-blue-100 text-blue-700' :
                        destination.category === 'city' ? 'bg-orange-100 text-orange-700' :
                        'bg-red-100 text-red-700'
                      }`}>
                        {destination.category}
                      </span>
                      <span className="text-gray-500">•</span>
                      <span className="text-gray-600">{destination.duration}</span>
                    </div>
                  </div>
                  
                  <div className="mt-3 pt-3 border-t border-gray-200">
                    <button
                      type="button"
                      className="w-full bg-primary-600 text-white py-2 px-4 rounded-lg hover:bg-primary-700 transition-colors text-sm font-medium"
                    >
                      View Details
                    </button>
                  </div>
                </div>
              </Popup>
            </Marker>
              )
            } catch (error) {
              console.warn(`Failed to render marker for ${destination.name}:`, error)
              return null
            }
          })}
          </MapContainer>
        </div>

        {/* Results Counter */}
        <div className="absolute bottom-4 left-4 z-[1000] bg-white rounded-lg shadow-lg px-3 py-2 border border-gray-200">
          <span className="text-sm text-gray-600">
            Showing <span className="font-semibold text-gray-900">{filteredDestinations.length}</span> of{' '}
            <span className="font-semibold text-gray-900">{destinations.length}</span> destinations
          </span>
        </div>
      </div>
    </div>
  )
}

export default InteractiveMap
