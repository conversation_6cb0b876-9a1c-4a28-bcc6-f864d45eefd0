var T,b;import{useRef as c,useState as S}from"react";import{disposables as m}from'../utils/disposables.js';import{useDisposables as g}from'./use-disposables.js';import{useFlags as y}from'./use-flags.js';import{useIsoMorphicEffect as A}from'./use-iso-morphic-effect.js';typeof process!="undefined"&&typeof globalThis!="undefined"&&typeof Element!="undefined"&&((T=process==null?void 0:process.env)==null?void 0:T["NODE_ENV"])==="test"&&typeof((b=Element==null?void 0:Element.prototype)==null?void 0:b.getAnimations)=="undefined"&&(Element.prototype.getAnimations=function(){return console.warn(["Headless UI has polyfilled `Element.prototype.getAnimations` for your tests.","Please install a proper polyfill e.g. `jsdom-testing-mocks`, to silence these warnings.","","Example usage:","```js","import { mockAnimationsApi } from 'jsdom-testing-mocks'","mockAnimationsApi()","```"].join(`
`)),[]});var L=(r=>(r[r.None=0]="None",r[r.Closed=1]="Closed",r[r.Enter=2]="Enter",r[r.Leave=4]="Leave",r))(L||{});function R(t){let n={};for(let e in t)t[e]===!0&&(n[`data-${e}`]="");return n}function x(t,n,e,i){let[r,o]=S(e),{hasFlag:s,addFlag:a,removeFlag:l}=y(t&&r?3:0),u=c(!1),f=c(!1),E=g();return A(()=>{var d;if(t){if(e&&o(!0),!n){e&&a(3);return}return(d=i==null?void 0:i.start)==null||d.call(i,e),C(n,{inFlight:u,prepare(){f.current?f.current=!1:f.current=u.current,u.current=!0,!f.current&&(e?(a(3),l(4)):(a(4),l(2)))},run(){f.current?e?(l(3),a(4)):(l(4),a(3)):e?l(1):a(1)},done(){var p;f.current&&typeof n.getAnimations=="function"&&n.getAnimations().length>0||(u.current=!1,l(7),e||o(!1),(p=i==null?void 0:i.end)==null||p.call(i,e))}})}},[t,e,n,E]),t?[r,{closed:s(1),enter:s(2),leave:s(4),transition:s(2)||s(4)}]:[e,{closed:void 0,enter:void 0,leave:void 0,transition:void 0}]}function C(t,{prepare:n,run:e,done:i,inFlight:r}){let o=m();return j(t,{prepare:n,inFlight:r}),o.nextFrame(()=>{e(),o.requestAnimationFrame(()=>{o.add(M(t,i))})}),o.dispose}function M(t,n){var o,s;let e=m();if(!t)return e.dispose;let i=!1;e.add(()=>{i=!0});let r=(s=(o=t.getAnimations)==null?void 0:o.call(t).filter(a=>a instanceof CSSTransition))!=null?s:[];return r.length===0?(n(),e.dispose):(Promise.allSettled(r.map(a=>a.finished)).then(()=>{i||n()}),e.dispose)}function j(t,{inFlight:n,prepare:e}){if(n!=null&&n.current){e();return}let i=t.style.transition;t.style.transition="none",e(),t.offsetHeight,t.style.transition=i}export{R as transitionDataAttributes,x as useTransition};
