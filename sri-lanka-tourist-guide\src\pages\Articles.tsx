import React, { useState, useMemo } from 'react'
import { He<PERSON><PERSON> } from 'react-helmet-async'
import { motion } from 'framer-motion'
import { Link } from 'react-router-dom'
import {
  BookOpenIcon,
  ClockIcon,
  TagIcon,
  MagnifyingGlassIcon,
  FunnelIcon,
  StarIcon
} from '@heroicons/react/24/outline'

import AnimatedSection from '@/components/AnimatedSection'
import { getAllArticles, getFeaturedArticles, type Article } from '@/data/articles'

// Get all available categories dynamically
const getAllCategories = (articles: Article[]): string[] => {
  const categories = new Set<string>()
  articles.forEach(article => categories.add(article.category))
  return ['All', ...Array.from(categories).sort()]
}

const Articles: React.FC = () => {
  const [selectedCategory, setSelectedCategory] = useState('All')
  const [searchTerm, setSearchTerm] = useState('')
  const [sortBy, setSortBy] = useState<'newest' | 'oldest' | 'featured'>('newest')

  // Get articles from centralized system
  const allArticles = useMemo(() => getAllArticles(), [])
  const featuredArticles = useMemo(() => getFeaturedArticles(), [])
  const categories = useMemo(() => getAllCategories(allArticles), [allArticles])

  // Sort articles based on selected criteria
  const sortedArticles = useMemo(() => {
    const sorted = [...allArticles]

    switch (sortBy) {
      case 'newest':
        return sorted.sort((a, b) => new Date(b.publishDate).getTime() - new Date(a.publishDate).getTime())
      case 'oldest':
        return sorted.sort((a, b) => new Date(a.publishDate).getTime() - new Date(b.publishDate).getTime())
      case 'featured':
        return sorted.sort((a, b) => {
          if (a.featured && !b.featured) return -1
          if (!a.featured && b.featured) return 1
          return new Date(b.publishDate).getTime() - new Date(a.publishDate).getTime()
        })
      default:
        return sorted
    }
  }, [allArticles, sortBy])

  // Filter articles based on search and category
  const filteredArticles = useMemo(() => {
    return sortedArticles.filter(article => {
      const matchesCategory = selectedCategory === 'All' || article.category === selectedCategory
      const matchesSearch = article.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           article.excerpt.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           article.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()))
      return matchesCategory && matchesSearch
    })
  }, [sortedArticles, selectedCategory, searchTerm])

  return (
    <>
      <Helmet>
        <title>Sri Lanka Travel Articles & Guides | Expert Travel Content</title>
        <meta name="description" content="Comprehensive travel articles and guides for Sri Lanka. Expert insights on destinations, culture, wildlife, and practical travel tips for the perfect Sri Lankan adventure." />
        <meta property="og:title" content="Sri Lanka Travel Articles & Guides" />
        <meta property="og:description" content="Expert travel content covering all aspects of Sri Lankan travel - from ancient temples to pristine beaches." />
        <script type="application/ld+json">
          {JSON.stringify({
            "@context": "https://schema.org",
            "@type": "Blog",
            "name": "Sri Lanka Travel Articles",
            "description": "Comprehensive travel guides and articles about Sri Lanka",
            "url": "https://srilankatouristguide.com/articles",
            "publisher": {
              "@type": "Organization",
              "name": "Sri Lanka Tourist Guide"
            }
          })}
        </script>
      </Helmet>

      <div className="min-h-screen bg-white">
        {/* Hero Section */}
        <section className="relative bg-gradient-to-br from-primary-600 to-purple-600 text-white py-20">
          <div className="container-max">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              className="text-center"
            >
              <h1 className="text-4xl md:text-6xl font-bold mb-6">
                Sri Lanka Travel Articles
              </h1>
              <p className="text-xl md:text-2xl mb-8 opacity-90 max-w-3xl mx-auto">
                Expert guides, insider tips, and comprehensive travel content to help you discover the magic of Sri Lanka
              </p>
              <div className="flex items-center justify-center space-x-4 text-sm">
                <div className="flex items-center space-x-2">
                  <BookOpenIcon className="h-5 w-5" />
                  <span>{allArticles.length} Articles</span>
                </div>
                <div className="flex items-center space-x-2">
                  <StarIcon className="h-5 w-5" />
                  <span>{featuredArticles.length} Featured</span>
                </div>
                <div className="flex items-center space-x-2">
                  <ClockIcon className="h-5 w-5" />
                  <span>Updated January 2025</span>
                </div>
              </div>
            </motion.div>
          </div>
        </section>

        {/* Search and Filter Section */}
        <section className="section-padding bg-gray-50">
          <div className="container-max">
            <div className="max-w-4xl mx-auto">
              {/* Search Bar */}
              <div className="relative mb-8">
                <MagnifyingGlassIcon className="absolute left-4 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
                <input
                  type="text"
                  placeholder="Search articles by title, content, or tags..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  aria-label="Search articles by title, content, or tags"
                  className="w-full pl-12 pr-4 py-4 text-lg border-2 border-gray-200 rounded-xl focus:outline-none focus:border-primary-500 focus:ring-2 focus:ring-primary-200"
                />
              </div>

              {/* Category Filter */}
              <div className="flex items-center space-x-4 mb-8">
                <FunnelIcon className="h-5 w-5 text-gray-600" />
                <div className="flex flex-wrap gap-2">
                  {categories.map((category) => (
                    <button
                      type="button"
                      key={category}
                      onClick={() => setSelectedCategory(category)}
                      className={`px-4 py-2 rounded-full text-sm font-medium transition-colors ${
                        selectedCategory === category
                          ? 'bg-primary-600 text-white'
                          : 'bg-white text-gray-700 hover:bg-gray-100 border border-gray-200'
                      }`}
                    >
                      {category}
                    </button>
                  ))}
                </div>
              </div>

              {/* Sort and Results */}
              <div className="flex items-center justify-between mb-8">
                <div className="text-gray-600">
                  Showing {filteredArticles.length} of {allArticles.length} articles
                </div>
                <div className="flex items-center space-x-2">
                  <span className="text-sm text-gray-600">Sort by:</span>
                  <select
                    value={sortBy}
                    onChange={(e) => setSortBy(e.target.value as 'newest' | 'oldest' | 'featured')}
                    className="px-3 py-1 border border-gray-200 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-primary-500"
                    aria-label="Sort articles by"
                  >
                    <option value="newest">Newest First</option>
                    <option value="oldest">Oldest First</option>
                    <option value="featured">Featured First</option>
                  </select>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Featured Articles */}
        {selectedCategory === 'All' && !searchTerm && (
          <section className="section-padding">
            <div className="container-max">
              <AnimatedSection>
                <h2 className="text-3xl font-bold text-gray-900 mb-8 text-center">
                  Featured Articles
                </h2>
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-16">
                  {featuredArticles.slice(0, 4).map((article, index) => (
                    <motion.article
                      key={article.id}
                      initial={{ opacity: 0, y: 30 }}
                      whileInView={{ opacity: 1, y: 0 }}
                      transition={{ duration: 0.6, delay: index * 0.1 }}
                      viewport={{ once: true }}
                      className="bg-white rounded-xl overflow-hidden shadow-lg hover:shadow-xl transition-shadow"
                    >
                      <div className="aspect-video overflow-hidden">
                        <img
                          src={article.image}
                          alt={article.title}
                          loading="lazy"
                          className="w-full h-full object-cover hover:scale-105 transition-transform duration-300"
                        />
                      </div>
                      <div className="p-6">
                        <div className="flex items-center space-x-4 mb-3">
                          <span className="px-3 py-1 bg-primary-100 text-primary-700 rounded-full text-sm font-medium">
                            {article.category}
                          </span>
                          <span className="text-gray-500 text-sm">{article.readTime}</span>
                        </div>
                        <h3 className="text-xl font-bold text-gray-900 mb-3 line-clamp-2">
                          {article.title}
                        </h3>
                        <p className="text-gray-600 mb-4 line-clamp-3">
                          {article.excerpt}
                        </p>
                        <div className="flex flex-wrap gap-2 mb-4">
                          {article.tags.slice(0, 3).map((tag) => (
                            <span
                              key={tag}
                              className="inline-flex items-center space-x-1 text-xs text-gray-500"
                            >
                              <TagIcon className="h-3 w-3" />
                              <span>{tag}</span>
                            </span>
                          ))}
                        </div>
                        <Link
                          to={`/articles/${article.id}`}
                          className="btn-primary w-full text-center"
                        >
                          Read Article
                        </Link>
                      </div>
                    </motion.article>
                  ))}
                </div>
              </AnimatedSection>
            </div>
          </section>
        )}

        {/* All Articles Grid */}
        <section className="section-padding bg-gray-50">
          <div className="container-max">
            <h2 className="text-3xl font-bold text-gray-900 mb-8 text-center">
              {selectedCategory === 'All' ? 'All Articles' : `${selectedCategory} Articles`}
            </h2>
            
            {filteredArticles.length > 0 ? (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                {filteredArticles.map((article, index) => (
                  <motion.article
                    key={article.id}
                    initial={{ opacity: 0, y: 30 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.6, delay: index * 0.1 }}
                    viewport={{ once: true }}
                    className="bg-white rounded-xl overflow-hidden shadow-lg hover:shadow-xl transition-shadow"
                  >
                    <div className="aspect-video overflow-hidden bg-gradient-to-br from-gray-100 to-gray-200 flex items-center justify-center">
                      <div className="text-center text-gray-500">
                        <div className="text-4xl mb-2">📰</div>
                        <div className="text-sm font-medium">Article Image</div>
                      </div>
                    </div>
                    <div className="p-6">
                      <div className="flex items-center justify-between mb-3">
                        <span className="px-3 py-1 bg-primary-100 text-primary-700 rounded-full text-sm font-medium">
                          {article.category}
                        </span>
                        <span className="text-gray-500 text-sm">{article.readTime}</span>
                      </div>
                      <h3 className="text-lg font-bold text-gray-900 mb-3 line-clamp-2">
                        {article.title}
                      </h3>
                      <p className="text-gray-600 mb-4 line-clamp-3">
                        {article.excerpt}
                      </p>
                      <div className="flex flex-wrap gap-2 mb-4">
                        {article.tags.slice(0, 2).map((tag) => (
                          <span
                            key={tag}
                            className="inline-flex items-center space-x-1 text-xs text-gray-500"
                          >
                            <TagIcon className="h-3 w-3" />
                            <span>{tag}</span>
                          </span>
                        ))}
                      </div>
                      <Link
                        to={`/articles/${article.id}`}
                        className="btn-primary w-full text-center"
                      >
                        Read Article
                      </Link>
                    </div>
                  </motion.article>
                ))}
              </div>
            ) : (
              <div className="text-center py-16">
                <BookOpenIcon className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                <h3 className="text-xl font-semibold text-gray-900 mb-2">No articles found</h3>
                <p className="text-gray-600 mb-6">
                  Try adjusting your search terms or category filter
                </p>
                <button
                  type="button"
                  onClick={() => {
                    setSearchTerm('')
                    setSelectedCategory('All')
                  }}
                  className="btn-secondary"
                >
                  Clear Filters
                </button>
              </div>
            )}
          </div>
        </section>

        {/* Newsletter Signup */}
        <section className="section-padding bg-primary-600 text-white">
          <div className="container-max text-center">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
            >
              <h2 className="text-3xl font-bold mb-4">
                Stay Updated with Latest Travel Guides
              </h2>
              <p className="text-xl mb-8 opacity-90 max-w-2xl mx-auto">
                Get notified when we publish new comprehensive travel guides and insider tips for Sri Lanka
              </p>
              <div className="max-w-md mx-auto flex gap-4">
                <input
                  type="email"
                  placeholder="Enter your email"
                  aria-label="Enter your email for newsletter subscription"
                  className="flex-1 px-4 py-3 rounded-lg text-gray-900 focus:outline-none focus:ring-2 focus:ring-white"
                />
                <button type="submit" className="bg-white text-primary-600 px-6 py-3 rounded-lg font-medium hover:bg-gray-100 transition-colors">
                  Subscribe
                </button>
              </div>
            </motion.div>
          </div>
        </section>
      </div>
    </>
  )
}

export default Articles
