import { FullConfig } from '@playwright/test'
import fs from 'fs'
import path from 'path'

async function globalTeardown(config: FullConfig) {
  console.log('🧹 Starting global teardown...')
  
  try {
    // Generate test summary
    const testResultsPath = path.join(process.cwd(), 'test-results')
    const resultsJsonPath = path.join(testResultsPath, 'results.json')
    
    if (fs.existsSync(resultsJsonPath)) {
      const results = JSON.parse(fs.readFileSync(resultsJsonPath, 'utf-8'))
      
      console.log('📊 Test Summary:')
      console.log(`   Total tests: ${results.stats?.total || 'N/A'}`)
      console.log(`   Passed: ${results.stats?.passed || 'N/A'}`)
      console.log(`   Failed: ${results.stats?.failed || 'N/A'}`)
      console.log(`   Skipped: ${results.stats?.skipped || 'N/A'}`)
      console.log(`   Duration: ${results.stats?.duration || 'N/A'}ms`)
    }
    
    // Clean up temporary files if needed
    const tempFiles = [
      path.join(testResultsPath, 'temp-*.json'),
      path.join(testResultsPath, 'temp-*.log')
    ]
    
    tempFiles.forEach(pattern => {
      // In a real implementation, you'd use glob to match patterns
      // For now, just log what we would clean up
      console.log(`🗑️ Would clean up: ${pattern}`)
    })
    
    // Archive test artifacts if in CI
    if (process.env.CI) {
      console.log('📦 Archiving test artifacts for CI...')
      // In a real implementation, you might:
      // - Upload artifacts to cloud storage
      // - Generate reports
      // - Send notifications
    }
    
    console.log('✅ Global teardown completed successfully')
    
  } catch (error) {
    console.error('❌ Global teardown failed:', error)
    // Don't throw error in teardown to avoid masking test failures
  }
}

export default globalTeardown
