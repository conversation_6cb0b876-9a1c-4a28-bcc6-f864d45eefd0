import React, { Suspense } from 'react'
import { Routes, Route } from 'react-router-dom'
import { motion } from 'framer-motion'
import Layout from './components/Layout'
import LoadingSpinner from './components/LoadingSpinner'

// Lazy load pages for better performance
const Home = React.lazy(() => import('./pages/Home'))
const Destinations = React.lazy(() => import('./pages/Destinations'))
const DestinationDetail = React.lazy(() => import('./pages/DestinationDetail'))
const Activities = React.lazy(() => import('./pages/Activities'))
const Culture = React.lazy(() => import('./pages/Culture'))
const TravelTips = React.lazy(() => import('./pages/TravelTips'))
const Articles = React.lazy(() => import('./pages/Articles'))
const ArticleDetail = React.lazy(() => import('./pages/ArticleDetail'))
const Contact = React.lazy(() => import('./pages/Contact'))
const TestMapPage = React.lazy(() => import('./pages/TestMapPage'))
const NotFound = React.lazy(() => import('./pages/NotFound'))

// Page transition variants
const pageVariants = {
  initial: { opacity: 0, y: 20 },
  in: { opacity: 1, y: 0 },
  out: { opacity: 0, y: -20 }
}

const pageTransition = {
  duration: 0.5
}

function App() {
  return (
    <Layout>
      <Suspense fallback={<LoadingSpinner />}>
        <Routes>
          <Route 
            path="/" 
            element={
              <motion.div
                initial="initial"
                animate="in"
                exit="out"
                variants={pageVariants}
                transition={pageTransition}
              >
                <Home />
              </motion.div>
            } 
          />
          <Route 
            path="/destinations" 
            element={
              <motion.div
                initial="initial"
                animate="in"
                exit="out"
                variants={pageVariants}
                transition={pageTransition}
              >
                <Destinations />
              </motion.div>
            } 
          />
          <Route 
            path="/destinations/:id" 
            element={
              <motion.div
                initial="initial"
                animate="in"
                exit="out"
                variants={pageVariants}
                transition={pageTransition}
              >
                <DestinationDetail />
              </motion.div>
            } 
          />
          <Route 
            path="/activities" 
            element={
              <motion.div
                initial="initial"
                animate="in"
                exit="out"
                variants={pageVariants}
                transition={pageTransition}
              >
                <Activities />
              </motion.div>
            } 
          />
          <Route 
            path="/culture" 
            element={
              <motion.div
                initial="initial"
                animate="in"
                exit="out"
                variants={pageVariants}
                transition={pageTransition}
              >
                <Culture />
              </motion.div>
            } 
          />
          <Route
            path="/travel-tips"
            element={
              <motion.div
                initial="initial"
                animate="in"
                exit="out"
                variants={pageVariants}
                transition={pageTransition}
              >
                <TravelTips />
              </motion.div>
            }
          />
          <Route
            path="/articles"
            element={
              <motion.div
                initial="initial"
                animate="in"
                exit="out"
                variants={pageVariants}
                transition={pageTransition}
              >
                <Articles />
              </motion.div>
            }
          />
          <Route
            path="/articles/:id"
            element={
              <motion.div
                initial="initial"
                animate="in"
                exit="out"
                variants={pageVariants}
                transition={pageTransition}
              >
                <ArticleDetail />
              </motion.div>
            }
          />
          <Route
            path="/contact"
            element={
              <motion.div
                initial="initial"
                animate="in"
                exit="out"
                variants={pageVariants}
                transition={pageTransition}
              >
                <Contact />
              </motion.div>
            }
          />
          <Route
            path="/test-map"
            element={
              <motion.div
                initial="initial"
                animate="in"
                exit="out"
                variants={pageVariants}
                transition={pageTransition}
              >
                <TestMapPage />
              </motion.div>
            }
          />
          <Route
            path="*"
            element={
              <motion.div
                initial="initial"
                animate="animate"
                exit="exit"
                variants={pageVariants}
                transition={pageTransition}
              >
                <NotFound />
              </motion.div>
            }
          />
        </Routes>
      </Suspense>
    </Layout>
  )
}

export default App
