#!/usr/bin/env node

const { execSync } = require('child_process')
const fs = require('fs')
const path = require('path')

// Comprehensive test execution script for Sri Lanka Tourist Guide
console.log('🧪 Starting comprehensive test execution...')

const PROJECT_ROOT = path.join(__dirname, '..')
const REPORTS_DIR = path.join(PROJECT_ROOT, 'test-reports')

// Ensure reports directory exists
if (!fs.existsSync(REPORTS_DIR)) {
  fs.mkdirSync(REPORTS_DIR, { recursive: true })
}

// Test suites configuration
const TEST_SUITES = [
  {
    name: 'Basic Functionality Tests',
    command: 'npx playwright test tests/basic-functionality.spec.ts',
    timeout: 300000, // 5 minutes
    critical: true
  },
  {
    name: 'Performance Tests',
    command: 'npx playwright test tests/performance.spec.ts',
    timeout: 600000, // 10 minutes
    critical: true
  },
  {
    name: 'Accessibility Tests',
    command: 'npx playwright test tests/accessibility.spec.ts',
    timeout: 300000, // 5 minutes
    critical: true
  },
  {
    name: 'SEO Tests',
    command: 'npx playwright test tests/seo.spec.ts',
    timeout: 300000, // 5 minutes
    critical: true
  },
  {
    name: 'Cross-browser Tests',
    command: 'npx playwright test --project=chromium --project=firefox --project=webkit',
    timeout: 900000, // 15 minutes
    critical: false
  },
  {
    name: 'Mobile Responsiveness Tests',
    command: 'npx playwright test tests/mobile.spec.ts',
    timeout: 300000, // 5 minutes
    critical: true
  }
]

// Utility functions
function executeCommand(command, options = {}) {
  try {
    const result = execSync(command, {
      cwd: PROJECT_ROOT,
      encoding: 'utf-8',
      timeout: options.timeout || 300000,
      ...options
    })
    return { success: true, output: result }
  } catch (error) {
    return { 
      success: false, 
      output: error.stdout || error.message,
      error: error.stderr || error.message
    }
  }
}

function generateTimestamp() {
  return new Date().toISOString().replace(/[:.]/g, '-')
}

function saveReport(filename, content) {
  const filePath = path.join(REPORTS_DIR, filename)
  fs.writeFileSync(filePath, content)
  console.log(`📄 Report saved: ${filename}`)
}

// Pre-test setup
function setupTestEnvironment() {
  console.log('⚙️ Setting up test environment...')
  
  // Check if development server is running
  try {
    const response = executeCommand('curl -s http://localhost:5173', { timeout: 5000 })
    if (!response.success) {
      console.log('🚀 Starting development server...')
      // Start dev server in background
      executeCommand('npm run dev &', { timeout: 10000 })
      
      // Wait for server to start
      let attempts = 0
      while (attempts < 30) {
        try {
          const check = executeCommand('curl -s http://localhost:5173', { timeout: 2000 })
          if (check.success) {
            console.log('✅ Development server is running')
            break
          }
        } catch (e) {
          // Server not ready yet
        }
        attempts++
        console.log(`⏳ Waiting for server... (${attempts}/30)`)
        // Sleep for 1 second
        execSync('sleep 1', { cwd: PROJECT_ROOT })
      }
      
      if (attempts >= 30) {
        console.error('❌ Failed to start development server')
        return false
      }
    } else {
      console.log('✅ Development server is already running')
    }
  } catch (error) {
    console.error('❌ Error checking development server:', error.message)
    return false
  }
  
  // Install Playwright browsers if needed
  console.log('🌐 Ensuring Playwright browsers are installed...')
  const browserCheck = executeCommand('npx playwright install', { timeout: 120000 })
  if (!browserCheck.success) {
    console.error('❌ Failed to install Playwright browsers')
    return false
  }
  
  console.log('✅ Test environment setup complete')
  return true
}

// Execute test suite
function executeTestSuite(suite) {
  console.log(`\n🧪 Running: ${suite.name}`)
  console.log('─'.repeat(50))
  
  const startTime = Date.now()
  const result = executeCommand(suite.command, { timeout: suite.timeout })
  const endTime = Date.now()
  const duration = (endTime - startTime) / 1000
  
  const testResult = {
    name: suite.name,
    command: suite.command,
    success: result.success,
    duration: duration,
    output: result.output,
    error: result.error,
    critical: suite.critical,
    timestamp: new Date().toISOString()
  }
  
  if (result.success) {
    console.log(`✅ ${suite.name} completed successfully (${duration.toFixed(2)}s)`)
  } else {
    console.log(`❌ ${suite.name} failed (${duration.toFixed(2)}s)`)
    if (suite.critical) {
      console.log('⚠️ This is a critical test suite')
    }
  }
  
  return testResult
}

// Generate comprehensive report
function generateComprehensiveReport(results) {
  console.log('\n📊 Generating comprehensive test report...')
  
  const timestamp = generateTimestamp()
  const totalTests = results.length
  const passedTests = results.filter(r => r.success).length
  const failedTests = totalTests - passedTests
  const criticalFailures = results.filter(r => !r.success && r.critical).length
  
  const report = `# Sri Lanka Tourist Guide - Comprehensive Test Report

**Generated:** ${new Date().toISOString()}
**Test Environment:** Playwright + Multi-browser
**Total Duration:** ${results.reduce((sum, r) => sum + r.duration, 0).toFixed(2)} seconds

## Executive Summary

| Metric | Value |
|--------|-------|
| Total Test Suites | ${totalTests} |
| Passed | ${passedTests} |
| Failed | ${failedTests} |
| Success Rate | ${Math.round((passedTests / totalTests) * 100)}% |
| Critical Failures | ${criticalFailures} |

## Test Results Overview

${results.map(result => `
### ${result.name}
- **Status:** ${result.success ? '✅ PASSED' : '❌ FAILED'}
- **Duration:** ${result.duration.toFixed(2)} seconds
- **Critical:** ${result.critical ? 'Yes' : 'No'}
- **Command:** \`${result.command}\`

${result.success ? 
  '**Result:** All tests in this suite passed successfully.' : 
  `**Error Details:**
\`\`\`
${result.error || 'No specific error details available'}
\`\`\`

**Output:**
\`\`\`
${result.output ? result.output.substring(0, 1000) + (result.output.length > 1000 ? '...' : '') : 'No output available'}
\`\`\``
}
`).join('\n')}

## Performance Metrics

${results.find(r => r.name.includes('Performance')) ? `
Based on performance test results:
- **Core Web Vitals:** ${results.find(r => r.name.includes('Performance')).success ? 'PASSED' : 'NEEDS IMPROVEMENT'}
- **Page Load Times:** ${results.find(r => r.name.includes('Performance')).success ? 'Within acceptable limits' : 'Exceeds recommended thresholds'}
- **Bundle Size:** ${results.find(r => r.name.includes('Performance')).success ? 'Optimized' : 'Requires optimization'}
` : 'Performance tests not completed'}

## Accessibility Compliance

${results.find(r => r.name.includes('Accessibility')) ? `
- **WCAG 2.1 AA Compliance:** ${results.find(r => r.name.includes('Accessibility')).success ? 'COMPLIANT' : 'NON-COMPLIANT'}
- **Screen Reader Support:** ${results.find(r => r.name.includes('Accessibility')).success ? 'Adequate' : 'Needs improvement'}
- **Keyboard Navigation:** ${results.find(r => r.name.includes('Accessibility')).success ? 'Functional' : 'Issues detected'}
` : 'Accessibility tests not completed'}

## SEO Health

${results.find(r => r.name.includes('SEO')) ? `
- **Meta Tags:** ${results.find(r => r.name.includes('SEO')).success ? 'Properly configured' : 'Issues detected'}
- **Structured Data:** ${results.find(r => r.name.includes('SEO')).success ? 'Valid' : 'Validation errors'}
- **Sitemap:** ${results.find(r => r.name.includes('SEO')).success ? 'Accessible' : 'Issues detected'}
` : 'SEO tests not completed'}

## Cross-Browser Compatibility

${results.find(r => r.name.includes('Cross-browser')) ? `
- **Chrome:** ${results.find(r => r.name.includes('Cross-browser')).success ? 'Compatible' : 'Issues detected'}
- **Firefox:** ${results.find(r => r.name.includes('Cross-browser')).success ? 'Compatible' : 'Issues detected'}
- **Safari/WebKit:** ${results.find(r => r.name.includes('Cross-browser')).success ? 'Compatible' : 'Issues detected'}
` : 'Cross-browser tests not completed'}

## Recommendations

${criticalFailures > 0 ? `
### Critical Issues (Immediate Action Required)
${results.filter(r => !r.success && r.critical).map(r => `- Fix issues in ${r.name}`).join('\n')}

### Priority Actions
1. Address all critical test failures
2. Re-run failed test suites after fixes
3. Validate core functionality before deployment
` : `
### Quality Improvements
1. Monitor performance metrics regularly
2. Maintain accessibility compliance
3. Keep SEO optimization up to date
4. Test across all supported browsers
`}

## Next Steps

${passedTests === totalTests ? `
🎉 **All tests passed!** The application is ready for production deployment.

**Recommended actions:**
1. Deploy to staging environment
2. Perform final user acceptance testing
3. Monitor production metrics
4. Set up continuous monitoring
` : `
⚠️ **Action required:** ${failedTests} test suite(s) failed.

**Immediate actions:**
1. Review and fix failed tests
2. Re-run test suite after fixes
3. Validate all functionality
4. Ensure critical issues are resolved before deployment
`}

---

**Report Generated:** ${new Date().toISOString()}
**Next Review:** After implementing fixes
**Contact:** Development Team
`

  saveReport(`comprehensive-test-report-${timestamp}.md`, report)
  
  // Also save JSON report for programmatic access
  const jsonReport = {
    timestamp: new Date().toISOString(),
    summary: {
      totalTests,
      passedTests,
      failedTests,
      successRate: Math.round((passedTests / totalTests) * 100),
      criticalFailures
    },
    results
  }
  
  saveReport(`test-results-${timestamp}.json`, JSON.stringify(jsonReport, null, 2))
  
  return report
}

// Main execution
function main() {
  console.log('🚀 Sri Lanka Tourist Guide - Comprehensive Test Execution')
  console.log('=' .repeat(60))
  
  // Setup test environment
  if (!setupTestEnvironment()) {
    console.error('❌ Failed to setup test environment')
    process.exit(1)
  }
  
  // Execute all test suites
  const results = []
  let criticalFailures = 0
  
  for (const suite of TEST_SUITES) {
    const result = executeTestSuite(suite)
    results.push(result)
    
    if (!result.success && result.critical) {
      criticalFailures++
    }
  }
  
  // Generate comprehensive report
  const report = generateComprehensiveReport(results)
  
  // Final summary
  console.log('\n🎯 Test Execution Summary:')
  console.log('=' .repeat(40))
  console.log(`Total Suites: ${results.length}`)
  console.log(`Passed: ${results.filter(r => r.success).length}`)
  console.log(`Failed: ${results.filter(r => !r.success).length}`)
  console.log(`Critical Failures: ${criticalFailures}`)
  
  if (criticalFailures > 0) {
    console.log('\n❌ Critical test failures detected!')
    console.log('🔧 Please run the fix script: npm run fix:critical')
    process.exit(1)
  } else if (results.every(r => r.success)) {
    console.log('\n🎉 All tests passed! Application is ready for deployment.')
  } else {
    console.log('\n⚠️ Some non-critical tests failed. Review the report for details.')
  }
}

if (require.main === module) {
  main()
}

module.exports = {
  setupTestEnvironment,
  executeTestSuite,
  generateComprehensiveReport
}
