# Sri Lanka Tourist Guide - Deployment Guide

## Quick Start Deployment

### Prerequisites
- Node.js 18+ installed
- Git repository access
- Hosting platform account (Vercel/Netlify recommended)

### 1. Production Build
```bash
# Install dependencies
npm install

# Run production build with all optimizations
npm run build:production

# Preview the production build locally
npm run preview
```

### 2. Deploy to Vercel (Recommended)

#### Option A: Automatic Deployment
1. Push code to GitHub repository
2. Connect repository to Vercel
3. Vercel will automatically deploy on every push

#### Option B: Manual Deployment
```bash
# Install Vercel CLI
npm install -g vercel

# Deploy to Vercel
vercel --prod
```

#### Vercel Configuration (vercel.json)
```json
{
  "buildCommand": "npm run build:production",
  "outputDirectory": "dist",
  "framework": "vite",
  "rewrites": [
    {
      "source": "/(.*)",
      "destination": "/index.html"
    }
  ],
  "headers": [
    {
      "source": "/(.*)",
      "headers": [
        {
          "key": "X-Content-Type-Options",
          "value": "nosniff"
        },
        {
          "key": "X-Frame-Options",
          "value": "DENY"
        },
        {
          "key": "X-XSS-Protection",
          "value": "1; mode=block"
        }
      ]
    },
    {
      "source": "/sw.js",
      "headers": [
        {
          "key": "Cache-Control",
          "value": "public, max-age=0, must-revalidate"
        }
      ]
    },
    {
      "source": "/assets/(.*)",
      "headers": [
        {
          "key": "Cache-Control",
          "value": "public, max-age=31536000, immutable"
        }
      ]
    }
  ]
}
```

### 3. Deploy to Netlify

#### Option A: Drag and Drop
1. Run `npm run build:production`
2. Drag the `dist` folder to Netlify dashboard

#### Option B: Git Integration
1. Connect GitHub repository to Netlify
2. Set build command: `npm run build:production`
3. Set publish directory: `dist`

#### Netlify Configuration (netlify.toml)
```toml
[build]
  command = "npm run build:production"
  publish = "dist"

[[redirects]]
  from = "/*"
  to = "/index.html"
  status = 200

[[headers]]
  for = "/*"
  [headers.values]
    X-Frame-Options = "DENY"
    X-XSS-Protection = "1; mode=block"
    X-Content-Type-Options = "nosniff"

[[headers]]
  for = "/assets/*"
  [headers.values]
    Cache-Control = "public, max-age=31536000, immutable"

[[headers]]
  for = "/sw.js"
  [headers.values]
    Cache-Control = "public, max-age=0, must-revalidate"
```

## Environment Configuration

### Environment Variables
Create `.env.production` file:
```env
VITE_APP_TITLE=Sri Lanka Tourist Guide
VITE_APP_DESCRIPTION=Complete travel guide to Sri Lanka featuring destinations, culture, and practical tips
VITE_APP_URL=https://srilankatouristguide.com
VITE_GOOGLE_ANALYTICS_ID=G-XXXXXXXXXX
VITE_GOOGLE_SEARCH_CONSOLE_ID=your-search-console-id
```

### Platform-Specific Environment Variables

#### Vercel
Set in Vercel dashboard under Project Settings > Environment Variables

#### Netlify
Set in Netlify dashboard under Site Settings > Environment Variables

## Post-Deployment Setup

### 1. SEO Configuration

#### Google Search Console
1. Go to [Google Search Console](https://search.google.com/search-console)
2. Add your domain
3. Verify ownership
4. Submit sitemap: `https://yourdomain.com/sitemap.xml`

#### Google Analytics
1. Create Google Analytics 4 property
2. Add tracking ID to environment variables
3. Verify tracking is working

#### Bing Webmaster Tools
1. Go to [Bing Webmaster Tools](https://www.bing.com/webmasters)
2. Add your site
3. Submit sitemap

### 2. Performance Monitoring

#### Core Web Vitals
Monitor these metrics:
- **LCP (Largest Contentful Paint)**: < 2.5s
- **FID (First Input Delay)**: < 100ms
- **CLS (Cumulative Layout Shift)**: < 0.1

#### Tools for Monitoring
- Google PageSpeed Insights
- Lighthouse CI
- Web Vitals Chrome Extension
- Vercel Analytics (if using Vercel)

### 3. Security Headers

Ensure these headers are configured:
```
X-Content-Type-Options: nosniff
X-Frame-Options: DENY
X-XSS-Protection: 1; mode=block
Strict-Transport-Security: max-age=31536000; includeSubDomains
Content-Security-Policy: default-src 'self'; img-src 'self' data: https:; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://www.googletagmanager.com; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com;
```

## Continuous Integration/Deployment

### GitHub Actions Workflow
Create `.github/workflows/deploy.yml`:
```yaml
name: Deploy to Production

on:
  push:
    branches: [main]

jobs:
  deploy:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Setup Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '18'
        cache: 'npm'
    
    - name: Install dependencies
      run: npm ci
    
    - name: Run tests
      run: npm run test:ci
    
    - name: Build production
      run: npm run build:production
    
    - name: Deploy to Vercel
      uses: amondnet/vercel-action@v25
      with:
        vercel-token: ${{ secrets.VERCEL_TOKEN }}
        vercel-org-id: ${{ secrets.ORG_ID }}
        vercel-project-id: ${{ secrets.PROJECT_ID }}
        vercel-args: '--prod'
```

## Troubleshooting

### Common Issues

#### Build Fails
1. Check Node.js version (requires 18+)
2. Clear node_modules and reinstall: `rm -rf node_modules package-lock.json && npm install`
3. Check for TypeScript errors: `npm run type-check`

#### Performance Issues
1. Run bundle analyzer: `npm run build:analyze`
2. Check for large dependencies
3. Implement code splitting if needed

#### SEO Issues
1. Verify sitemap is accessible: `/sitemap.xml`
2. Check robots.txt: `/robots.txt`
3. Validate structured data with Google's Rich Results Test

#### 404 Errors on Refresh
Ensure your hosting platform is configured for SPA routing:
- Vercel: Automatic with framework detection
- Netlify: Add `_redirects` file or use netlify.toml
- Other platforms: Configure fallback to index.html

## Monitoring and Maintenance

### Weekly Tasks
- [ ] Check Google Search Console for errors
- [ ] Review Core Web Vitals metrics
- [ ] Monitor uptime and performance
- [ ] Check for broken links

### Monthly Tasks
- [ ] Update dependencies: `npm update`
- [ ] Review and update content
- [ ] Analyze traffic and user behavior
- [ ] Security audit

### Quarterly Tasks
- [ ] Comprehensive SEO audit
- [ ] Performance optimization review
- [ ] Content strategy review
- [ ] Backup and disaster recovery test

## Support and Resources

### Documentation
- [Vite Deployment Guide](https://vitejs.dev/guide/static-deploy.html)
- [React Router Deployment](https://reactrouter.com/en/main/guides/deploying)
- [Vercel Documentation](https://vercel.com/docs)
- [Netlify Documentation](https://docs.netlify.com/)

### Performance Tools
- [Google PageSpeed Insights](https://pagespeed.web.dev/)
- [GTmetrix](https://gtmetrix.com/)
- [WebPageTest](https://www.webpagetest.org/)
- [Lighthouse](https://developers.google.com/web/tools/lighthouse)

### SEO Tools
- [Google Search Console](https://search.google.com/search-console)
- [Google Analytics](https://analytics.google.com/)
- [Bing Webmaster Tools](https://www.bing.com/webmasters)
- [Schema Markup Validator](https://validator.schema.org/)

---

**Last Updated:** June 2025
**Version:** 1.0
**Status:** Production Ready
