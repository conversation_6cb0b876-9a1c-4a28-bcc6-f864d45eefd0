// Performance optimization utilities for Sri Lanka Tourist Guide

// Image optimization
export const imageOptimization = {
  // Generate responsive image URLs
  generateResponsiveImageUrl: (baseUrl: string, width: number, quality = 80) => {
    // If using a service like Cloudinary or similar
    if (baseUrl.includes('cloudinary.com')) {
      return baseUrl.replace('/upload/', `/upload/w_${width},q_${quality},f_auto/`)
    }
    
    // For local images, return as-is (would need server-side processing)
    return baseUrl
  },

  // Generate srcSet for responsive images
  generateSrcSet: (baseUrl: string, sizes: number[] = [320, 640, 768, 1024, 1280, 1920]) => {
    return sizes
      .map(size => `${imageOptimization.generateResponsiveImageUrl(baseUrl, size)} ${size}w`)
      .join(', ')
  },

  // Lazy loading intersection observer
  createLazyLoadObserver: (callback: (entries: IntersectionObserverEntry[]) => void) => {
    if (!('IntersectionObserver' in window)) {
      // Fallback for older browsers
      return null
    }

    return new IntersectionObserver(callback, {
      rootMargin: '50px 0px',
      threshold: 0.01
    })
  },

  // Preload critical images
  preloadImage: (src: string): Promise<void> => {
    return new Promise((resolve, reject) => {
      const img = new Image()
      img.onload = () => resolve()
      img.onerror = reject
      img.src = src
    })
  },

  // Preload multiple images
  preloadImages: async (urls: string[]): Promise<void[]> => {
    return Promise.all(urls.map(url => imageOptimization.preloadImage(url)))
  }
}

// Code splitting and lazy loading
export const codeSplitting = {
  // Dynamic import with error handling
  dynamicImport: async <T>(importFn: () => Promise<{ default: T }>): Promise<T> => {
    try {
      const module = await importFn()
      return module.default
    } catch (error) {
      console.error('Failed to load module:', error)
      throw error
    }
  },

  // Preload route components
  preloadRoute: (routeImport: () => Promise<any>) => {
    // Preload on hover or focus
    const preload = () => {
      routeImport().catch(console.error)
    }

    return {
      onMouseEnter: preload,
      onFocus: preload
    }
  }
}

// Bundle optimization
export const bundleOptimization = {
  // Check if feature is supported before loading polyfill
  loadPolyfillIfNeeded: async (feature: string, polyfillLoader: () => Promise<any>) => {
    if (!(feature in window)) {
      await polyfillLoader()
    }
  },

  // Load third-party scripts asynchronously
  loadScript: (src: string, async = true, defer = true): Promise<void> => {
    return new Promise((resolve, reject) => {
      const script = document.createElement('script')
      script.src = src
      script.async = async
      script.defer = defer
      script.onload = () => resolve()
      script.onerror = reject
      document.head.appendChild(script)
    })
  }
}

// Memory management
export const memoryManagement = {
  // Cleanup event listeners
  cleanupEventListeners: (element: Element, events: string[], handler: EventListener) => {
    events.forEach(event => {
      element.removeEventListener(event, handler)
    })
  },

  // Debounce function for performance
  debounce: <T extends (...args: any[]) => any>(
    func: T,
    wait: number,
    immediate = false
  ): ((...args: Parameters<T>) => void) => {
    let timeout: NodeJS.Timeout | null = null

    return (...args: Parameters<T>) => {
      const later = () => {
        timeout = null
        if (!immediate) func(...args)
      }

      const callNow = immediate && !timeout
      if (timeout) clearTimeout(timeout)
      timeout = setTimeout(later, wait)
      if (callNow) func(...args)
    }
  },

  // Throttle function for performance
  throttle: <T extends (...args: any[]) => any>(
    func: T,
    limit: number
  ): ((...args: Parameters<T>) => void) => {
    let inThrottle: boolean

    return (...args: Parameters<T>) => {
      if (!inThrottle) {
        func(...args)
        inThrottle = true
        setTimeout(() => (inThrottle = false), limit)
      }
    }
  }
}

// Core Web Vitals monitoring
export const webVitals = {
  // Measure Largest Contentful Paint (LCP)
  measureLCP: (callback: (value: number) => void) => {
    if ('PerformanceObserver' in window) {
      const observer = new PerformanceObserver((list) => {
        const entries = list.getEntries()
        const lastEntry = entries[entries.length - 1] as any
        callback(lastEntry.startTime)
      })
      observer.observe({ entryTypes: ['largest-contentful-paint'] })
    }
  },

  // Measure First Input Delay (FID)
  measureFID: (callback: (value: number) => void) => {
    if ('PerformanceObserver' in window) {
      const observer = new PerformanceObserver((list) => {
        const entries = list.getEntries()
        entries.forEach((entry: any) => {
          callback(entry.processingStart - entry.startTime)
        })
      })
      observer.observe({ entryTypes: ['first-input'] })
    }
  },

  // Measure Cumulative Layout Shift (CLS)
  measureCLS: (callback: (value: number) => void) => {
    if ('PerformanceObserver' in window) {
      let clsValue = 0
      const observer = new PerformanceObserver((list) => {
        const entries = list.getEntries()
        entries.forEach((entry: any) => {
          if (!entry.hadRecentInput) {
            clsValue += entry.value
          }
        })
        callback(clsValue)
      })
      observer.observe({ entryTypes: ['layout-shift'] })
    }
  },

  // Get navigation timing
  getNavigationTiming: () => {
    if ('performance' in window && 'getEntriesByType' in performance) {
      const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming
      return {
        dns: navigation.domainLookupEnd - navigation.domainLookupStart,
        tcp: navigation.connectEnd - navigation.connectStart,
        request: navigation.responseStart - navigation.requestStart,
        response: navigation.responseEnd - navigation.responseStart,
        dom: navigation.domContentLoadedEventEnd - navigation.responseEnd,
        load: navigation.loadEventEnd - navigation.loadEventStart,
        total: navigation.loadEventEnd - navigation.fetchStart
      }
    }
    return null
  }
}

// Resource optimization
export const resourceOptimization = {
  // Preload critical resources
  preloadResource: (href: string, as: string, type?: string) => {
    const link = document.createElement('link')
    link.rel = 'preload'
    link.href = href
    link.as = as
    if (type) link.type = type
    document.head.appendChild(link)
  },

  // Prefetch resources for next navigation
  prefetchResource: (href: string) => {
    const link = document.createElement('link')
    link.rel = 'prefetch'
    link.href = href
    document.head.appendChild(link)
  },

  // DNS prefetch for external domains
  dnsPrefetch: (domain: string) => {
    const link = document.createElement('link')
    link.rel = 'dns-prefetch'
    link.href = domain
    document.head.appendChild(link)
  },

  // Preconnect to external domains
  preconnect: (domain: string, crossorigin = false) => {
    const link = document.createElement('link')
    link.rel = 'preconnect'
    link.href = domain
    if (crossorigin) link.crossOrigin = 'anonymous'
    document.head.appendChild(link)
  }
}

// Performance monitoring
export const performanceMonitoring = {
  // Log performance metrics
  logPerformanceMetrics: () => {
    const timing = webVitals.getNavigationTiming()
    if (timing) {
      console.group('Performance Metrics')
      console.log('DNS Lookup:', `${timing.dns}ms`)
      console.log('TCP Connection:', `${timing.tcp}ms`)
      console.log('Request:', `${timing.request}ms`)
      console.log('Response:', `${timing.response}ms`)
      console.log('DOM Processing:', `${timing.dom}ms`)
      console.log('Load Event:', `${timing.load}ms`)
      console.log('Total Load Time:', `${timing.total}ms`)
      console.groupEnd()
    }
  },

  // Monitor Core Web Vitals
  monitorWebVitals: () => {
    webVitals.measureLCP((lcp) => {
      console.log('LCP:', `${lcp}ms`, lcp < 2500 ? '✅' : '❌')
    })

    webVitals.measureFID((fid) => {
      console.log('FID:', `${fid}ms`, fid < 100 ? '✅' : '❌')
    })

    webVitals.measureCLS((cls) => {
      console.log('CLS:', cls, cls < 0.1 ? '✅' : '❌')
    })
  },

  // Send metrics to analytics
  sendMetricsToAnalytics: (metrics: Record<string, number>) => {
    // Implementation would depend on analytics service
    if (typeof (window as any).gtag !== 'undefined') {
      Object.entries(metrics).forEach(([name, value]) => {
        (window as any).gtag('event', 'performance_metric', {
          metric_name: name,
          metric_value: Math.round(value)
        })
      })
    }
  }
}

// Service Worker utilities
export const serviceWorker = {
  // Register service worker
  register: async (swUrl: string) => {
    if ('serviceWorker' in navigator) {
      try {
        const registration = await navigator.serviceWorker.register(swUrl)
        console.log('SW registered: ', registration)
        return registration
      } catch (error) {
        console.log('SW registration failed: ', error)
        throw error
      }
    }
    throw new Error('Service Worker not supported')
  },

  // Update service worker
  update: async () => {
    if ('serviceWorker' in navigator) {
      const registration = await navigator.serviceWorker.ready
      return registration.update()
    }
  },

  // Skip waiting for new service worker
  skipWaiting: () => {
    if ('serviceWorker' in navigator && navigator.serviceWorker.controller) {
      navigator.serviceWorker.controller.postMessage({ type: 'SKIP_WAITING' })
    }
  }
}

// Export all utilities
export default {
  imageOptimization,
  codeSplitting,
  bundleOptimization,
  memoryManagement,
  webVitals,
  resourceOptimization,
  performanceMonitoring,
  serviceWorker
}
