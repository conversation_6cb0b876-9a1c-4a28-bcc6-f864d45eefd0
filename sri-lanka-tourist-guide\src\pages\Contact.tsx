import React, { useState } from 'react'
import { Helmet } from 'react-helmet-async'
import { motion } from 'framer-motion'
import {
  EnvelopeIcon,
  PhoneIcon,
  MapPinIcon,
  ClockIcon,
  ChatBubbleLeftRightIcon,
  QuestionMarkCircleIcon
} from '@heroicons/react/24/outline'
import AnimatedSection from '@/components/AnimatedSection'

const Contact: React.FC = () => {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    subject: '',
    message: ''
  })

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    // Handle form submission
    console.log('Form submitted:', formData)
    // Reset form
    setFormData({ name: '', email: '', subject: '', message: '' })
  }

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    })
  }

  const contactInfo = [
    {
      icon: EnvelopeIcon,
      title: 'Email Us',
      content: '<EMAIL>',
      description: 'Send us your questions anytime'
    },
    {
      icon: PhoneIcon,
      title: 'Call Us',
      content: '+94 11 123 4567',
      description: 'Available 9 AM - 6 PM (GMT+5:30)'
    },
    {
      icon: MapPinIcon,
      title: 'Visit Us',
      content: 'Colombo, Sri Lanka',
      description: 'Our main office location'
    },
    {
      icon: ClockIcon,
      title: 'Response Time',
      content: '24-48 hours',
      description: 'We respond to all inquiries quickly'
    }
  ]

  const faqs = [
    {
      question: 'Do you provide tour booking services?',
      answer: 'We provide comprehensive travel information and can connect you with trusted local tour operators.'
    },
    {
      question: 'Is the travel information up to date?',
      answer: 'Yes, we regularly update our content to ensure accuracy. However, always verify current conditions before travel.'
    },
    {
      question: 'Can you help with visa applications?',
      answer: 'We provide guidance on visa requirements, but you\'ll need to apply through official government channels.'
    },
    {
      question: 'Do you offer personalized itineraries?',
      answer: 'We can provide general recommendations. For detailed custom itineraries, we recommend consulting with local travel agents.'
    }
  ]

  return (
    <>
      <Helmet>
        <title>Contact - Sri Lanka Tourist Guide</title>
        <meta name="description" content="Get in touch with us for travel advice, assistance, and questions about visiting Sri Lanka." />
      </Helmet>

      {/* Hero Section */}
      <section className="bg-gradient-to-br from-blue-50 to-purple-50 section-padding">
        <div className="container-max">
          <AnimatedSection animation="fadeIn" className="text-center mb-12">
            <h1 className="text-4xl md:text-6xl font-bold text-gray-900 mb-6">
              Get in <span className="text-gradient">Touch</span>
            </h1>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Have questions about visiting Sri Lanka? We're here to help you plan
              the perfect trip to the Pearl of the Indian Ocean.
            </p>
          </AnimatedSection>
        </div>
      </section>

      {/* Contact Information */}
      <section className="section-padding bg-white">
        <div className="container-max">
          <AnimatedSection animation="fadeIn" className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">Contact Information</h2>
            <p className="text-gray-600 max-w-2xl mx-auto">
              Get in touch with us through any of these convenient methods. We're here to help make your Sri Lankan adventure unforgettable.
            </p>
          </AnimatedSection>

          <AnimatedSection animation="stagger" staggerDelay={0.2}>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-16">
              {contactInfo.map((info, index) => (
                <motion.div
                  key={index}
                  whileHover={{ y: -5 }}
                  className="card p-6 text-center"
                >
                  <div className="inline-flex items-center justify-center w-12 h-12 bg-primary-100 text-primary-600 rounded-lg mb-4">
                    <info.icon className="h-6 w-6" />
                  </div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">
                    {info.title}
                  </h3>
                  <p className="text-primary-600 font-medium mb-1">
                    {info.content}
                  </p>
                  <p className="text-sm text-gray-600">
                    {info.description}
                  </p>
                </motion.div>
              ))}
            </div>
          </AnimatedSection>

          {/* Contact Form and FAQ */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
            {/* Contact Form */}
            <AnimatedSection animation="slideLeft">
              <div className="card p-8">
                <div className="flex items-center mb-6">
                  <ChatBubbleLeftRightIcon className="h-6 w-6 text-primary-600 mr-2" />
                  <h2 className="text-2xl font-bold text-gray-900">Send us a Message</h2>
                </div>

                <form onSubmit={handleSubmit} className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-2">
                        Your Name
                      </label>
                      <input
                        type="text"
                        id="name"
                        name="name"
                        value={formData.name}
                        onChange={handleChange}
                        required
                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                        placeholder="Enter your name"
                      />
                    </div>
                    <div>
                      <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-2">
                        Email Address
                      </label>
                      <input
                        type="email"
                        id="email"
                        name="email"
                        value={formData.email}
                        onChange={handleChange}
                        required
                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                        placeholder="Enter your email"
                      />
                    </div>
                  </div>

                  <div>
                    <label htmlFor="subject" className="block text-sm font-medium text-gray-700 mb-2">
                      Subject
                    </label>
                    <select
                      id="subject"
                      name="subject"
                      value={formData.subject}
                      onChange={handleChange}
                      required
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                    >
                      <option value="">Select a subject</option>
                      <option value="general">General Inquiry</option>
                      <option value="travel-advice">Travel Advice</option>
                      <option value="destinations">Destination Information</option>
                      <option value="activities">Activities & Tours</option>
                      <option value="accommodation">Accommodation</option>
                      <option value="transportation">Transportation</option>
                      <option value="other">Other</option>
                    </select>
                  </div>

                  <div>
                    <label htmlFor="message" className="block text-sm font-medium text-gray-700 mb-2">
                      Message
                    </label>
                    <textarea
                      id="message"
                      name="message"
                      value={formData.message}
                      onChange={handleChange}
                      required
                      rows={6}
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent resize-none"
                      placeholder="Tell us how we can help you..."
                    />
                  </div>

                  <motion.button
                    type="submit"
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                    className="w-full bg-primary-600 hover:bg-primary-700 text-white font-medium py-3 px-6 rounded-lg transition-colors"
                  >
                    Send Message
                  </motion.button>
                </form>
              </div>
            </AnimatedSection>

            {/* FAQ Section */}
            <AnimatedSection animation="slideRight">
              <div className="card p-8">
                <div className="flex items-center mb-6">
                  <QuestionMarkCircleIcon className="h-6 w-6 text-primary-600 mr-2" />
                  <h2 className="text-2xl font-bold text-gray-900">Frequently Asked Questions</h2>
                </div>

                <div className="space-y-6">
                  {faqs.map((faq, index) => (
                    <motion.div
                      key={index}
                      initial={{ opacity: 0, y: 20 }}
                      whileInView={{ opacity: 1, y: 0 }}
                      transition={{ duration: 0.5, delay: index * 0.1 }}
                      viewport={{ once: true }}
                      className="border-b border-gray-200 pb-4 last:border-b-0"
                    >
                      <h3 className="text-lg font-semibold text-gray-900 mb-2">
                        {faq.question}
                      </h3>
                      <p className="text-gray-600 leading-relaxed">
                        {faq.answer}
                      </p>
                    </motion.div>
                  ))}
                </div>

                <div className="mt-8 p-4 bg-blue-50 rounded-lg">
                  <p className="text-sm text-blue-800">
                    <strong>Can't find what you're looking for?</strong> Send us a message
                    and we'll get back to you within 24-48 hours.
                  </p>
                </div>
              </div>
            </AnimatedSection>
          </div>
        </div>
      </section>

      {/* Call to Action */}
      <section className="section-padding bg-gradient-to-r from-primary-600 to-purple-600 text-white">
        <div className="container-max text-center">
          <AnimatedSection animation="fadeIn">
            <h2 className="text-3xl md:text-4xl font-bold mb-6">
              Ready to Explore Sri Lanka?
            </h2>
            <p className="text-xl mb-8 opacity-90">
              Let us help you plan the perfect Sri Lankan adventure.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                className="bg-white text-primary-600 hover:bg-gray-100 font-medium py-3 px-8 rounded-lg transition-colors"
              >
                Start Planning
              </motion.button>
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                className="border-2 border-white text-white hover:bg-white hover:text-primary-600 font-medium py-3 px-8 rounded-lg transition-colors"
              >
                Browse Destinations
              </motion.button>
            </div>
          </AnimatedSection>
        </div>
      </section>
    </>
  )
}

export default Contact
