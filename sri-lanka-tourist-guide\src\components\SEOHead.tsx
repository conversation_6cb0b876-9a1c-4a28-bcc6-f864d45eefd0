import React from 'react'
import { Helmet } from 'react-helmet-async'
import { Article } from '../data/articles'
import { 
  generateArticleSEO, 
  generateArticleStructuredData,
  generateBreadcrumbStructuredData,
  generateFAQStructuredData,
  generateTravelGuideStructuredData,
  generateWebsiteStructuredData,
  generateOrganizationStructuredData,
  seoConfig 
} from '../utils/seo'

interface SEOHeadProps {
  // Basic SEO props
  title?: string
  description?: string
  canonical?: string
  image?: string
  
  // Article-specific props
  article?: Article
  
  // Page-specific props
  type?: 'website' | 'article' | 'blog'
  breadcrumbs?: Array<{name: string, url: string}>
  faqs?: Array<{question: string, answer: string}>
  
  // Additional meta tags
  additionalMetaTags?: Array<{name: string, content: string}>
  noIndex?: boolean
  noFollow?: boolean
}

export const SEOHead: React.FC<SEOHeadProps> = ({
  title,
  description,
  canonical,
  image,
  article,
  type = 'website',
  breadcrumbs,
  faqs,
  additionalMetaTags = [],
  noIndex = false,
  noFollow = false
}) => {
  // Generate SEO data based on article or custom props
  const seoData = article ? generateArticleSEO(article) : {
    title: title || seoConfig.defaultTitle,
    description: description || seoConfig.defaultDescription,
    canonical: canonical || seoConfig.siteUrl,
    openGraph: {
      title: title || seoConfig.defaultTitle,
      description: description || seoConfig.defaultDescription,
      url: canonical || seoConfig.siteUrl,
      type: type as any,
      images: [
        {
          url: image || `${seoConfig.siteUrl}${seoConfig.defaultImage}`,
          width: 1200,
          height: 630,
          alt: title || seoConfig.defaultTitle
        }
      ]
    },
    twitter: {
      card: 'summary_large_image',
      site: seoConfig.twitterHandle,
      title: title || seoConfig.defaultTitle,
      description: description || seoConfig.defaultDescription,
      image: image || `${seoConfig.siteUrl}${seoConfig.defaultImage}`
    },
    additionalMetaTags: additionalMetaTags
  }

  // Generate structured data
  const structuredData = []

  // Always include website and organization data
  structuredData.push(generateWebsiteStructuredData())
  structuredData.push(generateOrganizationStructuredData())

  // Add article-specific structured data
  if (article) {
    structuredData.push(generateArticleStructuredData(article))
    
    const travelGuideData = generateTravelGuideStructuredData(article)
    if (travelGuideData) {
      structuredData.push(travelGuideData)
    }
  }

  // Add breadcrumb structured data
  if (breadcrumbs && breadcrumbs.length > 0) {
    structuredData.push(generateBreadcrumbStructuredData(breadcrumbs))
  }

  // Add FAQ structured data
  if (faqs && faqs.length > 0) {
    structuredData.push(generateFAQStructuredData(faqs))
  }

  // Robots meta tag
  const robotsContent = []
  if (noIndex) robotsContent.push('noindex')
  if (noFollow) robotsContent.push('nofollow')
  if (robotsContent.length === 0) robotsContent.push('index', 'follow')

  return (
    <Helmet>
      {/* Basic Meta Tags */}
      <title>{seoData.title}</title>
      <meta name="description" content={seoData.description} />
      <meta name="robots" content={robotsContent.join(', ')} />
      <link rel="canonical" href={seoData.canonical} />
      
      {/* Language and Locale */}
      <html lang={seoConfig.language} />
      <meta property="og:locale" content={seoConfig.locale} />
      
      {/* Open Graph Tags */}
      <meta property="og:type" content={seoData.openGraph.type} />
      <meta property="og:title" content={seoData.openGraph.title} />
      <meta property="og:description" content={seoData.openGraph.description} />
      <meta property="og:url" content={seoData.openGraph.url} />
      <meta property="og:site_name" content={seoConfig.siteName} />
      
      {/* Open Graph Images */}
      {seoData.openGraph.images.map((img, index) => (
        <React.Fragment key={index}>
          <meta property="og:image" content={img.url} />
          <meta property="og:image:width" content={img.width.toString()} />
          <meta property="og:image:height" content={img.height.toString()} />
          <meta property="og:image:alt" content={img.alt} />
        </React.Fragment>
      ))}
      
      {/* Article-specific Open Graph Tags */}
      {article && seoData.openGraph.article && (
        <>
          <meta property="article:published_time" content={seoData.openGraph.article.publishedTime} />
          <meta property="article:modified_time" content={seoData.openGraph.article.modifiedTime} />
          <meta property="article:section" content={seoData.openGraph.article.section} />
          {seoData.openGraph.article.authors.map((author, index) => (
            <meta key={index} property="article:author" content={author} />
          ))}
          {seoData.openGraph.article.tags.map((tag, index) => (
            <meta key={index} property="article:tag" content={tag} />
          ))}
        </>
      )}
      
      {/* Twitter Card Tags */}
      <meta name="twitter:card" content={seoData.twitter.card} />
      <meta name="twitter:site" content={seoData.twitter.site} />
      <meta name="twitter:title" content={seoData.twitter.title} />
      <meta name="twitter:description" content={seoData.twitter.description} />
      <meta name="twitter:image" content={seoData.twitter.image} />
      
      {/* Facebook App ID */}
      {seoConfig.facebookAppId && (
        <meta property="fb:app_id" content={seoConfig.facebookAppId} />
      )}
      
      {/* Additional Meta Tags */}
      {seoData.additionalMetaTags.map((tag, index) => (
        <meta key={index} name={tag.name} content={tag.content} />
      ))}
      
      {/* Structured Data */}
      {structuredData.map((data, index) => (
        <script key={index} type="application/ld+json">
          {JSON.stringify(data)}
        </script>
      ))}
      
      {/* Preconnect to external domains for performance */}
      <link rel="preconnect" href="https://fonts.googleapis.com" />
      <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />
      <link rel="preconnect" href="https://images.unsplash.com" />
      
      {/* DNS Prefetch for better performance */}
      <link rel="dns-prefetch" href="//www.google-analytics.com" />
      <link rel="dns-prefetch" href="//www.googletagmanager.com" />
      
      {/* Favicon and App Icons */}
      <link rel="icon" type="image/x-icon" href="/favicon.ico" />
      <link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png" />
      <link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png" />
      <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png" />
      <link rel="manifest" href="/site.webmanifest" />
      
      {/* Theme Color for Mobile Browsers */}
      <meta name="theme-color" content="#3b82f6" />
      <meta name="msapplication-TileColor" content="#3b82f6" />
      
      {/* Viewport Meta Tag */}
      <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=5.0" />
      
      {/* Security Headers */}
      <meta httpEquiv="X-Content-Type-Options" content="nosniff" />
      <meta httpEquiv="X-Frame-Options" content="DENY" />
      <meta httpEquiv="X-XSS-Protection" content="1; mode=block" />
      
      {/* Performance Hints */}
      <meta httpEquiv="x-dns-prefetch-control" content="on" />
      
      {/* Content Security Policy (basic) */}
      <meta 
        httpEquiv="Content-Security-Policy" 
        content="default-src 'self'; img-src 'self' data: https:; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://www.googletagmanager.com https://www.google-analytics.com; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; connect-src 'self' https://www.google-analytics.com;" 
      />
    </Helmet>
  )
}

// Specialized SEO components for different page types
export const ArticleSEO: React.FC<{ article: Article; breadcrumbs?: Array<{name: string, url: string}> }> = ({ 
  article, 
  breadcrumbs 
}) => (
  <SEOHead 
    article={article} 
    type="article" 
    breadcrumbs={breadcrumbs}
  />
)

export const PageSEO: React.FC<{
  title: string
  description: string
  canonical?: string
  image?: string
  breadcrumbs?: Array<{name: string, url: string}>
  faqs?: Array<{question: string, answer: string}>
}> = ({ title, description, canonical, image, breadcrumbs, faqs }) => (
  <SEOHead 
    title={title}
    description={description}
    canonical={canonical}
    image={image}
    breadcrumbs={breadcrumbs}
    faqs={faqs}
  />
)

export const BlogSEO: React.FC<{
  title?: string
  description?: string
  canonical?: string
}> = ({ title, description, canonical }) => (
  <SEOHead 
    title={title}
    description={description}
    canonical={canonical}
    type="blog"
  />
)

export default SEOHead
