import React, { useEffect, useRef } from 'react'
import { motion } from 'framer-motion'
import { gsap } from 'gsap'
import { ScrollTrigger } from 'gsap/ScrollTrigger'
import { Festival } from '@/data/culture'

gsap.registerPlugin(ScrollTrigger)

interface FestivalsCalendarProps {
  festivals: Festival[]
  className?: string
}

const FestivalsCalendar: React.FC<FestivalsCalendarProps> = ({ festivals, className = '' }) => {
  const calendarRef = useRef<HTMLDivElement>(null)
  const itemRefs = useRef<(HTMLDivElement | null)[]>([])

  useEffect(() => {
    if (!calendarRef.current) return

    const items = itemRefs.current.filter(Boolean)

    // Set initial states
    gsap.set(items, { opacity: 0, y: 30, scale: 0.95 })

    // Animate each festival card
    items.forEach((item, index) => {
      if (!item) return

      ScrollTrigger.create({
        trigger: item,
        start: 'top 85%',
        end: 'bottom 15%',
        onEnter: () => {
          gsap.to(item, {
            opacity: 1,
            y: 0,
            scale: 1,
            duration: 0.6,
            ease: 'power2.out',
            delay: index * 0.1
          })
        },
        onLeave: () => {
          gsap.to(item, {
            opacity: 0.8,
            scale: 0.98,
            duration: 0.3,
            ease: 'power2.out'
          })
        },
        onEnterBack: () => {
          gsap.to(item, {
            opacity: 1,
            scale: 1,
            duration: 0.3,
            ease: 'power2.out'
          })
        },
        onLeaveBack: () => {
          gsap.to(item, {
            opacity: 0,
            y: 30,
            scale: 0.95,
            duration: 0.3,
            ease: 'power2.out'
          })
        }
      })

      // Add hover animations
      const handleMouseEnter = () => {
        gsap.to(item, {
          scale: 1.02,
          y: -5,
          duration: 0.3,
          ease: 'power2.out'
        })
      }

      const handleMouseLeave = () => {
        gsap.to(item, {
          scale: 1,
          y: 0,
          duration: 0.3,
          ease: 'power2.out'
        })
      }

      item.addEventListener('mouseenter', handleMouseEnter)
      item.addEventListener('mouseleave', handleMouseLeave)

      return () => {
        item.removeEventListener('mouseenter', handleMouseEnter)
        item.removeEventListener('mouseleave', handleMouseLeave)
      }
    })

    // Cleanup ScrollTriggers
    return () => {
      ScrollTrigger.getAll().forEach(trigger => {
        if (items.includes(trigger.trigger as HTMLDivElement)) {
          trigger.kill()
        }
      })
    }
  }, [festivals])

  const getTypeColor = (type: Festival['type']) => {
    switch (type) {
      case 'Buddhist': return 'from-orange-400 to-amber-500'
      case 'Hindu': return 'from-purple-400 to-violet-500'
      case 'Cultural': return 'from-blue-400 to-indigo-500'
      case 'National': return 'from-green-400 to-emerald-500'
      case 'Literary': return 'from-teal-400 to-cyan-500'
      case 'Harvest': return 'from-yellow-400 to-orange-500'
      default: return 'from-gray-400 to-gray-500'
    }
  }

  const getTypeIcon = (type: Festival['type']) => {
    switch (type) {
      case 'Buddhist': return '☸️'
      case 'Hindu': return '🕉️'
      case 'Cultural': return '🎭'
      case 'National': return '🇱🇰'
      case 'Literary': return '📚'
      case 'Harvest': return '🌾'
      default: return '🎉'
    }
  }

  const getCrowdLevelColor = (level: Festival['practicalInfo']['crowdLevel']) => {
    switch (level) {
      case 'Low': return 'bg-green-100 text-green-700'
      case 'Medium': return 'bg-yellow-100 text-yellow-700'
      case 'High': return 'bg-orange-100 text-orange-700'
      case 'Very High': return 'bg-red-100 text-red-700'
      default: return 'bg-gray-100 text-gray-700'
    }
  }

  // Group festivals by month
  const festivalsByMonth = festivals.reduce((acc, festival) => {
    if (!acc[festival.month]) {
      acc[festival.month] = []
    }
    acc[festival.month].push(festival)
    return acc
  }, {} as Record<string, Festival[]>)

  return (
    <div ref={calendarRef} className={`${className}`}>
      <div className="space-y-8 md:space-y-12">
        {Object.entries(festivalsByMonth).map(([month, monthFestivals]) => (
          <div key={month} className="space-y-6">
            {/* Month Header */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="text-center"
            >
              <h3 className="text-2xl md:text-3xl font-bold text-gray-900 mb-2">
                {month} 2025
              </h3>
              <div className="w-24 h-1 bg-gradient-to-r from-orange-400 to-red-500 mx-auto rounded-full"></div>
            </motion.div>

            {/* Festivals Grid */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {monthFestivals.map((festival) => (
                <div
                  key={festival.id}
                  ref={el => itemRefs.current[festivals.indexOf(festival)] = el}
                  className="festival-card"
                >
                  <motion.div
                    className="bg-white rounded-xl shadow-lg border border-gray-100 overflow-hidden hover:shadow-xl transition-all duration-300 relative"
                    whileHover={{ y: -2 }}
                  >
                    {/* Festival Type Badge */}
                    <div className="absolute top-4 right-4 z-10">
                      <div className={`inline-flex items-center px-3 py-1 rounded-full bg-gradient-to-r ${getTypeColor(festival.type)} text-white text-xs font-semibold shadow-lg`}>
                        <span className="mr-1">{getTypeIcon(festival.type)}</span>
                        {festival.type}
                      </div>
                    </div>

                    {/* Background Gradient */}
                    <div className={`absolute inset-0 bg-gradient-to-br ${getTypeColor(festival.type)} opacity-5`} />

                    <div className="relative p-6">
                      {/* Date */}
                      <div className="mb-4">
                        <div className="text-2xl font-bold text-gray-900 mb-1">
                          {festival.date}
                        </div>
                        <div className="text-sm text-gray-600">
                          Duration: {festival.duration}
                        </div>
                      </div>

                      {/* Festival Name */}
                      <h4 className="text-xl font-bold text-gray-900 mb-3 leading-tight">
                        {festival.name}
                      </h4>

                      {/* Description */}
                      <p className="text-gray-600 text-sm mb-4 leading-relaxed line-clamp-3">
                        {festival.description}
                      </p>

                      {/* Location */}
                      <div className="mb-4">
                        <div className="flex items-center text-sm text-gray-600">
                          <span className="mr-2">📍</span>
                          {festival.location}
                        </div>
                      </div>

                      {/* Crowd Level */}
                      <div className="mb-4">
                        <span className={`inline-block px-2 py-1 rounded-full text-xs font-medium ${getCrowdLevelColor(festival.practicalInfo.crowdLevel)}`}>
                          Crowd Level: {festival.practicalInfo.crowdLevel}
                        </span>
                      </div>

                      {/* Highlights */}
                      <div className="mb-4">
                        <h5 className="text-sm font-semibold text-gray-900 mb-2">Highlights:</h5>
                        <ul className="space-y-1">
                          {festival.highlights.slice(0, 2).map((highlight, idx) => (
                            <li key={idx} className="text-xs text-gray-600 flex items-start">
                              <span className="w-1 h-1 bg-orange-400 rounded-full mt-1.5 mr-2 flex-shrink-0"></span>
                              {highlight}
                            </li>
                          ))}
                          {festival.highlights.length > 2 && (
                            <li className="text-xs text-gray-500 italic">
                              +{festival.highlights.length - 2} more highlights
                            </li>
                          )}
                        </ul>
                      </div>

                      {/* Practical Info */}
                      <div className="flex flex-wrap gap-2 text-xs">
                        {festival.practicalInfo.bookingRequired && (
                          <span className="px-2 py-1 bg-blue-100 text-blue-700 rounded-full">
                            Booking Required
                          </span>
                        )}
                        <span className="px-2 py-1 bg-gray-100 text-gray-700 rounded-full">
                          {festival.practicalInfo.photography}
                        </span>
                      </div>
                    </div>
                  </motion.div>
                </div>
              ))}
            </div>
          </div>
        ))}
      </div>
    </div>
  )
}

export default FestivalsCalendar
