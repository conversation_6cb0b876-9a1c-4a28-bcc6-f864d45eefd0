import React, { useState, useEffect, useRef } from 'react'
import { Link, useLocation } from 'react-router-dom'
import { motion } from 'framer-motion'
import { Bars3Icon, XMarkIcon } from '@heroicons/react/24/outline'
import { useKeyboardNavigation } from '../hooks/useAccessibility'

const Header: React.FC = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false)
  const [currentFocusIndex, setCurrentFocusIndex] = useState(-1)
  const location = useLocation()
  const mobileMenuRef = useRef<HTMLDivElement>(null)
  const mobileButtonRef = useRef<HTMLButtonElement>(null)
  // const { trapFocus, restoreFocus } = useFocusManagement()
  const { handleKeyboardNavigation } = useKeyboardNavigation()

  const navigation = [
    { name: 'Home', href: '/' },
    { name: 'Destinations', href: '/destinations' },
    { name: 'Activities', href: '/activities' },
    { name: 'Culture', href: '/culture' },
    { name: 'Articles', href: '/articles' },
    { name: 'Travel Tips', href: '/travel-tips' },
    { name: 'Contact', href: '/contact' },
  ]

  const isActive = (path: string) => location.pathname === path

  // Enhanced accessibility functions
  const toggleMobileMenu = () => {
    setIsMenuOpen(!isMenuOpen)
    setCurrentFocusIndex(-1)
  }

  const closeMobileMenu = () => {
    setIsMenuOpen(false)
    setCurrentFocusIndex(-1)
    // Return focus to menu button
    mobileButtonRef.current?.focus()
  }

  // Handle keyboard navigation in mobile menu
  const handleMobileMenuKeyDown = (e: React.KeyboardEvent) => {
    if (!isMenuOpen) return

    const menuItems = mobileMenuRef.current?.querySelectorAll('a') || []
    const menuItemsArray = Array.from(menuItems) as HTMLElement[]

    switch (e.key) {
      case 'Escape':
        e.preventDefault()
        closeMobileMenu()
        break
      case 'Tab':
        // Allow normal tab behavior but ensure focus stays within menu
        if (e.shiftKey && currentFocusIndex === 0) {
          e.preventDefault()
          mobileButtonRef.current?.focus()
        } else if (!e.shiftKey && currentFocusIndex === menuItemsArray.length - 1) {
          e.preventDefault()
          mobileButtonRef.current?.focus()
        }
        break
      case 'ArrowDown':
      case 'ArrowUp':
        e.preventDefault()
        handleKeyboardNavigation(e, menuItemsArray, currentFocusIndex, setCurrentFocusIndex)
        break
      case 'Home':
        e.preventDefault()
        setCurrentFocusIndex(0)
        menuItemsArray[0]?.focus()
        break
      case 'End':
        e.preventDefault()
        const lastIndex = menuItemsArray.length - 1
        setCurrentFocusIndex(lastIndex)
        menuItemsArray[lastIndex]?.focus()
        break
    }
  }

  // Handle clicks outside mobile menu
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (isMenuOpen &&
          mobileMenuRef.current &&
          !mobileMenuRef.current.contains(event.target as Node) &&
          !mobileButtonRef.current?.contains(event.target as Node)) {
        closeMobileMenu()
      }
    }

    if (isMenuOpen) {
      document.addEventListener('mousedown', handleClickOutside)
      // Focus first menu item when menu opens
      setTimeout(() => {
        const firstMenuItem = mobileMenuRef.current?.querySelector('a') as HTMLElement
        firstMenuItem?.focus()
        setCurrentFocusIndex(0)
      }, 100)
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [isMenuOpen])

  return (
    <>
      {/* Skip Link for Screen Readers */}
      <a
        href="#main-content"
        className="sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-2 focus:right-2 sm:focus:left-4 sm:focus:right-auto focus:z-50 focus:px-2 sm:focus:px-4 focus:py-2 focus:bg-primary-600 focus:text-white focus:rounded-md focus:shadow-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 focus:text-sm"
      >
        Skip to main content
      </a>

      <header className="bg-white shadow-lg sticky top-0 z-50" role="banner">
      <nav className="container-max">
        <div className="flex justify-between items-center py-4">
          {/* Logo */}
          <Link to="/" className="flex items-center space-x-2 min-h-[44px] py-2">
            <motion.div
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className="text-2xl font-bold text-gradient"
            >
              Sri Lanka Guide
            </motion.div>
          </Link>

          {/* Desktop Navigation */}
          <nav className="hidden md:flex space-x-4 lg:space-x-8" role="navigation" aria-label="Main navigation">
            {navigation.map((item) => (
              <Link
                key={item.name}
                to={item.href}
                className={`relative px-3 py-2 text-sm font-medium transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 rounded-md ${
                  isActive(item.href)
                    ? 'text-primary-600'
                    : 'text-gray-700 hover:text-primary-600'
                }`}
                aria-current={isActive(item.href) ? 'page' : undefined}
                tabIndex={0}
              >
                {item.name}
                {isActive(item.href) && (
                  <motion.div
                    layoutId="activeTab"
                    className="absolute bottom-0 left-0 right-0 h-0.5 bg-primary-600"
                    initial={false}
                    transition={{ type: "spring", stiffness: 500, damping: 30 }}
                    aria-hidden="true"
                  />
                )}
              </Link>
            ))}
          </nav>

          {/* Mobile menu button */}
          <div className="md:hidden">
            <button
              ref={mobileButtonRef}
              type="button"
              onClick={toggleMobileMenu}
              onKeyDown={(e) => {
                if (e.key === 'Enter' || e.key === ' ') {
                  e.preventDefault()
                  toggleMobileMenu()
                }
              }}
              className="p-3 rounded-md text-gray-700 hover:text-primary-600 hover:bg-gray-100 mobile-menu-button focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 min-h-[44px] min-w-[44px] flex items-center justify-center"
              aria-label={isMenuOpen ? "Close navigation menu" : "Open navigation menu"}
              aria-expanded={isMenuOpen ? "true" : "false"}
              aria-controls="mobile-menu"
              aria-haspopup="true"
            >
              <span className="sr-only">
                {isMenuOpen ? "Close navigation menu" : "Open navigation menu"}
              </span>
              {isMenuOpen ? (
                <XMarkIcon className="h-6 w-6" aria-hidden="true" />
              ) : (
                <Bars3Icon className="h-6 w-6" aria-hidden="true" />
              )}
            </button>
          </div>
        </div>

        {/* Mobile Navigation */}
        <motion.div
          ref={mobileMenuRef}
          id="mobile-menu"
          initial={{ opacity: 0, height: 0 }}
          animate={isMenuOpen ? { opacity: 1, height: 'auto' } : { opacity: 0, height: 0 }}
          className="mobile-menu md:hidden border-t border-gray-200 overflow-hidden"
          data-testid="mobile-menu"
          role="navigation"
          aria-label="Mobile navigation"
          aria-hidden={!isMenuOpen}
          onKeyDown={handleMobileMenuKeyDown}
          style={{ display: isMenuOpen ? 'block' : 'none' }}
        >
          <ul className="py-4 space-y-2">
            {navigation.map((item, index) => (
              <li key={item.name}>
                <Link
                  to={item.href}
                  onClick={closeMobileMenu}
                  onFocus={() => setCurrentFocusIndex(index)}
                  className={`block px-4 py-2 text-sm font-medium rounded-md transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 focus:ring-offset-white ${
                    isActive(item.href)
                      ? 'text-primary-600 bg-primary-50'
                      : 'text-gray-700 hover:text-primary-600 hover:bg-gray-100'
                  }`}
                  aria-current={isActive(item.href) ? 'page' : undefined}
                  tabIndex={isMenuOpen ? 0 : -1}
                >
                  {item.name}
                </Link>
              </li>
            ))}
          </ul>
        </motion.div>
      </nav>
    </header>
    </>
  )
}

export default Header
